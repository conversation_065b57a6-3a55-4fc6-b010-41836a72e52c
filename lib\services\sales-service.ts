/**
 * Sales Service - Handles fetching and processing discount/sales data
 */

export interface SalesApiResponse {
  data: SalesItem[]
}

export interface SalesItem {
  active: boolean
  id: number
  item: {
    inventoryType: 'CHAMPION_SKIN' | 'CHAMPION'
    itemId: number
  }
  sale: {
    endDate: string
    prices: Array<{
      cost: number
      currency: string
      discount: number
    }>
  }
}

export interface ProcessedDiscountItem {
  id: number
  type: 'CHAMPION_SKIN' | 'CHAMPION'
  itemId: number
  championId: number
  championName: string
  championSlug: string
  skinName?: string
  skinId?: number
  image: string
  originalPrice: number
  salePrice: number
  currency: string
  discount: number
  endDate: string
  active: boolean
}

interface SalesCache {
  data: ProcessedDiscountItem[]
  timestamp: number
}

interface CommunityDragonSkinsCache {
  data: any
  timestamp: number
}

interface ChampionCache {
  data: { [key: number]: { name: string; slug: string; image: string } }
  timestamp: number
}

class SalesService {
  private cache: SalesCache | null = null
  private championCache: ChampionCache | null = null
  private communityDragonSkinsCache: CommunityDragonSkinsCache | null = null
  private readonly CACHE_TTL = 1000 * 60 * 30 // 30 minutes (shorter for sales data)
  private readonly CHAMPION_CACHE_TTL = 1000 * 60 * 60 * 24 // 24 hours for champion data
  private readonly COMMUNITY_DRAGON_CACHE_TTL = 1000 * 60 * 60 * 24 // 24 hours for Community Dragon data
  private readonly SALES_API_URL = 'https://api.loldb.info/api/store/sales'
  private readonly SKINS_API_URL = 'https://api.loldb.info/api/store/skins'
  private readonly CHAMPIONS_API_URL = 'https://api.loldb.info/api/store/champions'

  /**
   * Get all active discount items
   */
  async getActiveDiscounts(): Promise<ProcessedDiscountItem[]> {
    // Check cache first
    if (this.cache && (Date.now() - this.cache.timestamp) < this.CACHE_TTL) {
      
      return this.cache.data
    }

    try {
      

      // Fetch directly from external API to avoid circular dependency
      const salesResponse = await fetch(this.SALES_API_URL)
      if (!salesResponse.ok) {
        throw new Error(`HTTP ${salesResponse.status}: Failed to fetch sales data`)
      }

      const salesResult = await salesResponse.json()
      if (!salesResult.success) {
        throw new Error(salesResult.error || 'Failed to fetch sales data')
      }

      const salesData = salesResult.data || []
      

      // Filter only active items
      const activeItems = salesData.filter((item: any) => item.active)
      

      // Fetch all skins data once for better performance (with timeout)
      let allSkinsData: any[] = []
      try {
        

        const skinsPromise = fetch(this.SKINS_API_URL)
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Skins API timeout')), 15000)
        )

        const skinsResponse = await Promise.race([skinsPromise, timeoutPromise]) as Response
        if (skinsResponse.ok) {
          const skinsResult = await skinsResponse.json()
          allSkinsData = skinsResult.data?.catalog || []
          
        }
      } catch (error) {
        console.warn('⚠️ SALES SERVICE: Failed to fetch skins data (using fallback):', error)
        // Continue without skin data - will use fallback names
      }

      // Fetch all champions data for original pricing
      let allChampionsData: any[] = []
      try {
        

        const championsPromise = fetch(this.CHAMPIONS_API_URL)
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Champions API timeout')), 15000)
        )

        const championsResponse = await Promise.race([championsPromise, timeoutPromise]) as Response
        if (championsResponse.ok) {
          const championsResult = await championsResponse.json()
          allChampionsData = championsResult.data?.catalog || []
          
        }
      } catch (error) {
        console.warn('⚠️ SALES SERVICE: Failed to fetch champions data (using fallback):', error)
        // Continue without champion data - will use fallback prices
      }

      // Pre-load champion data and Community Dragon skins data once for all items
      await this.loadChampionData()
      await this.loadCommunityDragonSkinsData()

      // Process each active item
      const processedItems: ProcessedDiscountItem[] = []

      for (const item of activeItems) {
        try {
          const processedItem = await this.processDiscountItem(item, allSkinsData, allChampionsData)
          if (processedItem) {
            processedItems.push(processedItem)
          }
        } catch (error) {
          console.warn(`⚠️ SALES SERVICE: Failed to process item ${item.id}:`, error)
        }
      }

      

      // Cache the processed data
      this.cache = {
        data: processedItems,
        timestamp: Date.now()
      }

      return processedItems

    } catch (error) {
      console.error('❌ SALES SERVICE: Failed to fetch discount data:', error)

      // Return cached data if available, even if expired
      if (this.cache) {
        console.warn('⚠️ SALES SERVICE: Using expired cache due to API failure')
        return this.cache.data
      }

      // Return empty array as fallback
      return []
    }
  }

  /**
   * Process a single discount item
   */
  private async processDiscountItem(item: SalesItem, allSkinsData: any[] = [], allChampionsData: any[] = []): Promise<ProcessedDiscountItem | null> {
    try {
      // Extract champion ID from item ID
      const championId = this.extractChampionId(item.item.itemId)
      
      // Get champion data
      const championData = await this.getChampionData(championId)
      if (!championData) {
        console.warn(`⚠️ SALES SERVICE: No champion data found for ID ${championId}`)
        return null
      }

      // Get pricing info
      const priceInfo = item.sale.prices[0] // Use first price entry
      if (!priceInfo) {
        console.warn(`⚠️ SALES SERVICE: No price info found for item ${item.id}`)
        return null
      }

      let processedItem: ProcessedDiscountItem = {
        id: item.id,
        type: item.item.inventoryType,
        itemId: item.item.itemId,
        championId,
        championName: championData.name,
        championSlug: championData.slug,
        image: championData.image,
        originalPrice: 0, // Will be calculated
        salePrice: priceInfo.cost,
        currency: priceInfo.currency,
        discount: 0, // Will be calculated
        endDate: item.sale.endDate,
        active: item.active
      }

      // Handle skin-specific data
      if (item.item.inventoryType === 'CHAMPION_SKIN') {
        const skinData = this.findSkinInData(item.item.itemId, allSkinsData)
        if (skinData) {
          processedItem.skinName = skinData.name
          processedItem.skinId = item.item.itemId
          // Use Community Dragon splash image for skins
          processedItem.image = await this.generateSkinImageUrl(championData.slug, item.item.itemId)
          processedItem.originalPrice = skinData.rp || this.getDefaultSkinPrice(priceInfo.cost)
          // Use actual percentOff from API if available
          processedItem.discount = skinData.sale?.percentOff || Math.round(((processedItem.originalPrice - processedItem.salePrice) / processedItem.originalPrice) * 100)
        } else {
          // Try to get skin name from Community Dragon API as fallback
          const communityDragonSkinName = await this.getSkinNameFromCommunityDragon(item.item.itemId, championId)

          if (communityDragonSkinName) {
            processedItem.skinName = communityDragonSkinName
          } else {
            // Final fallback for skins without data
            processedItem.skinName = `${championData.name} Skin`
            console.warn(`⚠️ SALES SERVICE: Using generic fallback name for skin ${item.item.itemId}`)
          }

          processedItem.skinId = item.item.itemId
          processedItem.image = await this.generateSkinImageUrl(championData.slug, item.item.itemId)
          processedItem.originalPrice = this.getDefaultSkinPrice(priceInfo.cost)
          // Calculate discount percentage as fallback
          processedItem.discount = Math.round(((processedItem.originalPrice - processedItem.salePrice) / processedItem.originalPrice) * 100)
        }
      } else {
        // Champion purchase - get original price from champions API and update image
        const championData = allChampionsData.find(champ => champ.itemId === item.item.itemId && champ.inventoryType === 'CHAMPION')
        if (championData) {
          processedItem.originalPrice = championData.rp || this.getDefaultChampionPrice(priceInfo.cost)
          // Use actual percentOff from API if available
          processedItem.discount = championData.sale?.percentOff || Math.round(((processedItem.originalPrice - processedItem.salePrice) / processedItem.originalPrice) * 100)
        } else {
          // Fallback to default pricing
          processedItem.originalPrice = this.getDefaultChampionPrice(priceInfo.cost)
          processedItem.discount = Math.round(((processedItem.originalPrice - processedItem.salePrice) / processedItem.originalPrice) * 100)
        }

        // Update champion image to use Community Dragon base skin splash
        processedItem.image = await this.generateChampionImageUrl(championData.slug, item.item.itemId)
      }

      return processedItem

    } catch (error) {
      console.error(`❌ SALES SERVICE: Error processing discount item ${item.id}:`, error)
      return null
    }
  }

  /**
   * Find skin data in the pre-loaded skins array
   */
  private findSkinInData(itemId: number, allSkinsData: any[]): { name: string; iconUrl?: string; rp?: number; sale?: { percentOff: number } } | null {
    const skin = allSkinsData.find((s: any) => s.itemId === itemId && s.inventoryType === 'CHAMPION_SKIN')
    if (skin) {
      return {
        name: skin.name,
        iconUrl: skin.iconUrl || null,
        rp: skin.rp,
        sale: skin.sale || null
      }
    }
    return null
  }

  /**
   * Get skin name from Community Dragon API as fallback
   */
  private async getSkinNameFromCommunityDragon(itemId: number, championId: number): Promise<string | null> {
    try {
      // Use cached Community Dragon skins data
      if (!this.communityDragonSkinsCache?.data) {
        console.warn(`⚠️ SALES SERVICE: No Community Dragon skins data available`)
        return null
      }

      const skinsData = this.communityDragonSkinsCache.data

      // Find the skin by ID
      const skin = skinsData[itemId.toString()]
      if (skin && !skin.isBase) {
        return skin.name
      }

      return null
    } catch (error) {
      console.warn(`⚠️ SALES SERVICE: Failed to get skin name from Community Dragon for item ${itemId}:`, error)
      return null
    }
  }

  /**
   * Extract champion ID from item ID using the specified rules
   */
  private extractChampionId(itemId: number): number {
    const itemIdStr = itemId.toString()
    
    if (itemIdStr.length === 4) {
      // 4-digit IDs: use first digit
      return parseInt(itemIdStr.charAt(0))
    } else if (itemIdStr.length === 5) {
      // 5-digit IDs: use first 2 digits if 3rd is 0, otherwise first 3 digits
      const thirdDigit = itemIdStr.charAt(2)
      if (thirdDigit === '0') {
        return parseInt(itemIdStr.substring(0, 2))
      } else {
        return parseInt(itemIdStr.substring(0, 3))
      }
    } else {
      // For other lengths, try first 2-3 digits
      return parseInt(itemIdStr.substring(0, Math.min(3, itemIdStr.length)))
    }
  }

  /**
   * Load and cache all champion data from DDragon
   */
  private async loadChampionData(): Promise<void> {
    // Check if champion cache is still valid
    if (this.championCache && (Date.now() - this.championCache.timestamp) < this.CHAMPION_CACHE_TTL) {
      
      return
    }

    try {
      

      // Fetch all champions from DDragon
      const versionsResponse = await fetch('https://ddragon.leagueoflegends.com/api/versions.json')
      const versions = await versionsResponse.json()
      const currentVersion = versions[0]

      const url = `https://ddragon.leagueoflegends.com/cdn/${currentVersion}/data/en_US/champion.json`
      const response = await fetch(url)
      const data = await response.json()
      const championsData = data.data

      // Build champion lookup by key (numeric ID)
      const championLookup: { [key: number]: { name: string; slug: string; image: string } } = {}

      Object.values(championsData).forEach((champ: any) => {
        const championId = parseInt(champ.key)
        championLookup[championId] = {
          name: champ.name,
          slug: champ.id.toLowerCase(),
          image: `https://ddragon.leagueoflegends.com/cdn/img/champion/loading/${champ.id}_0.jpg`
        }
      })

      // Cache the champion data
      this.championCache = {
        data: championLookup,
        timestamp: Date.now()
      }

      
    } catch (error) {
      console.warn('⚠️ SALES SERVICE: Failed to load champion data:', error)
      // Keep existing cache if available
    }
  }

  /**
   * Get champion data by ID (numeric key) from cache
   */
  private async getChampionData(championId: number): Promise<{ name: string; slug: string; image: string } | null> {
    // Use cached data if available
    if (this.championCache?.data[championId]) {
      return this.championCache.data[championId]
    }

    // Fallback: return basic data
    console.warn(`⚠️ SALES SERVICE: Champion with key ${championId} not found in cache`)
    return {
      name: `Champion ${championId}`,
      slug: `champion-${championId}`,
      image: '/placeholder.svg'
    }
  }

  /**
   * Load and cache Community Dragon skins data
   */
  private async loadCommunityDragonSkinsData(): Promise<void> {
    // Check if cache is still valid
    if (this.communityDragonSkinsCache && (Date.now() - this.communityDragonSkinsCache.timestamp) < this.COMMUNITY_DRAGON_CACHE_TTL) {
      return
    }

    try {
      const response = await fetch('https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/skins.json')
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: Failed to fetch Community Dragon skins data`)
      }

      const skinsData = await response.json()

      // Cache the data
      this.communityDragonSkinsCache = {
        data: skinsData,
        timestamp: Date.now()
      }

    } catch (error) {
      console.warn('⚠️ SALES SERVICE: Failed to load Community Dragon skins data:', error)
      // Keep existing cache if available
    }
  }

  /**
   * Get skin data by item ID
   */
  private async getSkinData(itemId: number): Promise<{ name: string; id: number; image: string; originalPrice?: number } | null> {
    try {
      // Fetch from skins store API
      const response = await fetch(this.SKINS_API_URL)
      if (response.ok) {
        const data = await response.json()
        const skin = data.data?.find((s: any) => s.id === itemId)
        if (skin) {
          return {
            name: skin.name,
            id: skin.id,
            image: skin.image || '/placeholder.svg',
            originalPrice: skin.rp
          }
        }
      }
      return null
    } catch (error) {
      console.warn(`⚠️ SALES SERVICE: Failed to get skin data for item ID ${itemId}:`, error)
      return null
    }
  }

  /**
   * Get default skin price based on sale price (estimate original)
   */
  private getDefaultSkinPrice(salePrice: number): number {
    // Common skin price tiers and their typical discounts
    if (salePrice <= 300) return 520    // Regular skins ~40% off
    if (salePrice <= 500) return 750    // Regular skins ~33% off
    if (salePrice <= 700) return 975    // Epic skins ~30% off
    if (salePrice <= 900) return 1350   // Epic skins ~33% off
    if (salePrice <= 1200) return 1820  // Legendary skins ~33% off
    return Math.round(salePrice * 1.5)  // Default 33% discount assumption
  }

  /**
   * Get default champion price based on sale price (estimate original)
   */
  private getDefaultChampionPrice(salePrice: number): number {
    // Common champion price tiers
    if (salePrice <= 300) return 450    // 1350 BE champions
    if (salePrice <= 400) return 585    // 3150 BE champions
    if (salePrice <= 500) return 790    // 4800 BE champions
    return 975  // 6300 BE champions (most expensive)
  }

  /**
   * Generate champion image URL using Community Dragon base skin splash images
   * For champions, we append "000" to the itemId to find the base skin
   */
  private async generateChampionImageUrl(championSlug: string, championItemId: number): Promise<string> {
    try {
      // Use cached Community Dragon skins data
      if (!this.communityDragonSkinsCache?.data) {
        console.warn(`⚠️ SALES SERVICE: No Community Dragon skins data available`)
        return this.generateFallbackChampionImageUrl(championSlug, championItemId)
      }

      const skinsData = this.communityDragonSkinsCache.data

      // For champions, append "000" to find the base skin
      const baseSkinId = `${championItemId}000`
      const skinData = skinsData[baseSkinId]

      // Handle tiered skins (like K/DA ALL OUT Seraphine) by checking for tiers
      let splashPath: string
      if (skinData.questSkinInfo?.tiers && skinData.questSkinInfo.tiers.length > 0) {
        // For tiered skins, use the first tier's splash path
        const firstTier = skinData.questSkinInfo.tiers[0]
        splashPath = firstTier.splashPath || firstTier.uncenteredSplashPath
      } else {
        // For regular skins, use the root level path
        splashPath = skinData.splashPath
      }

      if (!skinData || !splashPath) {
        console.warn(`⚠️ SALES SERVICE: No splash path found for champion base skin ${baseSkinId}`)
        return this.generateFallbackChampionImageUrl(championSlug, championItemId)
      }

      // Extract the path after /ASSETS/ and convert to lowercase
      const assetsIndex = splashPath.indexOf('/ASSETS/')
      if (assetsIndex === -1) {
        console.warn(`⚠️ SALES SERVICE: Invalid splash path format for champion ${baseSkinId}: ${splashPath}`)
        return this.generateFallbackChampionImageUrl(championSlug, championItemId)
      }

      // Extract the part after /ASSETS/ and convert to lowercase
      const pathAfterAssets = splashPath.substring(assetsIndex + 1) // +1 to remove the leading slash
      const lowercasePath = pathAfterAssets.toLowerCase()

      // Construct the final Community Dragon URL
      const baseUrl = 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default'
      const finalUrl = `${baseUrl}/${lowercasePath}`

      return finalUrl

    } catch (error) {
      console.error(`❌ SALES SERVICE: Error generating champion image URL for ${championItemId}:`, error)
      return this.generateFallbackChampionImageUrl(championSlug, championItemId)
    }
  }

  /**
   * Generate skin image URL using Community Dragon splash images
   */
  private async generateSkinImageUrl(championSlug: string, itemId: number): Promise<string> {
    try {
      // Use cached Community Dragon skins data
      if (!this.communityDragonSkinsCache?.data) {
        console.warn(`⚠️ SALES SERVICE: No Community Dragon skins data available`)
        return this.generateFallbackImageUrl(championSlug, itemId)
      }

      const skinsData = this.communityDragonSkinsCache.data

      // Find the skin by itemId
      const skinData = skinsData[itemId.toString()]

      // Handle tiered skins (like K/DA ALL OUT Seraphine) by checking for tiers
      let splashPath: string
      if (skinData?.questSkinInfo?.tiers && skinData.questSkinInfo.tiers.length > 0) {
        // For tiered skins, use the first tier's splash path
        const firstTier = skinData.questSkinInfo.tiers[0]
        splashPath = firstTier.splashPath || firstTier.uncenteredSplashPath
      } else {
        // For regular skins, use the root level path
        splashPath = skinData?.splashPath
      }

      if (!skinData || !splashPath) {
        console.warn(`⚠️ SALES SERVICE: No splash path found for skin ${itemId}`)
        return this.generateFallbackImageUrl(championSlug, itemId)
      }

      // Extract the path after /ASSETS/ and convert to lowercase
      const assetsIndex = splashPath.indexOf('/ASSETS/')
      if (assetsIndex === -1) {
        console.warn(`⚠️ SALES SERVICE: Invalid splash path format for skin ${itemId}: ${splashPath}`)
        return this.generateFallbackImageUrl(championSlug, itemId)
      }

      // Extract the part after /ASSETS/ and convert to lowercase
      const pathAfterAssets = splashPath.substring(assetsIndex + 1) // +1 to remove the leading slash
      const lowercasePath = pathAfterAssets.toLowerCase()

      // Construct the final Community Dragon URL
      const baseUrl = 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default'
      const finalUrl = `${baseUrl}/${lowercasePath}`

      return finalUrl

    } catch (error) {
      console.error(`❌ SALES SERVICE: Error generating skin image URL for ${itemId}:`, error)
      return this.generateFallbackImageUrl(championSlug, itemId)
    }
  }

  /**
   * Generate fallback champion image URL using DDragon loading screen
   */
  private generateFallbackChampionImageUrl(championSlug: string, championItemId: number): string {
    // For champions, use skin number 0 (base skin)
    return `https://ddragon.leagueoflegends.com/cdn/img/champion/loading/${championSlug.charAt(0).toUpperCase() + championSlug.slice(1)}_0.jpg`
  }

  /**
   * Generate fallback image URL using DDragon loading screen
   */
  private generateFallbackImageUrl(championSlug: string, itemId: number): string {
    // Extract skin number from item ID (last 2-3 digits)
    const itemIdStr = itemId.toString()
    let skinNumber = '0'

    if (itemIdStr.length >= 4) {
      // For most skins, the skin number is the last 2-3 digits
      const championIdLength = this.extractChampionId(itemId).toString().length
      skinNumber = itemIdStr.substring(championIdLength)
    }

    // Remove leading zeros but keep at least one digit
    skinNumber = skinNumber.replace(/^0+/, '') || '0'

    // Generate Community Dragon loading screen URL as fallback
    return `https://ddragon.leagueoflegends.com/cdn/img/champion/loading/${championSlug.charAt(0).toUpperCase() + championSlug.slice(1)}_${skinNumber}.jpg`
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.cache = null
    this.championCache = null
    this.communityDragonSkinsCache = null

  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    return {
      cached: !!this.cache,
      timestamp: this.cache?.timestamp || null,
      itemCount: this.cache?.data.length || 0,
      age: this.cache ? Date.now() - this.cache.timestamp : 0,
      ttl: this.CACHE_TTL
    }
  }
}

// Export singleton instance
export const salesService = new SalesService()
