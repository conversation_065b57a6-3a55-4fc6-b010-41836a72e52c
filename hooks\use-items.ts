// React hooks for items data fetching

import { useCallback, useEffect, useState, useMemo } from 'react'

interface ItemData {
  id: string
  name: string
  description: string
  plaintext: string
  image: string
  category: string
  formattedPrice: string
  sellPrice: string
  components: string[]
  buildsInto: string[]
  isPurchasable: boolean
  tags: string[]
  gold: {
    base: number
    total: number
    sell: number
    purchasable: boolean
  }
  stats: Record<string, number>
  mainStats: string[]
  isFullItem: boolean
  maps: Record<string, boolean>
  availability: 'Available' | 'Legacy' | 'Removed'
}

interface UseItemsResult {
  items: ItemData[]
  tags: string[]
  mainStats: string[]
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
  refresh: () => Promise<void>
}

type SortField = 'name' | 'price'
type SortDirection = 'asc' | 'desc'

interface UseItemFiltersResult {
  filteredItems: ItemData[]
  filters: ItemFilters
  updateFilters: (newFilters: Partial<ItemFilters>) => void
  resetFilters: () => void
  removeFilter: (filterType: string, value: string) => void
  clearCategory: (category: string) => void
  isSearching: boolean
}

// Hook for fetching all items
export function useItems(): UseItemsResult {
  const [items, setItems] = useState<ItemData[]>([])
  const [tags, setTags] = useState<string[]>([])
  const [mainStats, setMainStats] = useState<string[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchItems = useCallback(async (forceRefresh = false) => {
    try {
      setLoading(true)
      setError(null)
      
      const url = forceRefresh ? '/api/items?refresh=true' : '/api/items'
      const response = await fetch(url)
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch items')
      }
      
      setItems(result.data)
      setTags(result.tags || [])
      setMainStats(result.mainStats || [])
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
      setError(errorMessage)
      console.error('Error fetching items:', err)
    } finally {
      setLoading(false)
    }
  }, [])

  const refetch = useCallback(() => fetchItems(false), [fetchItems])
  const refresh = useCallback(() => fetchItems(true), [fetchItems])

  useEffect(() => {
    fetchItems()
  }, [fetchItems])

  return {
    items,
    tags,
    mainStats,
    loading,
    error,
    refetch,
    refresh
  }
}

// Types for item filters
export interface ItemFilters {
  search: string
  priceRange: [number, number]
  tags: string[]
  mainStats: string[]
  sortField: SortField
  sortDirection: SortDirection
}

interface ItemFiltersInternal extends ItemFilters {
  debouncedSearch: string
  debouncedPriceRange: [number, number]
}

// Hook for filtering and searching items
export function useItemFilters(items: ItemData[]): UseItemFiltersResult {
  const [filteredItems, setFilteredItems] = useState<ItemData[]>(items)
  const [filters, setFilters] = useState<ItemFiltersInternal>({
    search: '',
    priceRange: [0, 5000],
    tags: [],
    mainStats: [],
    sortField: 'name',
    sortDirection: 'asc',
    debouncedSearch: '',
    debouncedPriceRange: [0, 5000]
  })

  // Debounce search term with 300ms delay
  useEffect(() => {
    const timer = setTimeout(() => {
      setFilters(prev => ({ ...prev, debouncedSearch: prev.search }))
    }, 300)

    return () => clearTimeout(timer)
  }, [filters.search])

  // Debounce price range with 300ms delay
  useEffect(() => {
    const timer = setTimeout(() => {
      setFilters(prev => ({ ...prev, debouncedPriceRange: prev.priceRange }))
    }, 300)

    return () => clearTimeout(timer)
  }, [filters.priceRange])

  useEffect(() => {
    let result = [...items]

    // Filter by search term - use debounced search
    if (filters.debouncedSearch.trim()) {
      const searchTerm = filters.debouncedSearch.toLowerCase().trim()
      result = result.filter(item =>
        item.name.toLowerCase().includes(searchTerm) ||
        item.plaintext.toLowerCase().includes(searchTerm) ||
        item.tags.some(tag => tag.toLowerCase().includes(searchTerm))
      )
    }

    // Filter by price range - use debounced price range
    result = result.filter(item =>
      item.gold.total >= filters.debouncedPriceRange[0] &&
      item.gold.total <= filters.debouncedPriceRange[1]
    )

    // Filter by tags
    if (filters.tags.length > 0) {
      result = result.filter(item =>
        filters.tags.some(tag => item.tags.includes(tag))
      )
    }

    // Filter by main stats
    if (filters.mainStats.length > 0) {
      result = result.filter(item =>
        filters.mainStats.some(stat => item.mainStats.includes(stat))
      )
    }

    // Sort the results
    result.sort((a, b) => {
      let comparison = 0

      switch (filters.sortField) {
        case 'name':
          comparison = a.name.localeCompare(b.name)
          break
        case 'price':
          comparison = a.gold.total - b.gold.total
          break
        default:
          comparison = 0
      }

      return filters.sortDirection === 'desc' ? -comparison : comparison
    })

    setFilteredItems(result)
  }, [items, filters.debouncedSearch, filters.debouncedPriceRange, filters.tags, filters.mainStats, filters.sortField, filters.sortDirection])

  const updateFilters = useCallback((newFilters: Partial<ItemFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }))
  }, [])

  const resetFilters = useCallback(() => {
    setFilters({
      search: '',
      priceRange: [0, 5000],
      tags: [],
      mainStats: [],
      sortField: 'name',
      sortDirection: 'asc',
      debouncedSearch: '',
      debouncedPriceRange: [0, 5000]
    })
  }, [])

  const removeFilter = useCallback((filterType: string, value: string) => {
    switch (filterType) {
      case 'search':
        setFilters(prev => ({ ...prev, search: '' }))
        break
      case 'tags':
        setFilters(prev => ({
          ...prev,
          tags: prev.tags.filter(tag => tag !== value)
        }))
        break
      case 'mainStats':
        setFilters(prev => ({
          ...prev,
          mainStats: prev.mainStats.filter(stat => stat !== value)
        }))
        break
      case 'priceRange':
        setFilters(prev => ({ ...prev, priceRange: [0, 5000] }))
        break
    }
  }, [])

  const clearCategory = useCallback((category: string) => {
    switch (category) {
      case 'tags':
        setFilters(prev => ({ ...prev, tags: [] }))
        break
      case 'mainStats':
        setFilters(prev => ({ ...prev, mainStats: [] }))
        break
    }
  }, [])

  // Create public filters interface (without internal debounced fields)
  const publicFilters: ItemFilters = {
    search: filters.search,
    priceRange: filters.priceRange,
    tags: filters.tags,
    mainStats: filters.mainStats,
    sortField: filters.sortField,
    sortDirection: filters.sortDirection
  }

  return {
    filteredItems,
    filters: publicFilters,
    updateFilters,
    resetFilters,
    removeFilter,
    clearCategory,
    isSearching: filters.search !== filters.debouncedSearch ||
                 filters.priceRange[0] !== filters.debouncedPriceRange[0] ||
                 filters.priceRange[1] !== filters.debouncedPriceRange[1]
  }
}

// Hook for item statistics
export function useItemStats(items: ItemData[]) {
  const [stats, setStats] = useState({
    total: 0,
    byCategory: {} as Record<string, number>,
    byPriceRange: {} as Record<string, number>,
    averagePrice: 0,
    mostExpensive: null as ItemData | null,
    cheapest: null as ItemData | null
  })

  useEffect(() => {
    if (items.length === 0) return

    const categoryCount = items.reduce((acc, item) => {
      acc[item.category] = (acc[item.category] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    const priceRangeCount = items.reduce((acc, item) => {
      const price = item.gold.total
      const range = price === 0 ? 'Free' :
                   price <= 1000 ? '0-1000g' :
                   price <= 2000 ? '1000-2000g' :
                   price <= 3000 ? '2000-3000g' :
                   price <= 4000 ? '3000-4000g' : '4000g+'
      acc[range] = (acc[range] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    const purchasableItems = items.filter(item => item.isPurchasable && item.gold.total > 0)
    const averagePrice = purchasableItems.length > 0 
      ? Math.round(purchasableItems.reduce((sum, item) => sum + item.gold.total, 0) / purchasableItems.length)
      : 0

    const mostExpensive = purchasableItems.reduce((max, item) => 
      !max || item.gold.total > max.gold.total ? item : max, null as ItemData | null)

    const cheapest = purchasableItems.reduce((min, item) => 
      !min || item.gold.total < min.gold.total ? item : min, null as ItemData | null)

    setStats({
      total: items.length,
      byCategory: categoryCount,
      byPriceRange: priceRangeCount,
      averagePrice,
      mostExpensive,
      cheapest
    })
  }, [items])

  return stats
}

// Hook for item recommendations based on category or stats
export function useItemRecommendations(items: ItemData[], category?: string, maxItems = 6) {
  const [recommendations, setRecommendations] = useState<ItemData[]>([])

  useEffect(() => {
    if (items.length === 0) return

    let filtered = items.filter(item => item.isPurchasable)

    if (category && category !== 'All') {
      filtered = filtered.filter(item => item.category === category)
    }

    // Sort by popularity (using price as a proxy for now)
    // In a real implementation, you might use actual usage statistics
    const sorted = filtered.sort((a, b) => {
      // Prefer items in certain price ranges (not too cheap, not too expensive)
      const getPopularityScore = (item: ItemData) => {
        const price = item.gold.total
        if (price >= 1000 && price <= 3000) return 3
        if (price >= 500 && price <= 4000) return 2
        return 1
      }
      
      return getPopularityScore(b) - getPopularityScore(a)
    })

    setRecommendations(sorted.slice(0, maxItems))
  }, [items, category, maxItems])

  return recommendations
}

// Hook for searching items with debouncing
export function useItemSearch(items: ItemData[], delay = 300) {
  const [searchTerm, setSearchTerm] = useState('')
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('')
  const [searchResults, setSearchResults] = useState<ItemData[]>([])

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm)
    }, delay)

    return () => clearTimeout(timer)
  }, [searchTerm, delay])

  // Perform search when debounced term changes
  useEffect(() => {
    if (!debouncedSearchTerm.trim()) {
      setSearchResults([])
      return
    }

    const searchLower = debouncedSearchTerm.toLowerCase()
    const results = items.filter(item =>
      item.name.toLowerCase().includes(searchLower) ||
      item.plaintext.toLowerCase().includes(searchLower) ||
      item.tags.some(tag => tag.toLowerCase().includes(searchLower))
    )

    setSearchResults(results.slice(0, 10)) // Limit to 10 results
  }, [items, debouncedSearchTerm])

  return {
    searchTerm,
    setSearchTerm,
    searchResults,
    isSearching: searchTerm !== debouncedSearchTerm
  }
}
