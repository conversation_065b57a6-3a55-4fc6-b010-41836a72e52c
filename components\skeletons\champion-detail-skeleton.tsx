"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { useEffect, useState, useCallback } from "react"
import { useIsMobile } from "@/hooks/use-mobile"

interface ChampionPageSkeletonProps {
  backgroundImage?: string
}

export function ChampionPageSkeleton({ backgroundImage }: ChampionPageSkeletonProps = {}) {
  const [screenWidth, setScreenWidth] = useState(0)
  const [isClient, setIsClient] = useState(false)
  const [isReady, setIsReady] = useState(false)
  const isMobile = useIsMobile()

  // Wait for client-side hydration and screen width detection
  useEffect(() => {
    setIsClient(true)

    // Get initial screen width
    const updateScreenWidth = () => {
      setScreenWidth(window.innerWidth)
      setIsReady(true) // Mark as ready once we have screen width
    }

    updateScreenWidth()
    window.addEventListener('resize', updateScreenWidth)
    return () => window.removeEventListener('resize', updateScreenWidth)
  }, [])

  // Enhanced responsive display settings based on screen width (matches actual carousel)
  const getMaxDisplaySkins = useCallback(() => {
    // Don't calculate until we have actual screen width
    if (!isReady || screenWidth === 0) {
      return 0 // Return 0 to prevent rendering until ready
    }

    if (isMobile) return 5 // Show 5 skins on mobile for better touch targets

    // Desktop responsive breakpoints - more conservative for better fit
    if (screenWidth >= 1920) return 9  // Very large screens: 9 skins
    if (screenWidth >= 1600) return 8  // Large screens: 8 skins
    if (screenWidth >= 1400) return 7  // Medium-large screens: 7 skins
    if (screenWidth >= 1200) return 6  // Medium screens: 6 skins (1080p falls here)
    if (screenWidth >= 1024) return 5  // Small desktop: 5 skins
    if (screenWidth >= 768) return 4   // Tablet: 4 skins
    return 3 // fallback for very small screens
  }, [isMobile, screenWidth, isReady])

  const maxDisplaySkins = getMaxDisplaySkins()

  // Determine if we should show mobile layout (only after ready)
  const showMobileLayout = isReady && isMobile

  // Apply the same background as the actual champion page
  useEffect(() => {
    if (backgroundImage) {
      // Create background element with blur effect (same as ChampionBackground component)
      const backgroundElement = document.createElement('div')
      backgroundElement.id = 'skeleton-background-image'
      backgroundElement.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        z-index: 0;
        background-image: url('${backgroundImage}');
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        filter: blur(10px) brightness(0.25) saturate(1.2);
        transform: scale(1.1);
        pointer-events: none;
      `
      document.body.insertBefore(backgroundElement, document.body.firstChild)

      // Add overlay (same as ChampionBackground component)
      const overlay = document.createElement('div')
      overlay.id = 'skeleton-background-overlay'
      overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        z-index: 1;
        background: linear-gradient(to bottom, rgba(0,0,0,0.4), rgba(0,0,0,0.2), rgba(0,0,0,0.6)),
                    linear-gradient(to right, rgba(0,0,0,0.6), rgba(0,0,0,0.2), rgba(0,0,0,0.6)),
                    linear-gradient(to top, rgba(17, 24, 39, 0.9), transparent, rgba(17, 24, 39, 0.5));
        pointer-events: none;
      `
      document.body.insertBefore(overlay, document.body.firstChild)

      // Header z-index is now handled by CSS media queries
      // No need to manipulate header z-index via JavaScript

      const main = document.querySelector('main')
      if (main instanceof HTMLElement) {
        main.style.position = 'relative'
        main.style.zIndex = '10'
      }

      const footer = document.querySelector('footer')
      if (footer instanceof HTMLElement) {
        footer.style.position = 'relative'
        footer.style.zIndex = '10'
      }

      const sidebar = document.querySelector('aside')
      if (sidebar instanceof HTMLElement) {
        sidebar.style.zIndex = '50'
      }

      // Cleanup function
      return () => {
        const existingBackground = document.getElementById('skeleton-background-image')
        if (existingBackground) {
          existingBackground.remove()
        }

        const existingOverlay = document.getElementById('skeleton-background-overlay')
        if (existingOverlay) {
          existingOverlay.remove()
        }

        // Header z-index is handled by CSS, no need to reset

        const sidebar = document.querySelector('aside')
        if (sidebar instanceof HTMLElement) {
          sidebar.style.zIndex = ''
        }

        const main = document.querySelector('main')
        if (main instanceof HTMLElement) {
          main.style.zIndex = ''
          main.style.position = ''
        }

        const footer = document.querySelector('footer')
        if (footer instanceof HTMLElement) {
          footer.style.zIndex = ''
          footer.style.position = ''
        }
      }
    }
  }, [backgroundImage])
  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-2 min-h-screen">
      {/* Reserve space to prevent layout shift */}
      <div className="min-h-[1200px]">
      {/* Header with Skin Navigation */}
      <div className="mb-6 lg:mb-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8">
          {/* Champion Info */}
          <div>
            <div className="flex items-center space-x-4 mb-4">
              <Skeleton className="w-20 h-20 rounded-full" />
              <div>
                <div className="flex items-center space-x-3 mb-2">
                  <Skeleton className="h-8 w-8" />
                  <Skeleton className="h-10 w-48" />
                  <Skeleton className="w-8 h-8" />
                </div>
                <Skeleton className="h-6 w-64 mb-3" />
                <div className="flex items-center space-x-2 mb-3">
                  <Skeleton className="h-6 w-20" />
                  <Skeleton className="h-6 w-16" />
                </div>

                {/* Cost, Skins Count, and Release Date */}
                <div className="space-y-2">
                  <div className="flex items-center space-x-4">
                    <Skeleton className="h-10 w-24" />
                    <Skeleton className="h-10 w-20" />
                    <Skeleton className="h-10 w-16" />
                  </div>
                  <Skeleton className="h-10 w-48" />
                </div>
              </div>
            </div>

            {/* Win Rate Graph positioned under the champion info */}
            <div className="mt-6">
              <Card className="bg-black/70 backdrop-blur-md border-orange-700/40 shadow-xl h-80">
                <CardContent className="p-6 flex flex-col h-full">
                  {/* Header */}
                  <div className="flex items-center justify-between mb-3">
                    <div>
                      <Skeleton className="h-6 w-32 mb-1" />
                      <Skeleton className="h-4 w-48" />
                    </div>
                    <Skeleton className="h-9 w-40" />
                  </div>

                  {/* Average Win Rate */}
                  <div className="mb-2">
                    <div className="flex items-center space-x-2">
                      <Skeleton className="h-4 w-24" />
                      <Skeleton className="h-5 w-12" />
                    </div>
                  </div>

                  {/* Chart Area */}
                  <div className="flex-1 relative">
                    <div className="w-full h-40 relative">
                      {/* Grid lines skeleton */}
                      <div className="absolute inset-0">
                        {Array.from({ length: 5 }).map((_, i) => (
                          <div
                            key={i}
                            className="absolute w-full border-t border-gray-700/30"
                            style={{ top: `${(i * 100) / 4}%` }}
                          />
                        ))}
                      </div>

                      {/* Y-axis labels skeleton */}
                      <div className="absolute left-0 top-0 h-full flex flex-col justify-between py-1">
                        {Array.from({ length: 5 }).map((_, i) => (
                          <Skeleton key={i} className="h-3 w-8" />
                        ))}
                      </div>

                      {/* Chart line skeleton */}
                      <div className="absolute inset-0 flex items-center justify-center">
                        <Skeleton className="h-20 w-full rounded-lg" />
                      </div>

                      {/* X-axis labels skeleton */}
                      <div className="absolute bottom-0 left-0 right-0 flex justify-between px-10">
                        {Array.from({ length: 7 }).map((_, i) => (
                          <Skeleton key={i} className="h-3 w-6" />
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Skin Display */}
          <div className="relative">
            {/* Chromas Button and View Mode Toggle */}
            <div className="flex items-center justify-between mb-4">
              {/* Chromas Button (Left Side) */}
              <div className="flex items-center space-x-3">
                <Skeleton className="w-8 h-8 rounded-full" />
                <Skeleton className="h-4 w-24" />
              </div>

              {/* View Mode Toggle (Right Side) */}
              <div className="flex items-center space-x-3">
                <Skeleton className="h-4 w-16" />
                <Skeleton className="w-12 h-6 rounded-full" />
                <Skeleton className="h-4 w-20" />
              </div>
            </div>

            {/* Main Display */}
            <div className="w-full h-96 rounded-lg bg-gray-800/40 border border-gray-700/20 relative">
              <Skeleton className="w-full h-full rounded-lg" />

              {/* Skin Info Overlay */}
              <div className="absolute bottom-4 left-4 right-4 bg-black/90 backdrop-blur-md rounded-lg p-4 border border-gray-700/40">
                <div className="flex items-center justify-between">
                  <div>
                    <Skeleton className="h-5 w-32 mb-2" />
                    <Skeleton className="h-4 w-16" />
                  </div>
                  <div className="flex items-center space-x-2">
                    <Skeleton className="h-6 w-20" />
                    <Skeleton className="h-4 w-12" />
                  </div>
                </div>
              </div>
            </div>

            {/* Skin Carousel - Only render when screen width is known */}
            <div className="relative mt-3 px-2 sm:px-0 min-h-[80px] sm:min-h-[100px]" style={{ paddingTop: '5px', paddingBottom: '0px' }}>
              {isReady && (
                <div className="flex items-center justify-center h-full gap-2 sm:gap-3">
                  {/* Left Arrow - Hidden on mobile */}
                  {!showMobileLayout && (
                    <Skeleton className="w-6 h-6 rounded-full flex-shrink-0" />
                  )}

                  {/* Skin carousel container - Responsive */}
                  <div
                    className={`relative overflow-visible ${showMobileLayout ? 'w-full' : 'flex-1'} ${showMobileLayout ? 'h-[100px]' : 'h-[140px]'}`}
                    style={{
                      maxWidth: showMobileLayout ? '320px' : `${maxDisplaySkins * 72 + (maxDisplaySkins - 1) * 4}px`
                    }}
                  >
                    <div
                      className="relative select-none w-full"
                      style={{
                        height: showMobileLayout ? '80px' : '100px',
                        padding: showMobileLayout ? '20px 10px' : '40px 20px'
                      }}
                    >
                      <div
                        className="flex justify-center items-center space-x-1"
                        style={{ height: showMobileLayout ? '48px' : '64px' }}
                      >
                        {Array.from({ length: maxDisplaySkins }).map((_, index) => (
                          <div
                            key={index}
                            className="flex justify-center items-center flex-shrink-0"
                            style={{ width: showMobileLayout ? '56px' : '72px' }}
                          >
                            <div className="relative">
                              <Skeleton className={`${showMobileLayout ? 'w-12 h-12' : 'w-16 h-16'} rounded-lg`} />
                              {/* Rarity icon skeleton */}
                              <div
                                className="absolute left-1/2 transform -translate-x-1/2"
                                style={{ bottom: showMobileLayout ? '-1px' : '-2px' }}
                              >
                                <Skeleton className={`${showMobileLayout ? 'w-3.5 h-3.5' : 'w-4 h-4'} rounded-full`} />
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* Right Arrow - Hidden on mobile */}
                  {!showMobileLayout && (
                    <Skeleton className="w-6 h-6 rounded-full flex-shrink-0" />
                  )}
                </div>
              )}

              {/* Mobile hint - Show only on mobile */}
              {isReady && showMobileLayout && (
                <div className="text-center mt-2">
                  <Skeleton className="h-3 w-20 mx-auto" />
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Champion Stats */}
      <div className="mb-6 sm:mb-8 grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 lg:gap-8">
        {/* Base Stats */}
        <Card className="bg-black/70 backdrop-blur-md border-orange-700/40 shadow-xl">
          <CardContent className="p-4 sm:p-6">
            <Skeleton className="h-5 sm:h-6 w-20 sm:w-24 mb-3 sm:mb-4" />
            <div className="space-y-3 sm:space-y-4">
              <div className="base-stats-grid grid grid-cols-2 gap-3 sm:gap-4">
                {Array.from({ length: 6 }).map((_, index) => (
                  <div key={index}>
                    <div className="flex items-center space-x-2 text-xs sm:text-sm mb-1">
                      <Skeleton className="w-4 h-4 sm:w-5 sm:h-5" />
                      <Skeleton className="h-3 sm:h-4 w-12 sm:w-16" />
                    </div>
                    <Skeleton className="h-5 sm:h-6 w-10 sm:w-12" />
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Champion Matchups */}
        <Card className="bg-black/70 backdrop-blur-md border-orange-700/40 shadow-xl">
          <CardContent className="p-4 sm:p-6">
            {/* Header with dropdown */}
            <div className="flex items-center justify-between mb-3 sm:mb-4">
              <Skeleton className="h-5 sm:h-6 w-24 sm:w-32" />
              <div className="relative">
                <Skeleton className="h-8 sm:h-9 w-32 sm:w-40 rounded-lg" />
              </div>
            </div>

            {/* Matchup Grid - 3x3 layout */}
            <div className="matchup-grid grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 sm:gap-3 overflow-hidden">
              {Array.from({ length: 9 }).map((_, index) => (
                <div key={index} className="flex items-center space-x-2 p-2 rounded-lg bg-gray-800/30">
                  {/* Current Champion Icon */}
                  <Skeleton className="w-6 h-6 sm:w-8 sm:h-8 rounded-full flex-shrink-0" />

                  {/* Progress Bar */}
                  <div className="flex-1 mx-2 min-w-0">
                    <Skeleton className="h-2.5 w-full rounded-full mb-1" />
                    <div className="flex justify-between text-xs">
                      <Skeleton className="h-3 w-8" />
                      <Skeleton className="h-3 w-8" />
                    </div>
                  </div>

                  {/* Opponent Champion Icon */}
                  <Skeleton className="w-6 h-6 sm:w-8 sm:h-8 rounded-full flex-shrink-0" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Abilities Section */}
      <Card className="bg-black/70 backdrop-blur-md border-orange-700/40 shadow-xl mb-8">
        <CardContent className="p-6">
          <Skeleton className="h-5 sm:h-6 w-16 sm:w-20 mb-6" />
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Array.from({ length: 5 }).map((_, index) => (
              <div key={index} className="flex flex-col h-full">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="w-12 h-12 rounded-lg overflow-hidden bg-gray-800/50 border border-orange-500/40 flex items-center justify-center">
                    <Skeleton className="w-12 h-12" />
                  </div>
                  <div>
                    <div className="flex items-center space-x-2">
                      {index > 0 && (
                        <Skeleton className="h-4 sm:h-5 w-5 sm:w-6 rounded" />
                      )}
                      <Skeleton className="h-4 sm:h-5 w-20 sm:w-24" />
                    </div>
                  </div>
                </div>

                <Skeleton className="h-3 sm:h-4 w-full mb-2" />
                <Skeleton className="h-3 sm:h-4 w-3/4 mb-2" />
                <Skeleton className="h-3 sm:h-4 w-1/2 mb-3 flex-grow" />

                {index > 0 && (
                  <div className="flex justify-between items-center text-xs mt-auto">
                    <Skeleton className="h-3 w-16" />
                    <Skeleton className="h-3 w-12" />
                  </div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Lore Section */}
      <Card className="bg-black/70 backdrop-blur-md border-orange-700/40 shadow-xl">
        <CardContent className="p-6">
          <Skeleton className="h-5 sm:h-6 w-12 sm:w-16 mb-6" />
          <div className="space-y-3">
            <Skeleton className="h-3 sm:h-4 w-full" />
            <Skeleton className="h-3 sm:h-4 w-full" />
            <Skeleton className="h-3 sm:h-4 w-full" />
            <Skeleton className="h-3 sm:h-4 w-3/4" />
            <Skeleton className="h-3 sm:h-4 w-full" />
            <Skeleton className="h-3 sm:h-4 w-5/6" />
            <Skeleton className="h-3 sm:h-4 w-full" />
            <Skeleton className="h-3 sm:h-4 w-2/3" />
          </div>
        </CardContent>
      </Card>
      </div>
    </div>
  )
}
