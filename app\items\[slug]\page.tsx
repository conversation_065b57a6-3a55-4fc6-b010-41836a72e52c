"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useR<PERSON><PERSON> } from "next/navigation"
import SharedLayout from "@/components/shared-layout"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ExternalLink, Bell, ArrowLeft, Sword } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import ItemBuildTree from "@/components/items/item-build-tree"
import ComponentUpgradeTree from "@/components/items/component-upgrade-tree"

interface ItemData {
  id: string
  name: string
  description: string
  plaintext: string
  image: string
  category: string
  formattedPrice: string
  sellPrice: string
  components: string[]
  buildsInto: string[]
  isPurchasable: boolean
  tags: string[]
  gold: {
    base: number
    total: number
    sell: number
    purchasable: boolean
  }
  stats: Record<string, number>
  mainStats: string[]
  isFullItem: boolean
  maps: Record<string, boolean>
  availability: 'Available' | 'Legacy' | 'Removed'
}

// Function to create URL-friendly item names
function createItemSlug(itemName: string): string {
  return itemName
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters except spaces and hyphens
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .replace(/^-|-$/g, '') // Remove leading/trailing hyphens
}

// Function to format tags (add spaces between words)
function formatTag(tag: string): string {
  return tag.replace(/([a-z])([A-Z])/g, '$1 $2')
}

// Function to truncate long tags for display
function truncateTag(tag: string, maxLength: number = 12): string {
  const formatted = formatTag(tag)
  if (formatted.length <= maxLength) {
    return formatted
  }
  return formatted.substring(0, maxLength) + '...'
}

// Function to format stat names
function formatStatName(statName: string): string {
  const statMappings: Record<string, string> = {
    'FlatHPPoolMod': 'Health',
    'FlatMPPoolMod': 'Mana',
    'FlatArmorMod': 'Armor',
    'FlatSpellBlockMod': 'Magic Resist',
    'FlatPhysicalDamageMod': 'Attack Damage',
    'FlatMagicDamageMod': 'Ability Power',
    'FlatCritChanceMod': 'Critical Strike Chance',
    'FlatAttackSpeedMod': 'Attack Speed',
    'FlatMovementSpeedMod': 'Movement Speed',
    'PercentLifeStealMod': 'Life Steal',
    'PercentSpellVampMod': 'Spell Vamp',
    'rFlatHPModPerLevel': 'Health per Level',
    'rFlatMPModPerLevel': 'Mana per Level',
    'rFlatArmorModPerLevel': 'Armor per Level',
    'rFlatSpellBlockModPerLevel': 'Magic Resist per Level',
    'rFlatPhysicalDamageModPerLevel': 'Attack Damage per Level',
    'rFlatMagicDamageModPerLevel': 'Ability Power per Level',
    'rPercentCooldownMod': 'Cooldown Reduction',
    'rPercentCooldownModPerLevel': 'Cooldown Reduction per Level'
  }

  return statMappings[statName] || statName.replace(/([a-z])([A-Z])/g, '$1 $2')
}

// Function to determine item type based on components and buildsInto
function getItemType(item: ItemData): string {
  const hasComponents = item.components && item.components.length > 0
  const hasBuildsInto = item.buildsInto && item.buildsInto.length > 0

  if (!hasComponents) {
    return 'Component'
  } else if (hasComponents && hasBuildsInto) {
    return 'Secondary Item'
  } else if (hasComponents && !hasBuildsInto) {
    return 'Full Item'
  }
  return 'Component'
}

// Function to get stat icon URL and color
function getStatIcon(statName: string): { url: string; color?: string } | null {
  const statIconMap: Record<string, { url: string; color?: string }> = {
    'Health': {
      url: 'https://raw.communitydragon.org/latest/game/assets/perks/statmods/statmodshealthscalingicon.png'
    },
    'Health Regen': {
      url: 'https://raw.communitydragon.org/latest/game/assets/perks/statmods/statmodshealthplusicon.png'
    },
    'Base Health Regen': {
      url: 'https://raw.communitydragon.org/latest/game/assets/perks/statmods/statmodshealthplusicon.png'
    },
    'Base Mana Regen': {
      url: 'https://i.ibb.co/6cqhsLTH/Frame-3-2.png'
    },
    'Mana': {
      url: 'https://i.ibb.co/0VFpT40y/Frame-3-1.png'
    },
    'Magic Resist': {
      url: 'https://raw.communitydragon.org/latest/game/assets/perks/statmods/statmodsmagicresicon.png'
    },
    'Move Speed': {
      url: 'https://raw.communitydragon.org/latest/game/assets/perks/statmods/statmodsmovementspeedicon.png'
    },
    'Movement Speed': {
      url: 'https://raw.communitydragon.org/latest/game/assets/perks/statmods/statmodsmovementspeedicon.png'
    },
    'Ability Haste': {
      url: 'https://raw.communitydragon.org/latest/game/assets/perks/statmods/statmodscdrscalingicon.png'
    },
    'Magic Penetration': {
      url: 'https://raw.communitydragon.org/latest/game/assets/perks/statmods/statmodstenacityicon.png'
    },
    'Attack Speed': {
      url: 'https://raw.communitydragon.org/latest/game/assets/perks/statmods/statmodsattackspeedicon.png'
    },
    'Attack Damage': {
      url: 'https://raw.communitydragon.org/latest/game/assets/perks/statmods/statmodsattackdamageicon.png'
    },
    'Armor': {
      url: 'https://raw.communitydragon.org/latest/game/assets/perks/statmods/statmodsarmoricon.png'
    },
    'Adaptive Force': {
      url: 'https://raw.communitydragon.org/latest/game/assets/perks/statmods/statmodsadaptiveforceicon.png'
    },
    'Ability Power': {
      url: 'https://raw.communitydragon.org/latest/game/assets/perks/statmods/statmodsabilitypowericon.png',
      color: '#6c3ccc'
    }
  }

  return statIconMap[statName] || null
}

// Function to extract stats from item description
function extractStats(description: string): string[] {
  if (!description) return []

  // Decode HTML entities
  let decoded = description.replace(/\\u003C/g, '<').replace(/\\u003E/g, '>')

  // Extract content between <stats> tags
  const statsMatch = decoded.match(/<stats>(.*?)<\/stats>/s)
  if (!statsMatch) return []

  let statsContent = statsMatch[1]

  // Remove all HTML tags except <br>
  statsContent = statsContent.replace(/<(?!br\s*\/?>)[^>]*>/g, '')

  // Split by <br> and clean up
  const stats = statsContent
    .split(/<br\s*\/?>/i)
    .map(stat => stat.trim())
    .filter(stat => stat.length > 0)

  return stats
}

// Function to clean item description HTML (excluding stats)
function cleanDescription(description: string): string {
  if (!description) return ''

  // Decode HTML entities
  let cleaned = description.replace(/\\u003C/g, '<').replace(/\\u003E/g, '>')

  // Remove the entire stats section
  cleaned = cleaned.replace(/<stats>.*?<\/stats>/s, '')

  // Remove mainText wrapper
  cleaned = cleaned.replace(/<\/?mainText>/g, '')

  // Handle passive sections - convert to "Passive – Name:" and add line break
  cleaned = cleaned.replace(/<passive>(.*?)<\/passive>/g, 'Passive – $1:\n')

  // Handle active sections - convert to "Active – Name:" and add line break
  cleaned = cleaned.replace(/<active>(.*?)<\/active>/g, 'Active – $1:\n')

  // Remove ALL other HTML tags (anything between < and >)
  cleaned = cleaned.replace(/<[^>]*>/g, '')

  // Clean up multiple consecutive line breaks
  cleaned = cleaned.replace(/\n{3,}/g, '\n\n')

  // Remove leading/trailing whitespace and line breaks
  cleaned = cleaned.trim()

  return cleaned
}

export default function ItemDetailPage() {
  const params = useParams()
  const router = useRouter()
  const [item, setItem] = useState<ItemData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [relatedItems, setRelatedItems] = useState<ItemData[]>([])
  const [allItems, setAllItems] = useState<ItemData[]>([])

  useEffect(() => {
    const fetchItemData = async () => {
      try {
        setLoading(true)
        setError(null)

        // Fetch all items to find the matching one
        const response = await fetch('/api/items')
        if (!response.ok) {
          throw new Error('Failed to fetch items data')
        }

        const result = await response.json()
        if (!result.success) {
          throw new Error(result.error || 'Failed to fetch items data')
        }

        const allItems: ItemData[] = result.data
        setAllItems(allItems) // Store all items for build tree
        const slug = params.slug as string

        // Find the item that matches the slug
        const foundItem = allItems.find(i => createItemSlug(i.name) === slug)
        
        if (!foundItem) {
          throw new Error('Item not found')
        }

        setItem(foundItem)

        // Find related items (same category)
        const categoryItems = allItems
          .filter(i => i.category === foundItem.category && i.id !== foundItem.id && i.isPurchasable)
          .slice(0, 6) // Limit to 6 related items

        setRelatedItems(categoryItems)

      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
        setError(errorMessage)
        console.error('❌ Failed to fetch item data:', errorMessage)
      } finally {
        setLoading(false)
      }
    }

    if (params.slug) {
      fetchItemData()
    }
  }, [params.slug])

  if (loading) {
    return (
      <SharedLayout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto mb-4"></div>
            <p className="text-gray-300">Loading item details...</p>
          </div>
        </div>
      </SharedLayout>
    )
  }

  if (error || !item) {
    return (
      <SharedLayout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="relative mx-auto mb-4 w-32 h-32">
              <Image
                src="https://raw.communitydragon.org/latest/game/assets/loadouts/summoneremotes/champions/amumu/amumu_sad_crying_glow.png"
                alt="Failed to load glow"
                width={128}
                height={128}
                className="absolute inset-0 opacity-60"
                unoptimized
              />
              <Image
                src="https://raw.communitydragon.org/latest/game/assets/loadouts/summoneremotes/champions/amumu/amumu_sad_crying_vfx.png"
                alt="Failed to load"
                width={128}
                height={128}
                className="relative z-10"
                unoptimized
              />
            </div>
            <h1 className="text-2xl font-bold text-white mb-4">Item Not Found</h1>
            <p className="text-gray-400 mb-6">{error || 'The requested item could not be found.'}</p>
            <Link href="/items">
              <Button variant="outline" className="border-gray-700/20 text-gray-300">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Items
              </Button>
            </Link>
          </div>
        </div>
      </SharedLayout>
    )
  }

  return (
    <SharedLayout>
      <div className="container mx-auto px-8 pb-8 pl-8 pr-8">
        {/* Main Item Details - Layout: Details Left, Image Right */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          {/* Item Information - LEFT */}
          <div className="space-y-6">
            <div>
              <h1 className="text-4xl font-bold text-white mb-2">{item.name}</h1>

              <div className="flex flex-wrap items-center gap-2 mb-6">
                {item.tags.map((tag, index) => (
                  <Badge
                    key={index}
                    variant="secondary"
                    className="text-xs px-2 py-1 bg-violet-600/20 text-violet-300 border-violet-600/30"
                  >
                    {formatTag(tag)}
                  </Badge>
                ))}
              </div>
            </div>

            {/* Enhanced Item Details */}
            <Card className="bg-gray-900/40 border-blue-700/20">
              <CardContent className="p-6">
                <h3 className="text-xl font-semibold text-white mb-4">Item Details</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Cost:</span>
                    {item.gold.total === 0 ? (
                      <span className="font-medium text-gray-400">
                        N/A
                      </span>
                    ) : (
                      <div className="flex items-center space-x-1">
                        <span
                          className="font-medium"
                          style={{ color: '#fb923c' }}
                        >
                          {item.gold.total}
                        </span>
                        <Image
                          src="https://raw.communitydragon.org/latest/game/assets/ux/tft/regionportals/icon/gold.png"
                          alt="Gold"
                          width={16}
                          height={16}
                          className="w-4 h-4"
                          style={{
                            userSelect: 'none',
                            pointerEvents: 'none',
                            filter: 'brightness(0) saturate(100%) invert(73%) sepia(85%) saturate(1234%) hue-rotate(359deg) brightness(95%) contrast(95%)'
                          }}
                          draggable={false}
                        />
                      </div>
                    )}
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Sell Price:</span>
                    {item.gold.sell === 0 ? (
                      <span className="font-medium text-gray-400">
                        N/A
                      </span>
                    ) : (
                      <div className="flex items-center space-x-1">
                        <span
                          className="font-medium"
                          style={{ color: '#fb923c' }}
                        >
                          {item.gold.sell}
                        </span>
                        <Image
                          src="https://raw.communitydragon.org/latest/game/assets/ux/tft/regionportals/icon/gold.png"
                          alt="Gold"
                          width={16}
                          height={16}
                          className="w-4 h-4"
                          style={{
                            userSelect: 'none',
                            pointerEvents: 'none',
                            filter: 'brightness(0) saturate(100%) invert(73%) sepia(85%) saturate(1234%) hue-rotate(359deg) brightness(95%) contrast(95%)'
                          }}
                          draggable={false}
                        />
                      </div>
                    )}
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Type:</span>
                    <span className="text-white">{getItemType(item)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Purchasable:</span>
                    <span className={`font-medium ${item.isPurchasable ? 'text-green-400' : 'text-red-400'}`}>
                      {item.isPurchasable ? 'Yes' : 'No'}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Item Stats */}
            {(() => {
              const extractedStats = extractStats(item.description)
              return extractedStats.length > 0 && (
                <Card className="bg-gray-900/40 border-blue-700/20">
                  <CardContent className="p-6">
                    <h3 className="text-xl font-semibold text-white mb-4">Stats</h3>
                    <div className="space-y-2">
                      {extractedStats.map((stat, index) => {
                        // Parse stat to separate name and value
                        // Handle patterns like "60 Attack Damage", "10% Heal and Shield Power", "75% Base Mana Regen"
                        const match = stat.match(/^(\d+%?)\s+(.+)$/)
                        if (match) {
                          const [, statValue, statName] = match
                          const statIcon = getStatIcon(statName.trim())

                          return (
                            <div key={index} className="flex justify-between items-center">
                              <div className="flex items-center gap-2">
                                {statIcon && (
                                  <Image
                                    src={statIcon.url}
                                    alt={statName.trim()}
                                    width={24}
                                    height={24}
                                    className="w-6 h-6 flex-shrink-0"
                                    style={{
                                      userSelect: 'none',
                                      pointerEvents: 'none',
                                      ...(statIcon.color && {
                                        filter: 'brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%) hue-rotate(246deg) brightness(104%) contrast(97%)'
                                      })
                                    }}
                                    draggable={false}
                                  />
                                )}
                                <span className="text-gray-400">{statName.trim()}</span>
                              </div>
                              <span className="text-white font-medium">{statValue}</span>
                            </div>
                          )
                        }
                        // Fallback for stats that don't match the pattern
                        return (
                          <div key={index} className="text-white">
                            {stat}
                          </div>
                        )
                      })}
                    </div>
                  </CardContent>
                </Card>
              )
            })()}
          </div>

          {/* Item Image/Build Tree - RIGHT */}
          <div className="space-y-4">
            {/* Check if it's a component item (has "into" but no "from") */}
            {(!item.components || item.components.length === 0) && item.buildsInto && item.buildsInto.length > 0 ? (
              <ComponentUpgradeTree
                item={item}
                allItems={allItems}
                onItemClick={(itemSlug) => router.push(`/items/${itemSlug}`)}
              />
            ) : (
              <ItemBuildTree
                item={item}
                allItems={allItems}
                onItemClick={(itemSlug) => router.push(`/items/${itemSlug}`)}
              />
            )}

            {/* Action Buttons */}
            <div className="space-y-3">
              <Button variant="outline" className="w-full border-gray-700/20 text-gray-500 cursor-not-allowed opacity-50" disabled>
                <Bell className="h-4 w-4 mr-2" />
                Add to Watchlist (Coming Soon)
              </Button>
            </div>
          </div>
        </div>

        {/* Description - Full Width */}
        {item.description && cleanDescription(item.description).trim() && (
          <div className="mb-12">
            <Card className="bg-gray-900/40 border-blue-700/20">
              <CardContent className="p-6">
                <h3 className="text-xl font-semibold text-white mb-4">Description</h3>
                <div className="text-gray-300 leading-relaxed whitespace-pre-line">
                  {cleanDescription(item.description)}
                </div>
              </CardContent>
            </Card>
          </div>
        )}





        {/* Related Items */}
        {relatedItems.length > 0 && (
          <div>
            <h2 className="text-2xl font-bold text-white mb-6">
              More {item.category} Items
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
              {relatedItems.map((relatedItem) => (
                <Link
                  key={relatedItem.id}
                  href={`/items/${createItemSlug(relatedItem.name)}`}
                  className="block"
                >
                  <Card className="bg-gray-900/40 border-purple-700/20 hover:border-purple-400/50 transition-all duration-300 cursor-pointer">
                  <CardContent className="p-3">
                    <div className="flex items-center gap-3">
                      {/* Item Icon */}
                      <div className="flex-shrink-0">
                        <div
                          className="rounded-lg overflow-hidden bg-gray-800/50"
                          style={{
                            width: "48px",
                            height: "48px"
                          }}
                        >
                          <Image
                            src={relatedItem.image || "/placeholder.svg"}
                            alt={relatedItem.name}
                            width={48}
                            height={48}
                            className="w-full h-full object-contain"
                            loading="lazy"
                          />
                        </div>
                      </div>

                      {/* Item Details */}
                      <div className="flex-1 min-w-0">
                        {/* Item Name */}
                        <h3 className="font-semibold text-white text-sm mb-1 truncate">
                          {relatedItem.name}
                        </h3>

                        {/* Tags */}
                        <div className="flex flex-wrap gap-1">
                          {relatedItem.tags.length > 0 && (
                            <>
                              {/* First Tag */}
                              <Badge
                                variant="secondary"
                                className="border-blue-600/30 text-blue-400 bg-blue-600/20 text-xs"
                              >
                                {formatTag(relatedItem.tags[0])}
                              </Badge>

                              {/* Second Tag (if exists) */}
                              {relatedItem.tags.length > 1 && (
                                <Badge
                                  variant="secondary"
                                  className="border-blue-600/30 text-blue-400 bg-blue-600/20 text-xs"
                                >
                                  {truncateTag(relatedItem.tags[1])}
                                </Badge>
                              )}

                              {/* Additional Tags Indicator */}
                              {relatedItem.tags.length > 2 && (
                                <div className="relative group">
                                  <Badge
                                    variant="secondary"
                                    className="border-orange-600/30 text-orange-400 bg-orange-600/20 text-xs cursor-pointer hover:border-orange-500/50 transition-colors"
                                  >
                                    +{relatedItem.tags.length - 2}
                                  </Badge>
                                  {/* Custom Tooltip */}
                                  <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 hidden group-hover:block z-50">
                                    <div className="bg-slate-800/80 text-slate-100 border border-slate-700/50 backdrop-blur-sm rounded-md px-3 py-1.5 shadow-lg text-xs">
                                      <div className="flex flex-wrap gap-1 min-w-max">
                                        {relatedItem.tags.slice(2).map((tag, tagIndex) => (
                                          <Badge
                                            key={tagIndex}
                                            variant="secondary"
                                            className="border-blue-600/30 text-blue-400 bg-blue-600/20 text-xs whitespace-nowrap"
                                          >
                                            {formatTag(tag)}
                                          </Badge>
                                        ))}
                                      </div>
                                      {/* Arrow pointing down */}
                                      <div className="absolute top-full left-1/2 transform -translate-x-1/2">
                                        <div className="w-0 h-0 border-l-[6px] border-r-[6px] border-t-[6px] border-l-transparent border-r-transparent border-t-slate-800/80"></div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              )}
                            </>
                          )}
                        </div>
                      </div>

                      {/* Price */}
                      <div className="flex-shrink-0">
                        {relatedItem.gold.total === 0 ? (
                          <span className="font-medium text-sm text-gray-400">
                            N/A
                          </span>
                        ) : (
                          <div className="flex items-center gap-1">
                            <span
                              className="font-medium text-sm"
                              style={{ color: '#fb923c' }}
                            >
                              {relatedItem.gold.total}
                            </span>
                            <Image
                              src="https://raw.communitydragon.org/latest/game/assets/ux/tft/regionportals/icon/gold.png"
                              alt="Gold"
                              width={12}
                              height={12}
                              className="w-3 h-3 flex-shrink-0"
                              style={{
                                userSelect: 'none',
                                pointerEvents: 'none',
                                filter: 'brightness(0) saturate(100%) invert(73%) sepia(85%) saturate(1234%) hue-rotate(359deg) brightness(95%) contrast(95%)'
                              }}
                              draggable={false}
                            />
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                  </Card>
                </Link>
              ))}
            </div>
          </div>
        )}
      </div>
    </SharedLayout>
  )
}
