// Skin Service - Fetches and processes skin data from Community Dragon API

import { CommunityDragonSkin, CommunityDragonSkinsResponse, CommunityDragonQuestSkinInfo } from '@/lib/types/league-api'

export interface ProcessedSkinData {
  id: number
  name: string
  champion: string
  championKey: string
  tier: string
  rarity: string
  price: string
  image: string
  splashArt: string
  videoUrl?: string // For Exalted skins with animated splash
  isLegacy: boolean
  isBase: boolean
  skinType: string
  contentId: string
  skinNumber: number
  skinLines: string[] // Array of skin line names
  // New fields for enhanced skin details
  description?: string
  featuresText?: string
  hasNewEffects: boolean | null // null = N/A (no LOLDB data)
  hasNewAnimations: boolean | null // null = N/A (no LOLDB data)
  hasNewRecall: boolean | null // null = N/A (no LOLDB data)
  hasChromas: boolean
  set?: string // Skin line/set name
  releaseDate?: string
  availability: string | null // Available, Legacy, Limited, etc. - null = N/A (no LOLDB data)
  lootEligible: boolean | null // null = N/A (no LOLDB data)
  lastDiscount?: string
  itemId?: number // Store item ID for sales API
  questSkinInfo?: CommunityDragonQuestSkinInfo // Quest skin information
  // New fields from LOLDB API
  newVoiceLines?: boolean | null // New Voice Lines field, null = N/A (no LOLDB data)
  // Upcoming skin fields
  isUpcoming?: boolean // Flag to identify upcoming skins
  releaseCountdown?: string // ISO date string for countdown
}

// Interface for skin store data from the new API
interface SkinStoreData {
  itemId: number
  inventoryType: string
  iconUrl: string
  maxQuantity: number
  rp: number
  releaseDate: number
  name: string
}

// Interface for LOLDB API skin data
interface LoldbSkinData {
  id: number
  skinId: number
  name: string
  championId: number
  availability: string
  cost: number
  release: string
  looteligible: boolean | null
  neweffects: boolean | null
  newanimations: boolean | null
  newrecall: boolean | null
  newvoice: boolean | null
  distribution: string | null
  variant: string | null
  formatname: string | null
  set: string | null
  voiceactor: string | null
  splashartist: string | null
  lore: string | null
  chromas: string | null
  createdAt: string
  updatedAt: string
}

// Interface for LOLDB API response
interface LoldbApiResponse {
  data: {
    id: number
    name: string
    createdAt: string
    updatedAt: string
    skins: LoldbSkinData[]
  }[]
}

// Hardcoded upcoming skins data
const UPCOMING_SKINS: ProcessedSkinData[] = [
  {
    id: 999001, // Use high ID to avoid conflicts
    name: "Spirit Blossom Springs Yunara",
    champion: "Yunara",
    championKey: "yunara",
    tier: "Epic",
    rarity: "Epic",
    price: "1350 RP",
    image: "https://raw.communitydragon.org/pbe/plugins/rcp-be-lol-game-data/global/default/assets/characters/yunara/skins/skin01/images/yunara_splash_centered_1.yunara.jpg",
    splashArt: "https://raw.communitydragon.org/pbe/plugins/rcp-be-lol-game-data/global/default/assets/characters/yunara/skins/skin01/images/yunara_splash_centered_1.yunara.jpg",
    isLegacy: false,
    isBase: false,
    skinType: "kSkinType_Base",
    contentId: "yunara-spirit-blossom-springs",
    skinNumber: 1,
    skinLines: ["Spirit Blossom Springs"],
    hasNewEffects: true,
    hasNewAnimations: true,
    hasNewRecall: true,
    hasChromas: false,
    set: "Spirit Blossom Springs",
    releaseDate: "July 16, 2025",
    availability: "Unavailable",
    lootEligible: false,
    lastDiscount: "N/A",
    newVoiceLines: null,
    isUpcoming: true,
    releaseCountdown: "2025-07-16T00:00:00Z"
  },
  {
    id: 999002, // Use high ID to avoid conflicts
    name: "Nightbringer Yuumi",
    champion: "Yuumi",
    championKey: "yuumi",
    tier: "Epic",
    rarity: "Epic",
    price: "1350 RP",
    image: "https://raw.communitydragon.org/pbe/plugins/rcp-be-lol-game-data/global/default/assets/characters/yuumi/skins/skin61/images/yuumi_splash_centered_61.skins_yuumi_skin61.jpg",
    splashArt: "https://raw.communitydragon.org/pbe/plugins/rcp-be-lol-game-data/global/default/assets/characters/yuumi/skins/skin61/images/yuumi_splash_centered_61.skins_yuumi_skin61.jpg",
    isLegacy: false,
    isBase: false,
    skinType: "kSkinType_Base",
    contentId: "yuumi-nightbringer",
    skinNumber: 61,
    skinLines: ["Nightbringer and Dawnbringer"],
    hasNewEffects: true,
    hasNewAnimations: true,
    hasNewRecall: true,
    hasChromas: false,
    set: "Nightbringer and Dawnbringer",
    releaseDate: "July 16, 2025",
    availability: "Unavailable",
    lootEligible: false,
    lastDiscount: "N/A",
    newVoiceLines: null,
    isUpcoming: true,
    releaseCountdown: "2025-07-16T00:00:00Z"
  },
  {
    id: 999003, // Use high ID to avoid conflicts
    name: "Dawnbringer Kalista",
    champion: "Kalista",
    championKey: "kalista",
    tier: "Epic",
    rarity: "Epic",
    price: "1350 RP",
    image: "https://raw.communitydragon.org/pbe/plugins/rcp-be-lol-game-data/global/default/assets/characters/kalista/skins/skin24/images/kalista_splash_centered_24.skins_kalista_skin24.jpg",
    splashArt: "https://raw.communitydragon.org/pbe/plugins/rcp-be-lol-game-data/global/default/assets/characters/kalista/skins/skin24/images/kalista_splash_centered_24.skins_kalista_skin24.jpg",
    isLegacy: false,
    isBase: false,
    skinType: "kSkinType_Base",
    contentId: "kalista-dawnbringer",
    skinNumber: 24,
    skinLines: ["Nightbringer and Dawnbringer"],
    hasNewEffects: true,
    hasNewAnimations: true,
    hasNewRecall: true,
    hasChromas: false,
    set: "Nightbringer and Dawnbringer",
    releaseDate: "July 16, 2025",
    availability: "Unavailable",
    lootEligible: false,
    lastDiscount: "N/A",
    newVoiceLines: null,
    isUpcoming: true,
    releaseCountdown: "2025-07-16T00:00:00Z"
  },
  {
    id: 999004, // Use high ID to avoid conflicts
    name: "Nightbringer Hecarim",
    champion: "Hecarim",
    championKey: "hecarim",
    tier: "Epic",
    rarity: "Epic",
    price: "1350 RP",
    image: "https://raw.communitydragon.org/pbe/plugins/rcp-be-lol-game-data/global/default/assets/characters/hecarim/skins/skin41/images/hecarim_splash_centered_41.skins_hecarim_skin41.jpg",
    splashArt: "https://raw.communitydragon.org/pbe/plugins/rcp-be-lol-game-data/global/default/assets/characters/hecarim/skins/skin41/images/hecarim_splash_centered_41.skins_hecarim_skin41.jpg",
    isLegacy: false,
    isBase: false,
    skinType: "kSkinType_Base",
    contentId: "hecarim-nightbringer",
    skinNumber: 41,
    skinLines: ["Nightbringer and Dawnbringer"],
    hasNewEffects: true,
    hasNewAnimations: true,
    hasNewRecall: true,
    hasChromas: false,
    set: "Nightbringer and Dawnbringer",
    releaseDate: "July 16, 2025",
    availability: "Unavailable",
    lootEligible: false,
    lastDiscount: "N/A",
    newVoiceLines: null,
    isUpcoming: true,
    releaseCountdown: "2025-07-16T00:00:00Z"
  }
]

/**
 * Service for fetching and managing skin data from Community Dragon API
 */
export class SkinService {
  private static instance: SkinService
  private cache: Map<string, { data: ProcessedSkinData[]; timestamp: number }> = new Map()
  private skinLinesCache: Map<string, { data: Record<number, string>; timestamp: number }> = new Map()
  private skinStoreCache: Map<string, { data: SkinStoreData[]; timestamp: number }> = new Map()
  private loldbCache: Map<string, { data: LoldbApiResponse; timestamp: number }> = new Map()
  private readonly CACHE_TTL = 24 * 60 * 60 * 1000 // 24 hours
  private readonly API_URL = 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/skins.json'
  private readonly SKIN_LINES_API_URL = 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/en_gb/v1/skinlines.json'
  private readonly SKIN_STORE_API_URL = 'https://api.loldb.info/api/store/skins'
  private readonly LOLDB_SKINS_API_URL = 'https://api.loldb.info/api/lol-skins'

  private constructor() {}

  public static getInstance(): SkinService {
    if (!SkinService.instance) {
      SkinService.instance = new SkinService()
    }
    return SkinService.instance
  }

  /**
   * Fetch paginated skins data from Community Dragon API
   */
  public async getPaginatedSkins(page: number = 1, limit: number = 50): Promise<{
    skins: ProcessedSkinData[]
    total: number
    hasMore: boolean
    currentPage: number
  }> {
    // First get all skins (cached)
    const allSkins = await this.getAllSkinsInternal()

    // Calculate pagination
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedSkins = allSkins.slice(startIndex, endIndex)

    return {
      skins: paginatedSkins,
      total: allSkins.length,
      hasMore: endIndex < allSkins.length,
      currentPage: page
    }
  }

  /**
   * Get all skins count for statistics
   */
  public async getSkinsCount(): Promise<number> {
    const allSkins = await this.getAllSkinsInternal()
    return allSkins.length
  }

  /**
   * Fetch skin lines data from Community Dragon API
   */
  private async getSkinLinesData(): Promise<Record<number, string>> {
    const cacheKey = 'skin_lines'
    const cached = this.skinLinesCache.get(cacheKey)

    // Check if cached data is still valid
    if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
      
      return cached.data
    }

    try {
      
      const response = await fetch(this.SKIN_LINES_API_URL)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: Failed to fetch skin lines data`)
      }

      const rawData: Array<{ id: number; name: string; description: string }> = await response.json()

      // Convert to a map for easy lookup
      const skinLinesMap: Record<number, string> = {}
      rawData.forEach(skinLine => {
        if (skinLine.name && skinLine.name.trim()) {
          skinLinesMap[skinLine.id] = skinLine.name
        }
      })

      // Cache the processed data
      this.skinLinesCache.set(cacheKey, {
        data: skinLinesMap,
        timestamp: Date.now()
      })

      
      return skinLinesMap

    } catch (error) {
      console.error('❌ Failed to fetch skin lines data:', error)

      // Return cached data if available, even if expired
      if (cached) {
        
        return cached.data
      }

      // Return empty object as fallback
      return {}
    }
  }

  /**
   * Internal method to fetch all skins data from Community Dragon API and process it
   */
  private async getAllSkinsInternal(): Promise<ProcessedSkinData[]> {
    const cacheKey = 'all_skins'
    const cached = this.cache.get(cacheKey)

    // Check if cached data is still valid
    if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {

      // Combine upcoming skins with cached data (upcoming first)
      return [...UPCOMING_SKINS, ...cached.data]
    }

    try {
      

      // Fetch skins data, skin lines data, store data, and LOLDB data in parallel
      const [skinsResponse, skinLinesData, storeData, loldbData] = await Promise.all([
        fetch(this.API_URL),
        this.getSkinLinesData(),
        this.getSkinStoreData(),
        this.getLoldbSkinData()
      ])

      if (!skinsResponse.ok) {
        throw new Error(`HTTP ${skinsResponse.status}: Failed to fetch skins data`)
      }

      const rawData: CommunityDragonSkinsResponse = await skinsResponse.json()

      // Process and filter the data
      const processedSkins = this.processSkinData(rawData, skinLinesData, storeData, loldbData)

      // Combine upcoming skins with regular skins (upcoming first)
      const allSkins = [...UPCOMING_SKINS, ...processedSkins]

      // Cache the processed data (without upcoming skins to avoid caching hardcoded data)
      this.cache.set(cacheKey, {
        data: processedSkins,
        timestamp: Date.now()
      })


      return allSkins

    } catch (error) {
      console.error('❌ Failed to fetch skins data:', error)

      // Return cached data if available, even if expired
      if (cached) {

        // Combine upcoming skins with cached data (upcoming first)
        return [...UPCOMING_SKINS, ...cached.data]
      }

      throw error
    }
  }

  /**
   * Legacy method for backward compatibility
   */
  public async getAllSkins(): Promise<ProcessedSkinData[]> {
    return this.getAllSkinsInternal()
  }

  /**
   * Process raw Community Dragon skin data
   */
  private processSkinData(rawData: CommunityDragonSkinsResponse, skinLinesData: Record<number, string>, storeData: SkinStoreData[], loldbData: LoldbApiResponse): ProcessedSkinData[] {
    const processedSkins: ProcessedSkinData[] = []

    // First, create a map of champion names from base skins
    const championNameMap = this.createChampionNameMap(rawData)

    // Create a map to track original skins for year variants
    const originalSkinMap = this.createOriginalSkinMap(rawData)

    for (const [skinId, skinData] of Object.entries(rawData)) {
      // Skip base skins as requested
      if (skinData.isBase) {
        continue
      }

      // Special handling for Transcendent skins - use stage 2 data but keep original name
      let effectiveSkinData = skinData
      let effectiveSkinId = skinId
      let originalName = skinData.name

      if (this.isTranscendentSkin(skinData) && skinData.questSkinInfo?.tiers) {
        const stage2Tier = skinData.questSkinInfo.tiers.find(tier => tier.stage === 2)
        if (stage2Tier) {
          

          // Keep the original name but use stage 2 data for images
          originalName = skinData.name
          effectiveSkinId = stage2Tier.id.toString()

          // Create a modified skin data object with stage 2 paths
          effectiveSkinData = {
            ...skinData,
            splashPath: stage2Tier.splashPath,
            uncenteredSplashPath: stage2Tier.uncenteredSplashPath,
            tilePath: stage2Tier.tilePath,
            loadScreenPath: stage2Tier.loadScreenPath,
            splashVideoPath: stage2Tier.splashVideoPath
          }

          
        }
      }

      // Get champion name from the base skin mapping
      const championName = this.getChampionNameFromSkin(effectiveSkinData, championNameMap, rawData)
      const championKey = this.normalizeChampionKey(championName)

      // Extract skin number from effective skin ID (handle both 2-digit and 3-digit cases)
      let skinNumber: number

      // Handle special cases like Master Yi skin 106
      if (championKey === 'masteryi' && effectiveSkinId.endsWith('106')) {
        skinNumber = 106
      } else if (effectiveSkinId.length >= 3 && parseInt(effectiveSkinId.slice(-3)) >= 100) {
        // For other 3-digit skin numbers (100+)
        skinNumber = parseInt(effectiveSkinId.slice(-3))
      } else {
        // For regular 2-digit skin numbers
        skinNumber = parseInt(effectiveSkinId.slice(-2)) || 0
      }

      // Extract champion ID from skin ID
      const championId = Math.floor(parseInt(effectiveSkinId) / 1000)

      // Get LOLDB skin data for enhanced information
      const loldbSkinData = this.findLoldbSkinData(championId, skinNumber, loldbData)

      // Normalize rarity
      const normalizedRarity = this.normalizeRarity(effectiveSkinData.rarity)



      // Get price, release date, and itemId from store data, with fallback to rarity-based pricing
      let price: string
      let releaseDate: string | null = null
      let itemId: number | null = null

      // Get price, release date, and itemId from store data, with LOLDB fallback for price and release date
      const storeInfo = this.getSkinPriceAndDate(originalName, storeData, normalizedRarity, effectiveSkinId)
      itemId = storeInfo.itemId

      // Use store price if available, otherwise fallback to LOLDB price
      if (storeInfo.price) {
        price = storeInfo.price
      } else if (loldbSkinData?.cost) {
        // Use LOLDB cost as fallback for all skin types
        // Special case for URFWick skin (ID 19009) - uses Blue Essence instead of RP
        if (parseInt(effectiveSkinId) === 19009) {
          price = `${loldbSkinData.cost} BE`
        } else if (normalizedRarity === 'Exalted') {
          // Exalted skins use Ancient Spark (AS) currency instead of RP
          price = `${loldbSkinData.cost} AS`
        } else if (normalizedRarity === 'Transcendent') {
          // Transcendent skins like Risen Legend use RP
          price = `${loldbSkinData.cost} RP`
        } else if (normalizedRarity === 'Mythic' && loldbSkinData.cost > 10000) {
          // Special high-cost skins like Risen Legend use RP despite being Mythic rarity
          price = `${loldbSkinData.cost} RP`
        } else if (normalizedRarity === 'Mythic') {
          // Regular Mythic skins use Mythic Essence (handled separately)
          price = 'N/A'
        } else {
          price = `${loldbSkinData.cost} RP`
        }
      } else if (loldbSkinData?.distribution) {
        // Third fallback: use distribution field to extract price and currency
        const distributionData = this.parseDistribution(loldbSkinData.distribution)
        if (distributionData) {
          const currencyAbbr = this.getCurrencyAbbreviation(distributionData.currency)
          price = `${distributionData.price} ${currencyAbbr}`
        } else {
          price = 'N/A'
        }
      } else {
        price = 'N/A'
      }

      // Use store release date if available, otherwise fallback to LOLDB release date
      if (storeInfo.releaseDate) {
        releaseDate = storeInfo.releaseDate
      } else if (loldbSkinData?.release) {
        try {
          const date = new Date(loldbSkinData.release)
          releaseDate = date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          })
        } catch (error) {
          console.warn(`⚠️ Failed to parse LOLDB release date for ${originalName}:`, error)
          releaseDate = null
        }
      }

      // Generate image URLs using the new Community Dragon structure
      // Check if this is a year variant and should use original skin's image
      const imageUrl = this.generateNewImageUrl(effectiveSkinData, championKey, effectiveSkinId, originalSkinMap, rawData)
      const splashUrl = this.generateNewSplashUrl(effectiveSkinData, championKey, effectiveSkinId, originalSkinMap, rawData)

      // Generate video URL for animated skins (Exalted, Ultimate, Transcendent)
      const animatedRarities = ['Exalted', 'Ultimate', 'Transcendent']
      const videoUrl = animatedRarities.includes(normalizedRarity) ? this.generateVideoUrl(effectiveSkinData, championKey, effectiveSkinId) : undefined

      // Debug logging for animated skins
      if (animatedRarities.includes(normalizedRarity)) {
      }

      // Process skin lines
      const skinLines: string[] = []
      if (effectiveSkinData.skinLines && Array.isArray(effectiveSkinData.skinLines)) {
        effectiveSkinData.skinLines.forEach(skinLine => {
          const skinLineName = skinLinesData[skinLine.id]
          if (skinLineName) {
            skinLines.push(skinLineName)
          }
        })
      }

      // Determine skin features based on LOLDB data
      // If skin exists in LOLDB but field is null → false (No)
      // If skin doesn't exist in LOLDB → null (N/A)
      const hasNewEffects = loldbSkinData
        ? loldbSkinData.neweffects === true
        : null // N/A when skin not found in LOLDB

      const hasNewAnimations = loldbSkinData
        ? loldbSkinData.newanimations === true
        : null // N/A when skin not found in LOLDB

      const hasNewRecall = loldbSkinData
        ? loldbSkinData.newrecall === true
        : null // N/A when skin not found in LOLDB

      const hasNewVoiceLines = loldbSkinData
        ? loldbSkinData.newvoice === true
        : null // N/A when skin not found in LOLDB

      const hasChromas = !!(effectiveSkinData.chromas && effectiveSkinData.chromas.length > 0)

      // Determine availability status
      // If skin exists in LOLDB → use availability value (even if it's a specific status)
      // If skin doesn't exist in LOLDB → null (N/A)
      const availability = loldbSkinData?.availability || null // N/A when skin not found in LOLDB

      // Determine loot eligibility
      // If skin exists in LOLDB and looteligible is null → false (No)
      // If skin exists in LOLDB and looteligible is true → true (Yes)
      // If skin doesn't exist in LOLDB → null (N/A)
      const lootEligible = loldbSkinData
        ? loldbSkinData.looteligible === true
        : null // N/A when skin not found in LOLDB

      // Get skin set name (first skin line or fallback)
      const set = skinLines.length > 0 ? skinLines[0] : undefined

      const processedSkin: ProcessedSkinData = {
        id: parseInt(effectiveSkinId),
        name: originalName, // Use original name for Transcendent skins
        champion: championName,
        championKey: championKey,
        tier: normalizedRarity,
        rarity: effectiveSkinData.rarity,
        price: price,
        image: imageUrl,
        splashArt: splashUrl,
        videoUrl: videoUrl,
        isLegacy: effectiveSkinData.isLegacy,
        isBase: effectiveSkinData.isBase,
        skinType: effectiveSkinData.skinType,
        contentId: effectiveSkinData.contentId,
        skinNumber: skinNumber,
        skinLines: skinLines,
        // New enhanced fields
        description: effectiveSkinData.description,
        featuresText: effectiveSkinData.featuresText,
        hasNewEffects: hasNewEffects,
        hasNewAnimations: hasNewAnimations,
        hasNewRecall: hasNewRecall,
        hasChromas: hasChromas,
        set: set,
        releaseDate: releaseDate || undefined, // From store API or undefined
        availability: availability,
        lootEligible: lootEligible,
        lastDiscount: undefined, // Will be fetched separately when needed
        itemId: itemId || undefined, // Store item ID for sales API
        questSkinInfo: effectiveSkinData.questSkinInfo, // Quest skin information
        newVoiceLines: hasNewVoiceLines // New Voice Lines field from LOLDB
      }

      processedSkins.push(processedSkin)
    }

    // Sort by champion name, then by skin name
    processedSkins.sort((a, b) => {
      if (a.champion !== b.champion) {
        return a.champion.localeCompare(b.champion)
      }
      return a.name.localeCompare(b.name)
    })

    return processedSkins
  }

  /**
   * Create a map of champion names from base skins
   */
  private createChampionNameMap(rawData: CommunityDragonSkinsResponse): Record<string, string> {
    const championNameMap: Record<string, string> = {}

    for (const [skinId, skinData] of Object.entries(rawData)) {
      if (skinData.isBase && skinData.name) {
        // Extract champion ID from skin ID (first digits before last three zeros)
        const championId = Math.floor(parseInt(skinId) / 1000)
        championNameMap[championId.toString()] = skinData.name
      }
    }

    return championNameMap
  }

  /**
   * Get champion name from skin data using base skin mapping
   */
  private getChampionNameFromSkin(
    skinData: CommunityDragonSkin,
    championNameMap: Record<string, string>,
    rawData: CommunityDragonSkinsResponse
  ): string {
    // Extract champion ID from skin ID
    const skinId = skinData.id.toString()
    const championId = Math.floor(parseInt(skinId) / 1000)

    // Try to get champion name from the base skin mapping
    if (championNameMap[championId.toString()]) {
      return championNameMap[championId.toString()]
    }

    // Fallback: extract from skin name (first word before space)
    const words = skinData.name.split(' ')
    return words[0] || skinData.name
  }

  /**
   * Normalize champion name to champion key for URL generation
   */
  private normalizeChampionKey(championName: string): string {
    // Special cases for champions that need specific key mappings
    const specialCases: Record<string, string> = {
      'Nunu & Willump': 'nunu',
      'Lee Sin': 'leesin',
      'Master Yi': 'masteryi',
      'Miss Fortune': 'missfortune',
      'Twisted Fate': 'twistedfate',
      'Xin Zhao': 'xinzhao',
      'Aurelion Sol': 'aurelionsol',
      'Jarvan IV': 'jarvaniv',
      'Dr. Mundo': 'drmundo',
      'Tahm Kench': 'tahmkench',
      'Cho\'Gath': 'chogath',
      'Kha\'Zix': 'khazix',
      'Kai\'Sa': 'kaisa',
      'Kog\'Maw': 'kogmaw',
      'Rek\'Sai': 'reksai',
      'Vel\'Koz': 'velkoz',
      'Bel\'Veth': 'belveth'
    }

    // Check if it's a special case
    if (specialCases[championName]) {
      return specialCases[championName]
    }

    // Default: convert to lowercase and remove non-alphabetic characters
    return championName.toLowerCase().replace(/[^a-z]/g, '')
  }

  /**
   * Create a map of original skins for year variants
   * Maps year variant skin IDs to their original skin data
   */
  private createOriginalSkinMap(rawData: CommunityDragonSkinsResponse): Record<string, { id: string, splashPath: string }> {
    const originalSkinMap: Record<string, { id: string, splashPath: string }> = {}
    const skinsByChampion: Record<string, Array<{ id: string, name: string, splashPath: string }>> = {}

    // Group skins by champion
    for (const [skinId, skinData] of Object.entries(rawData)) {
      if (skinData.isBase) continue

      const championId = Math.floor(parseInt(skinId) / 1000)
      const championKey = championId.toString()

      if (!skinsByChampion[championKey]) {
        skinsByChampion[championKey] = []
      }

      skinsByChampion[championKey].push({
        id: skinId,
        name: skinData.name,
        splashPath: skinData.splashPath || ''
      })
    }

    // Find year variants and map them to original skins
    for (const championSkins of Object.values(skinsByChampion)) {
      for (const skin of championSkins) {
        // Check if this skin has a year suffix (e.g., "(2022)", "(2023)", etc.)
        const yearMatch = skin.name.match(/^(.+?)\s*\((\d{4})\)$/)

        if (yearMatch) {
          const baseName = yearMatch[1].trim()

          // Find the original skin with the same base name
          const originalSkin = championSkins.find(s =>
            s.name === baseName && s.id !== skin.id
          )

          if (originalSkin) {
            // Map the year variant to use the original skin's ID and splashPath
            originalSkinMap[skin.id] = {
              id: originalSkin.id,
              splashPath: originalSkin.splashPath
            }
          }
        }
      }
    }

    return originalSkinMap
  }



  /**
   * Generate new image URL using the Community Dragon structure
   */
  private generateNewImageUrl(
    skinData: CommunityDragonSkin,
    championKey: string,
    skinId: string,
    originalSkinMap: Record<string, { id: string, splashPath: string }>,
    rawData: CommunityDragonSkinsResponse
  ): string {
    // Use splashPath directly from Community Dragon skins.json
    const splashPath = skinData.splashPath
    if (!splashPath) {
      console.warn(`No splashPath found for skin ${skinId}`)
      return '/placeholder.svg'
    }

    // Extract the path after "/ASSETS/" and convert to lowercase
    const assetsIndex = splashPath.indexOf('/ASSETS/')
    if (assetsIndex === -1) {
      console.warn(`Invalid splashPath format for skin ${skinId}: ${splashPath}`)
      return '/placeholder.svg'
    }

    const pathAfterAssets = splashPath.substring(assetsIndex + 1) // +1 to remove the leading slash
    const lowercasePath = pathAfterAssets.toLowerCase()

    // Construct the final Community Dragon URL
    const baseUrl = 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default'
    const finalUrl = `${baseUrl}/${lowercasePath}`

    return finalUrl
  }

  /**
   * Generate new splash URL using the Community Dragon structure
   */
  private generateNewSplashUrl(
    skinData: CommunityDragonSkin,
    championKey: string,
    skinId: string,
    originalSkinMap: Record<string, { id: string, splashPath: string }>,
    rawData: CommunityDragonSkinsResponse
  ): string {
    // Use the same method as image URL generation
    return this.generateNewImageUrl(skinData, championKey, skinId, originalSkinMap, rawData)
  }

  /**
   * Generate video URL for animated skins using splashVideoPath
   */
  private generateVideoUrl(
    skinData: CommunityDragonSkin,
    championKey: string,
    skinId: string
  ): string | undefined {
    if (!skinData.splashVideoPath) {
      return undefined
    }

    // Extract the filename from splashVideoPath
    // Example: "/lol-game-data/assets/ASSETS/Characters/Jinx/Skins/Skin60/AnimatedSplash/Jinx_Skin60_centered.SKINS_Jinx_Skin60.webm"
    const pathParts = skinData.splashVideoPath.split('/')
    let filename = pathParts[pathParts.length - 1]

    if (!filename) {
      return undefined
    }

    // Extract skin number from skinId (last 2 digits, but handle 3-digit cases)
    let skinNumber = skinId.slice(-2)

    // Handle special cases like Master Yi skin 106
    if (championKey === 'masteryi' && skinId.endsWith('106')) {
      skinNumber = '106'
    } else if (skinId.length >= 3 && parseInt(skinId.slice(-3)) >= 100) {
      // For other 3-digit skin numbers
      skinNumber = skinId.slice(-3)
    }



    // Construct the Community Dragon URL:
    // https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/assets/characters/{championName}/skins/skin{skinNumber}/animatedsplash/{filename}
    const baseUrl = 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default'
    const videoUrl = `${baseUrl}/assets/characters/${championKey}/skins/skin${skinNumber}/animatedsplash/${filename}`.toLowerCase()

    return videoUrl
  }



  /**
   * Normalize rarity from Community Dragon format to our standard format
   */
  private normalizeRarity(rarity: string): string {
    const rarityMap: Record<string, string> = {
      'kNoRarity': 'Regular',
      'kRare': 'Regular', 
      'kEpic': 'Epic',
      'kLegendary': 'Legendary',
      'kUltimate': 'Ultimate',
      'kMythic': 'Mythic',
      'kExalted': 'Exalted',
      'kTranscendent': 'Transcendent'
    }

    return rarityMap[rarity] || 'Regular'
  }



  /**
   * Generate image URL for skin
   */
  private generateImageUrl(skinData: CommunityDragonSkin): string {
    // Use tile path if available, otherwise use splash path
    if (skinData.tilePath) {
      // Remove leading slash if present and ensure proper casing
      const cleanPath = skinData.tilePath.startsWith('/') ? skinData.tilePath.slice(1) : skinData.tilePath
      return `https://raw.communitydragon.org/latest/game/${cleanPath}`
    }

    if (skinData.splashPath) {
      // Remove leading slash if present and ensure proper casing
      const cleanPath = skinData.splashPath.startsWith('/') ? skinData.splashPath.slice(1) : skinData.splashPath
      return `https://raw.communitydragon.org/latest/game/${cleanPath}`
    }

    // Fallback to a placeholder
    return '/placeholder.svg'
  }

  /**
   * Generate splash art URL for skin
   */
  private generateSplashUrl(skinData: CommunityDragonSkin): string {
    if (skinData.splashPath) {
      // Remove leading slash if present and ensure proper casing
      const cleanPath = skinData.splashPath.startsWith('/') ? skinData.splashPath.slice(1) : skinData.splashPath
      return `https://raw.communitydragon.org/latest/game/${cleanPath}`
    }

    if (skinData.uncenteredSplashPath) {
      // Remove leading slash if present and ensure proper casing
      const cleanPath = skinData.uncenteredSplashPath.startsWith('/') ? skinData.uncenteredSplashPath.slice(1) : skinData.uncenteredSplashPath
      return `https://raw.communitydragon.org/latest/game/${cleanPath}`
    }

    // Fallback to tile path or placeholder
    return this.generateImageUrl(skinData)
  }

  /**
   * Fetch skin store data from the new API
   */
  private async getSkinStoreData(): Promise<SkinStoreData[]> {
    const cacheKey = 'skin-store-data'
    const cached = this.skinStoreCache.get(cacheKey)

    if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
      
      return cached.data
    }

    try {
      
      const response = await fetch(this.SKIN_STORE_API_URL)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: Failed to fetch skin store data`)
      }

      const responseData = await response.json()
      const data: SkinStoreData[] = responseData.data?.catalog || []

      // Filter only skin items (not champions or chromas)
      const skinItems = data.filter(item =>
        item.inventoryType === 'CHAMPION_SKIN' &&
        item.subInventoryType !== 'RECOLOR' // Exclude chromas
      )

      // Cache the data
      this.skinStoreCache.set(cacheKey, {
        data: skinItems,
        timestamp: Date.now()
      })

      
      return skinItems

    } catch (error) {
      console.warn('⚠️ Failed to fetch skin store data:', error)
      return []
    }
  }

  /**
   * Fetch LOLDB skin data for enhanced skin information
   */
  private async getLoldbSkinData(): Promise<LoldbApiResponse> {
    const cacheKey = 'loldb-skins'
    const cached = this.loldbCache.get(cacheKey)

    // Return cached data if it's still valid
    if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
      return cached.data
    }

    try {
      const response = await fetch(this.LOLDB_SKINS_API_URL)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: Failed to fetch LOLDB skin data`)
      }

      const data: LoldbApiResponse = await response.json()

      // Cache the data
      this.loldbCache.set(cacheKey, {
        data: data,
        timestamp: Date.now()
      })

      return data

    } catch (error) {
      console.warn('⚠️ Failed to fetch LOLDB skin data:', error)
      return { data: [] }
    }
  }

  /**
   * Generate LOLDB skin ID from champion ID and skin number
   * Format: ChampionID + padded SkinId (e.g., 266001 for Justicar Aatrox)
   */
  private generateLoldbSkinId(championId: number, skinNumber: number): number {
    // Convert skinNumber to 3-digit string with leading zeros
    const paddedSkinId = skinNumber.toString().padStart(3, '0')
    return parseInt(`${championId}${paddedSkinId}`)
  }

  /**
   * Parse distribution string to extract price and currency
   * Examples: "150000 Blue Essence", "32035 Mythic Essence", "10 Ancient Spark"
   */
  private parseDistribution(distribution: string): { price: number; currency: string } | null {
    if (!distribution || typeof distribution !== 'string') {
      return null
    }

    // Match pattern: number followed by currency name
    const match = distribution.match(/^(\d+)\s+(.+)$/)
    if (!match) {
      return null
    }

    const price = parseInt(match[1])
    const currency = match[2].trim()

    if (isNaN(price)) {
      return null
    }

    return { price, currency }
  }

  /**
   * Convert currency name from distribution to abbreviated form
   */
  private getCurrencyAbbreviation(currency: string): string {
    const currencyMap: Record<string, string> = {
      'Blue Essence': 'BE',
      'Mythic Essence': 'ME',
      'Ancient Spark': 'AS'
    }

    return currencyMap[currency] || 'RP' // Default to RP if unknown
  }

  /**
   * Find LOLDB skin data by champion ID and skin number
   */
  private findLoldbSkinData(championId: number, skinNumber: number, loldbData: LoldbApiResponse): LoldbSkinData | null {
    // Find the champion in LOLDB data
    const championData = loldbData.data.find(champ => champ.id === championId)
    if (!championData) {
      return null
    }

    // Find the skin by skinId (excluding Original skins)
    const skinData = championData.skins.find(skin =>
      skin.skinId === skinNumber && skin.name !== 'Original'
    )

    return skinData || null
  }

  /**
   * Get skin pricing, release date, and itemId from store data
   */
  private getSkinPriceAndDate(skinName: string, storeData: SkinStoreData[], tier?: string, skinId?: string): {
    price: string | null
    releaseDate: string | null
    itemId: number | null
  } {


    // Ensure storeData is an array
    if (!Array.isArray(storeData)) {
      console.warn('⚠️ Store data is not an array:', typeof storeData)
      return { price: null, releaseDate: null, itemId: null }
    }

    let storeEntry: SkinStoreData | undefined

    // IMPROVED: Try ID-based matching first (most reliable)
    if (skinId) {
      const numericSkinId = parseInt(skinId)
      storeEntry = storeData.find(entry => entry.itemId === numericSkinId)
    }

    // Fallback to exact name matching
    if (!storeEntry) {
      storeEntry = storeData.find(entry => entry.name === skinName)
    }

    // If exact match fails, try alternative matching for tiered skins
    if (!storeEntry) {
      // For K/DA Seraphine, try multiple variations
      if (skinName.includes('K/DA ALL OUT Seraphine')) {
        storeEntry = storeData.find(entry =>
          entry.name === 'K/DA ALL OUT Seraphine' ||
          entry.name.includes('K/DA ALL OUT Seraphine') ||
          entry.name === 'K/DA ALL OUT' ||
          entry.name.includes('K/DA Seraphine')
        )
      }

      // For other tiered skins, try matching the base name without tier suffixes
      if (!storeEntry) {
        const baseName = skinName.replace(/\s+(Indie|Rising Star|Superstar|Baddest)$/i, '')
        storeEntry = storeData.find(entry => entry.name === baseName)
      }

      // Try partial matching for complex skin names
      // FIXED: This was incorrectly matching Grand Reckoning Sion/Talon to Grand Reckoning Draven
      // because it only checked first 2 words ("Grand Reckoning") instead of champion name
      if (!storeEntry) {
        // Extract champion name from skin name (last word is usually the champion)
        const skinWords = skinName.split(' ')
        const championName = skinWords[skinWords.length - 1]

        // Only match if the champion name is also present in the store entry
        storeEntry = storeData.find(entry => {
          const entryWords = entry.name.toLowerCase().split(' ')
          const skinWordsLower = skinWords.map(w => w.toLowerCase())

          // Must contain the champion name AND match first 2 words of skin line
          return skinWordsLower.length >= 2 && entryWords.length >= 2 &&
                 entryWords.includes(championName.toLowerCase()) &&
                 skinWordsLower.slice(0, 2).every(word => entryWords.includes(word))
        })

      }
    }

    if (!storeEntry) {
      return { price: null, releaseDate: null, itemId: null }
    }

    // Exclude mythic, exalted, and transcendent skin prices - return null for these special skins
    let price: string | null = null
    if (tier !== 'Mythic' && tier !== 'Exalted' && tier !== 'Transcendent' && storeEntry.rp) {
      price = `${storeEntry.rp} RP`
    }

    // Format release date
    let releaseDate: string | null = null
    if (storeEntry.releaseDate) {
      try {
        const date = new Date(storeEntry.releaseDate)
        releaseDate = date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        })
      } catch (error) {
        console.warn(`⚠️ Failed to parse release date for ${skinName}:`, error)
      }
    }

    return { price, releaseDate, itemId: storeEntry.itemId }
  }

  /**
   * Fetch last discount information for a skin by itemId
   */
  public async getLastDiscount(itemId: number): Promise<string> {
    try {

      const response = await fetch(`/api/skin-sales/${itemId}`)

      if (!response.ok) {
        console.warn(`⚠️ Failed to fetch sales data for item ID ${itemId}: HTTP ${response.status}`)
        return 'N/A'
      }

      const salesData = await response.json()

      if (!salesData.success || !salesData.data) {
        return 'N/A'
      }

      const { cost, beforeSale, currency, percentOff, endDate } = salesData.data

      // Format the discount string
      // Example: "540 RP ~~1350~~ - 60% off - June 23, 2025"
      const formattedEndDate = new Date(endDate).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })

      return `${cost} ${currency} ~~${beforeSale}~~ - ${percentOff}% off - ${formattedEndDate}`

    } catch (error) {
      console.error(`❌ Error fetching last discount for item ID ${itemId}:`, error)
      return 'N/A'
    }
  }

  /**
   * Clear cache (useful for testing or manual refresh)
   */
  public clearCache(): void {
    this.cache.clear()
    this.skinLinesCache.clear()
    this.skinStoreCache.clear()

  }

  /**
   * Get cache statistics
   */
  public getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    }
  }

  /**
   * Check if a skin is a Transcendent skin based on its rarity
   */
  private isTranscendentSkin(skinData: CommunityDragonSkin): boolean {
    return this.normalizeRarity(skinData.rarity) === 'Transcendent'
  }

  /**
   * Determine if skin has new effects based on rarity and features
   */
  private determineHasNewEffects(rarity: string, featuresText?: string): boolean {
    // Epic and higher rarities typically have new effects
    const hasEffectsByRarity = ['Epic', 'Legendary', 'Ultimate', 'Mythic', 'Exalted', 'Transcendent'].includes(rarity)

    // Check features text for effect-related keywords
    const hasEffectsInFeatures = featuresText ?
      /new.*effect|effect|particle|vfx|visual/i.test(featuresText) : false

    return hasEffectsByRarity || hasEffectsInFeatures
  }

  /**
   * Determine if skin has new animations based on rarity and features
   */
  private determineHasNewAnimations(rarity: string, featuresText?: string): boolean {
    // Legendary and higher rarities typically have new animations
    const hasAnimationsByRarity = ['Legendary', 'Ultimate', 'Mythic', 'Exalted', 'Transcendent'].includes(rarity)

    // Check features text for animation-related keywords
    const hasAnimationsInFeatures = featuresText ?
      /new.*animation|animation|emote|gesture/i.test(featuresText) : false

    return hasAnimationsByRarity || hasAnimationsInFeatures
  }

  /**
   * Determine if skin has new recall animation based on rarity and features
   */
  private determineHasNewRecall(rarity: string, featuresText?: string): boolean {
    // Epic and higher rarities typically have new recall
    const hasRecallByRarity = ['Epic', 'Legendary', 'Ultimate', 'Mythic', 'Exalted', 'Transcendent'].includes(rarity)

    // Check features text for recall-related keywords
    const hasRecallInFeatures = featuresText ?
      /recall|back.*animation|teleport/i.test(featuresText) : false

    return hasRecallByRarity || hasRecallInFeatures
  }

  /**
   * Determine skin availability status
   */
  private determineAvailability(isLegacy: boolean, rarity: string): string {
    if (isLegacy) {
      return 'Legacy'
    }

    if (rarity === 'Mythic' || rarity === 'Exalted' || rarity === 'Transcendent') {
      return 'Limited'
    }

    return 'Available'
  }
}

// Export singleton instance
export const skinService = SkinService.getInstance()
