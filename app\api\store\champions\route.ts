import { NextRequest, NextResponse } from 'next/server'
import { championStoreService } from '@/lib/services/champion-store-service'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const championName = searchParams.get('name')
    const championId = searchParams.get('id')


    if (championName) {
      // Get specific champion by name
      const championData = await championStoreService.getChampionStoreData(championName)
      
      if (!championData) {
        return NextResponse.json({
          success: false,
          error: `Champion "${championName}" not found in store data`,
          championName
        }, { status: 404 })
      }

      return NextResponse.json({
        success: true,
        data: {
          champion: championData,
          pricing: {
            costBE: championData.ip,
            costRP: championData.rp,
            releaseDate: championStoreService.formatReleaseDate(championData.releaseDate)
          }
        }
      })
    }

    if (championId) {
      // Get specific champion by ID
      const id = parseInt(championId)
      if (isNaN(id)) {
        return NextResponse.json({
          success: false,
          error: 'Invalid champion ID provided'
        }, { status: 400 })
      }

      const championData = await championStoreService.getChampionStoreDataById(id)
      
      if (!championData) {
        return NextResponse.json({
          success: false,
          error: `Champion with ID "${id}" not found in store data`,
          championId: id
        }, { status: 404 })
      }

      return NextResponse.json({
        success: true,
        data: {
          champion: championData,
          pricing: {
            costBE: championData.ip,
            costRP: championData.rp,
            releaseDate: championStoreService.formatReleaseDate(championData.releaseDate)
          }
        }
      })
    }

    // Get all champions store data
    const allChampions = await championStoreService.getAllChampionStoreData()
    
    return NextResponse.json({
      success: true,
      data: {
        champions: allChampions,
        count: allChampions.length
      }
    })

  } catch (error) {
    console.error('❌ API: Failed to fetch champion store data:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch champion store data'
    }, { status: 500 })
  }
}

// Handle cache management operations
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action } = body

    switch (action) {
      case 'clear-cache':
        championStoreService.clearCache()
        return NextResponse.json({
          success: true,
          message: 'Champion store cache cleared successfully'
        })

      case 'get-stats':
        const stats = championStoreService.getCacheStats()
        return NextResponse.json({
          success: true,
          data: stats
        })

      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid action. Supported actions: clear-cache, get-stats'
        }, { status: 400 })
    }
  } catch (error) {
    console.error('❌ API: Failed to handle cache operation:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to handle cache operation'
    }, { status: 500 })
  }
}
