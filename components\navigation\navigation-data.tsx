import { FileText, Gamepad2 } from "lucide-react"
import Image from "next/image"

// Custom Icons Components
export const ItemsIcon = ({ className, size = 20 }: { className?: string; size?: number }) => (
  <div className={className} style={{
    width: `${size}px`,
    height: `${size}px`,
    maskImage: `url(https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/nav-icon-collections.svg)`,
    maskRepeat: 'no-repeat',
    maskPosition: 'center',
    maskSize: 'contain',
    backgroundColor: 'currentColor'
  }} />
)

export const ChampionsIcon = ({ className }: { className?: string }) => (
  <div className={className} style={{
    width: '20px',
    height: '20px',
    maskImage: `url(https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/champions_rewards.svg)`,
    maskRepeat: 'no-repeat',
    maskPosition: 'center',
    maskSize: 'contain',
    backgroundColor: 'currentColor'
  }} />
)

export const ShopIcon = ({ className }: { className?: string }) => (
  <div className={className} style={{
    width: '20px',
    height: '20px',
    maskImage: `url(https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/nav-icon-store.svg)`,
    maskRepeat: 'no-repeat',
    maskPosition: 'center',
    maskSize: 'contain',
    backgroundColor: 'currentColor'
  }} />
)

export const SkinsIcon = ({ className, size = 20 }: { className?: string; size?: number }) => (
  <div className={className} style={{
    width: `${size}px`,
    height: `${size}px`,
    maskImage: `url(https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/skins_rewards.svg)`,
    maskRepeat: 'no-repeat',
    maskPosition: 'center',
    maskSize: 'contain',
    backgroundColor: 'currentColor'
  }} />
)

export const ChromasIcon = ({ className }: { className?: string }) => (
  <Image
    src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/icons/chroma-icon.png"
    alt="Chromas"
    width={20}
    height={20}
    className={className}
  />
)

export const SeasonIcon = ({ className }: { className?: string }) => (
  <div className={className} style={{
    width: '20px',
    height: '20px',
    maskImage: `url(https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/ranked-mini-crests/diamond_tft.svg)`,
    maskRepeat: 'no-repeat',
    maskPosition: 'center',
    maskSize: 'contain',
    backgroundColor: 'currentColor'
  }} />
)



export const CurrencyConversionIcon = ({ className }: { className?: string }) => (
  <div className={className} style={{
    width: '20px',
    height: '20px',
    maskImage: `url(/CurrencyConversion.svg)`,
    maskRepeat: 'no-repeat',
    maskPosition: 'center',
    maskSize: 'contain',
    backgroundColor: 'currentColor'
  }} />
)

export const RankTrackerIcon = ({ className }: { className?: string }) => (
  <div className={className} style={{
    width: '20px',
    height: '20px',
    maskImage: `url(https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/ranked-mini-crests/iron.svg)`,
    maskRepeat: 'no-repeat',
    maskPosition: 'center',
    maskSize: 'contain',
    backgroundColor: 'currentColor'
  }} />
)

// Submenu Icon Component
export const SubmenuIcon = ({ src, alt, className }: { src: string; alt: string; className?: string }) => (
  <Image
    src={src}
    alt={alt}
    width={16}
    height={16}
    className={className}
  />
)

// Custom Discounts Icon Component with background and overlay icon
export const DiscountsIcon = ({ className }: { className?: string }) => (
  <div className={`relative ${className}`}>
    {/* Background sale badge */}
    <Image
      src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-collections/global/default/images/item-element/sale-badge.png"
      alt="Sale Badge"
      width={16}
      height={16}
      className="w-4 h-4"
    />
    {/* Clock icon overlay */}
    <Image
      src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/clock-icon-gold1.svg"
      alt="Clock"
      width={12}
      height={12}
      className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-3 h-3"
    />
  </div>
)

// Custom Mythic Shop Icon Component
export const MythicIcon = ({ className }: { className?: string }) => (
  <Image
    src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/mythic.svg"
    alt="Mythic Essence"
    width={32}
    height={32}
    className={className}
  />
)

// Navigation Data Structure
export interface NavigationItem {
  icon: React.ComponentType<{ className?: string }>
  label: string
  count?: string
  href: string
  submenu?: SubmenuItem[]
  isLive?: boolean
  isComingSoon?: boolean
}

export interface SubmenuItem {
  label: string
  href: string
  icon?: string
  customIcon?: string
  customAction?: string
  isLocked?: boolean
  isDisabled?: boolean
}

export interface FeaturedItem {
  title: string
  description: string
  href: string
  color: string
  borderColor: string
  hoverBorderColor: string
}

export const navigationData: NavigationItem[] = [
  {
    icon: ChampionsIcon,
    label: "Champions",
    count: "170",
    href: "/champions",
    submenu: [
      {
        label: "All Champions",
        href: "/champions",
      },
      { 
        label: "Free Champion Rotation",
        href: "/champions/free-rotation",
      },
    ],
  },
  {
    icon: SkinsIcon,
    label: "Skins",
    count: "1,800+",
    href: "/skins",
    submenu: [
      {
        label: "All Skins",
        href: "/skins",
        icon: "https://i.ibb.co/KcBL7QtM/skinline.webp"
      },
      {
        label: "Legacy",
        href: "/skins/rarity/legacy",
        icon: "https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/summoner-icon/icon-legacy.png"
      },
      {
        label: "Regular",
        href: "/skins/rarity/regular",
        icon: "https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-parties/global/default/summoner-icon-rare.png"
      },
      {
        label: "Epic",
        href: "/skins/rarity/epic",
        icon: "https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/cn-gem-3.png"
      },
      {
        label: "Legendary",
        href: "/skins/rarity/legendary",
        icon: "https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/legendary.png"
      },
      {
        label: "Ultimate",
        href: "/skins/rarity/ultimate",
        icon: "https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/ultimate.png"
      },
      {
        label: "Mythic",
        href: "/skins/rarity/mythic",
        icon: "https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/cn-gem-8.png"
      },
      {
        label: "Exalted",
        href: "/skins/rarity/exalted",
        icon: "https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/exalted.png"
      },
      {
        label: "Transcendent",
        href: "/skins/rarity/transcendent",
        icon: "https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/transcendent.png"
      },
      {
        label: "Chromas",
        href: "/skins/chromas",
        icon: "https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/icons/chroma-icon.png"
      },
    ],
  },
  {
    icon: ItemsIcon,
    label: "Items",
    count: "250+",
    href: "/items",
    submenu: [
      {
        label: "All Items",
        href: "/items"
      },
      {
        label: "Attack Damage",
        href: "/items?tags=Damage"
      },
      {
        label: "Ability Power",
        href: "/items?tags=SpellDamage"
      },
      {
        label: "Attack Speed",
        href: "/items?tags=AttackSpeed"
      },
    ],
  },

  {
    icon: ShopIcon,
    label: "Shop",
    count: "New",
    href: "/shop",
    submenu: [
      {
        label: "Current Shop",
        href: "/shop",
        icon: "https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg"
      },
      {
        label: "Discounts",
        href: "/shop/discounts",
        customIcon: "discounts"
      },
      {
        label: "Mythic Shop",
        href: "/shop/mythic",
        icon: "https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/mythic.svg"
      },
    ],
  },
  {
    icon: SeasonIcon,
    label: "Season",
    count: "S2",
    href: "/season",
    submenu: [
      {
        label: "Countdown",
        href: "/season/countdown"
      },
      {
        label: "Start & End Dates",
        href: "#",
        isLocked: true,
        isDisabled: true
      },
    ],
  },
  {
    icon: CurrencyConversionIcon,
    label: "Conversion Tool",
    count: "New",
    href: "/conversion-tool",
    submenu: [
      {
        label: "Riot Points to USD",
        href: "/conversion-tool",
        customAction: "rp-to-usd"
      },
      {
        label: "Ancient Sparks to RP",
        href: "/conversion-tool",
        customAction: "as-to-rp"
      },
      {
        label: "Mythic Essence to RP",
        href: "/conversion-tool",
        customAction: "me-to-rp"
      },
    ],
  },

  { icon: Gamepad2, label: "Rank Tracker", count: "Soon", href: "/rank-tracker", isComingSoon: true },
]

export const featuredData: FeaturedItem[] = [
  {
    title: "Mythic Shop",
    description: "Exclusive items",
    href: "/shop/mythic",
    color: "text-purple-300",
    borderColor: "border-purple-800/20",
    hoverBorderColor: "hover:border-purple-600/40"
  },
  {
    title: "Weekly Free Rotation",
    description: "20 champions this week",
    href: "/champions/free-rotation",
    color: "text-orange-300",
    borderColor: "border-orange-800/20",
    hoverBorderColor: "hover:border-orange-600/40"
  },
  {
    title: "Skin Sales",
    description: "Up to 60% off",
    href: "/shop/discounts",
    color: "text-amber-300",
    borderColor: "border-amber-800/20",
    hoverBorderColor: "hover:border-amber-600/40"
  }
]
