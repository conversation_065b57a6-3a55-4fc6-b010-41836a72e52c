"use client"

import * as React from "react"
import { useState } from "react"
import Image from "next/image"
import { ChevronLeft, ChevronRight } from "lucide-react"
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip"

import { Card, CardContent } from "@/components/ui/card"

// Sample skin data structure for demo
interface SkinData {
  name: string
  price: string
  rarity: string
  image: string
  splashArt: string
  inGameImage: string
  isBase: boolean
  hasInGameImage: boolean
  chromas: any[]
  skinNum: number
}

// Sample data for demonstration
const sampleSkins: SkinData[] = [
  {
    name: "Classic",
    price: "Free",
    rarity: "Regular",
    image: "https://ddragon.leagueoflegends.com/cdn/img/champion/loading/Jinx_0.jpg",
    splashArt: "https://ddragon.leagueoflegends.com/cdn/img/champion/splash/Jinx_0.jpg",
    inGameImage: "",
    isBase: true,
    hasInGameImage: false,
    chromas: [],
    skinNum: 0
  },
  {
    name: "Mafia Jinx",
    price: "975",
    rarity: "Epic",
    image: "https://ddragon.leagueoflegends.com/cdn/img/champion/loading/Jinx_1.jpg",
    splashArt: "https://ddragon.leagueoflegends.com/cdn/img/champion/splash/Jinx_1.jpg",
    inGameImage: "",
    isBase: false,
    hasInGameImage: false,
    chromas: [],
    skinNum: 1
  },
  {
    name: "Firecracker Jinx",
    price: "1350",
    rarity: "Epic",
    image: "https://ddragon.leagueoflegends.com/cdn/img/champion/loading/Jinx_2.jpg",
    splashArt: "https://ddragon.leagueoflegends.com/cdn/img/champion/splash/Jinx_2.jpg",
    inGameImage: "",
    isBase: false,
    hasInGameImage: false,
    chromas: [],
    skinNum: 2
  },
  {
    name: "Slayer Jinx",
    price: "1350",
    rarity: "Epic",
    image: "https://ddragon.leagueoflegends.com/cdn/img/champion/loading/Jinx_3.jpg",
    splashArt: "https://ddragon.leagueoflegends.com/cdn/img/champion/splash/Jinx_3.jpg",
    inGameImage: "",
    isBase: false,
    hasInGameImage: false,
    chromas: [],
    skinNum: 3
  },
  {
    name: "Star Guardian Jinx",
    price: "1350",
    rarity: "Epic",
    image: "https://ddragon.leagueoflegends.com/cdn/img/champion/loading/Jinx_4.jpg",
    splashArt: "https://ddragon.leagueoflegends.com/cdn/img/champion/splash/Jinx_4.jpg",
    inGameImage: "",
    isBase: false,
    hasInGameImage: false,
    chromas: [],
    skinNum: 4
  }
]

// Rarity icon mapping
function getRarityIconUrl(rarity: string): string | null {
  const iconMap: { [key: string]: string } = {
    'Regular': 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/summoner-icon-rare.png',
    'Legacy': 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/icon-legacy.png',
    'Epic': 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/cn-gem-3.png',
    'Legendary': 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/cn-gem-4.png',
    'Ultimate': 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/cn-gem-5.png',
    'Mythic': 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/cn-gem-8.png',
    'Exalted': 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/cn-gem-10.png',
    'Transcendent': 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/cn-gem-11.png'
  }
  return iconMap[rarity] || null
}

// Get rarity description for tooltip
function getRarityDescription(rarity: string) {
  const descriptions: Record<string, string> = {
    'Regular': 'Standard skin quality with basic visual changes',
    'Legacy': 'Limited-time skin no longer available for purchase',
    'Epic': 'High-quality skin with new model, textures, and effects',
    'Legendary': 'Premium skin with new voice lines and animations',
    'Ultimate': 'Evolving skin that changes throughout the game',
    'Mythic': 'Exclusive skin with unique prestige elements',
    'Exalted': 'Ultra-rare skin with exceptional quality',
    'Transcendent': 'Highest tier skin with transformative features'
  }
  return descriptions[rarity] || `${rarity} rarity skin`
}

export function CarouselDemo() {
  const [currentSkinIndex, setCurrentSkinIndex] = useState(0)
  const [isAnimating, setIsAnimating] = useState(false)

  const skins = sampleSkins
  const maxDisplaySkins = 9
  const actualSkinsCount = skins.length
  const displayCount = Math.min(actualSkinsCount, maxDisplaySkins)

  // Get visible skins for carousel display
  const getVisibleSkins = () => {
    const visibleIndices: (number | null)[] = []
    const centerPosition = Math.floor(displayCount / 2)

    if (actualSkinsCount < maxDisplaySkins) {
      // For champions with fewer than 9 skins, show actual skins centered with empty slots
      const emptySlots = maxDisplaySkins - actualSkinsCount
      const leftEmptySlots = Math.floor(emptySlots / 2)
      
      // Add left empty slots
      for (let i = 0; i < leftEmptySlots; i++) {
        visibleIndices.push(null)
      }
      
      // Add actual skins in sequential order
      for (let i = 0; i < actualSkinsCount; i++) {
        visibleIndices.push(i)
      }
      
      // Add right empty slots
      while (visibleIndices.length < maxDisplaySkins) {
        visibleIndices.push(null)
      }
    } else {
      // For champions with 9+ skins, show 9 skins with circular wrapping around current
      for (let i = 0; i < maxDisplaySkins; i++) {
        const offset = i - centerPosition
        let targetPosition = currentSkinIndex + offset

        // Handle circular wrapping
        if (targetPosition < 0) {
          targetPosition = actualSkinsCount + targetPosition
        } else if (targetPosition >= actualSkinsCount) {
          targetPosition = targetPosition - actualSkinsCount
        }

        visibleIndices.push(targetPosition)
      }
    }

    return visibleIndices
  }

  const visibleSkins = getVisibleSkins()
  const centerIndex = Math.floor(displayCount / 2)

  const handleSkinClick = async (targetIndex: number) => {
    if (targetIndex === currentSkinIndex || isAnimating) return
    
    setIsAnimating(true)
    setCurrentSkinIndex(targetIndex)
    
    // Reset animation state after a short delay
    setTimeout(() => setIsAnimating(false), 300)
  }

  const navigateCarousel = (direction: 'prev' | 'next') => {
    if (isAnimating) return
    
    const newIndex = direction === 'prev' 
      ? (currentSkinIndex - 1 + actualSkinsCount) % actualSkinsCount
      : (currentSkinIndex + 1) % actualSkinsCount
    
    handleSkinClick(newIndex)
  }

  return (
    <div className="relative mt-3" style={{ minHeight: '100px', paddingTop: '5px', paddingBottom: '0px' }}>
      <div className="flex items-center justify-center h-full" style={{ gap: '12px' }}>
        {/* Left Arrow - Circular */}
        <button
          onClick={() => navigateCarousel('prev')}
          disabled={isAnimating}
          className="w-10 h-10 rounded-full bg-orange-600/80 hover:bg-orange-500/90 border-2 border-orange-500/60 hover:border-orange-400 flex items-center justify-center transition-all duration-200 disabled:opacity-50 text-white shadow-lg backdrop-blur-sm"
        >
          <ChevronLeft size={20} className="text-white" />
        </button>

        {/* Skin carousel container */}
        <div className="relative overflow-visible" style={{ width: '720px', height: '120px' }}>
          <div
            className="relative"
            style={{
              height: '100px',
              padding: '30px 20px',
              width: '720px'
            }}
          >
            <div className="flex space-x-1 justify-center items-center" style={{ height: '64px' }}>
              {visibleSkins.map((skinIndex, position) => {
                if (skinIndex === null) {
                  // Empty slot with dotted border
                  return (
                    <div key={`empty-${position}`} className="flex justify-center items-center flex-shrink-0" style={{ width: '72px' }}>
                      <div className="w-16 h-16 rounded-lg border-2 border-dashed border-gray-500/80 bg-gray-800/40 backdrop-blur-sm flex items-center justify-center">
                        <div className="w-2 h-2 rounded-full bg-gray-500/60"></div>
                      </div>
                    </div>
                  )
                }

                const skin = skins[skinIndex]
                const isSelected = skinIndex === currentSkinIndex

                return (
                  <div key={skinIndex} className="flex justify-center items-center flex-shrink-0" style={{ width: '72px' }}>
                    <div className="relative">
                      <button
                        onClick={() => handleSkinClick(skinIndex)}
                        disabled={isAnimating}
                        className={`relative w-16 h-16 rounded-lg overflow-hidden border-2 transition-all duration-300 ${
                          isSelected 
                            ? 'border-orange-500 shadow-lg shadow-orange-500/50 scale-110' 
                            : 'border-gray-600 hover:border-orange-400 hover:scale-105'
                        } ${isAnimating ? 'pointer-events-none' : ''}`}
                      >
                        <Image
                          src={skin.image}
                          alt={skin.name}
                          width={64}
                          height={64}
                          className="w-full h-full object-cover object-top"
                          style={{ userSelect: 'none', pointerEvents: 'none' }}
                          draggable={false}
                        />
                      </button>

                      {/* Rarity icon */}
                      {getRarityIconUrl(skin.rarity) && (
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="absolute -bottom-1 -right-1 w-4 h-4 rounded-full bg-black/80 backdrop-blur-sm flex items-center justify-center border border-gray-600/50 cursor-help">
                              <Image
                                src={getRarityIconUrl(skin.rarity)!}
                                alt={skin.rarity}
                                width={12}
                                height={12}
                                className="w-3 h-3"
                                style={{ userSelect: 'none', pointerEvents: 'none' }}
                                draggable={false}
                              />
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <div className="text-center">
                              <div className="font-semibold text-orange-300">{skin.rarity}</div>
                              <div className="text-sm text-gray-300 mt-1">
                                {getRarityDescription(skin.rarity)}
                              </div>
                            </div>
                          </TooltipContent>
                        </Tooltip>
                      )}
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        </div>

        {/* Right Arrow - Circular */}
        <button
          onClick={() => navigateCarousel('next')}
          disabled={isAnimating}
          className="w-10 h-10 rounded-full bg-orange-600/80 hover:bg-orange-500/90 border-2 border-orange-500/60 hover:border-orange-400 flex items-center justify-center transition-all duration-200 disabled:opacity-50 text-white shadow-lg backdrop-blur-sm"
        >
          <ChevronRight size={20} className="text-white" />
        </button>
      </div>

      {/* Current skin info display */}
      <div className="mt-4 text-center">
        <h3 className="text-lg font-semibold text-white">{skins[currentSkinIndex].name}</h3>
        <p className="text-sm text-gray-300">
          {skins[currentSkinIndex].price === "Free" ? "Free" : `${skins[currentSkinIndex].price} RP`} • {skins[currentSkinIndex].rarity}
        </p>
      </div>
    </div>
  )
}
