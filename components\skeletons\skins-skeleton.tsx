import { Card, CardContent } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"

interface SkinsGridSkeletonProps {
  count?: number
  isClient?: boolean
}

export function SkinsGridSkeleton({ count = 60, isClient = false }: SkinsGridSkeletonProps) {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
      {Array.from({ length: count }).map((_, index) => (
        <div
          key={index}
          className="bg-gradient-to-br from-gray-900/60 to-gray-800/40 border border-purple-700/30 rounded-xl overflow-hidden relative h-64"
        >
          {/* Image Skeleton */}
          <Skeleton className="w-full h-full" />

          {/* Content overlay skeleton */}
          <div className="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/90 via-black/70 to-transparent">
            <Skeleton className="w-3/4 h-4 mb-2" />
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-1">
                <Skeleton className="w-4 h-4 rounded" />
                <Skeleton className="w-8 h-3" />
              </div>
              <Skeleton className="w-4 h-4 rounded" />
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}

export function SkinsPageSkeleton() {
  return (
    <div className="container mx-auto px-4 sm:px-6 md:px-8 py-8 max-w-full overflow-hidden">
        {/* Header Skeleton */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <Skeleton className="h-8 w-8 rounded" />
              <Skeleton className="h-10 w-32" />
            </div>
            {/* Mobile Search Button - Next to Title */}
            <div className="lg:hidden">
              <Skeleton className="h-8 w-8 rounded" />
            </div>
          </div>
          <Skeleton className="h-6 w-96" />
        </div>

        {/* Desktop Filters Skeleton - Hidden on mobile, shown on lg+ */}
        <div className="hidden lg:block mb-8 space-y-4">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search Bar */}
            <div className="relative lg:flex-1 lg:max-w-md">
              <Skeleton className="h-10 w-full" />
            </div>
            {/* Champion Filter */}
            <Skeleton className="h-10 w-full lg:w-48" />
            {/* Tier Filter */}
            <Skeleton className="h-10 w-full lg:w-48" />
            {/* Skin Lines Filter */}
            <Skeleton className="h-10 w-full lg:w-48" />
            {/* Reset Filters Button */}
            <Skeleton className="h-10 w-full lg:w-32" />
          </div>

          {/*
            NOTE: SelectedFiltersDisplay is intentionally NOT included in skeleton
            as it only appears when there are actual selected filters.
            This prevents layout shift since the skeleton should match the initial
            state (no filters selected).
          */}
        </div>

        {/* Results Count Skeleton */}
        <div className="mb-8">
          <Skeleton className="h-5 w-64" />
        </div>

        {/* Skins Grid Skeleton - Use responsive layout to prevent layout jump */}
        <SkinsGridSkeleton count={60} isClient={false} />

        {/* Quick Links Skeleton */}
        <div className="mt-12 grid grid-cols-1 gap-6 overflow-visible">
          <Card className="bg-gray-900/40 border-purple-700/20">
            <CardContent className="p-6">
              <Skeleton className="h-8 w-8 mb-3" />
              <Skeleton className="h-6 w-20 mb-2" />
              <Skeleton className="h-4 w-48" />
            </CardContent>
          </Card>
        </div>
      </div>
  )
}
