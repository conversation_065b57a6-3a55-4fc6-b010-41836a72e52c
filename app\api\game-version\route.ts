// API route for game version information

import { leagueApi } from '@/lib/api/league-client'
import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    
    
    // Get all available versions
    const versions = await leagueApi.getVersions()
    
    if (!versions || versions.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'No versions available'
      }, { status: 404 })
    }
    
    const currentVersion = versions[0]
    const previousVersions = versions.slice(1, 6) // Get last 5 previous versions
    
    
    
    return NextResponse.json({
      success: true,
      data: {
        current: currentVersion,
        previous: previousVersions,
        all: versions.slice(0, 20) // Limit to 20 most recent versions
      },
      timestamp: new Date().toISOString()
    }, {
      headers: {
        'Cache-Control': 'public, s-maxage=1800, stale-while-revalidate=3600',
        'CDN-Cache-Control': 'public, s-maxage=1800',
        'Vercel-CDN-Cache-Control': 'public, s-maxage=1800'
      }
    })
    
  } catch (error) {
    console.error('❌ Error fetching game version:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch game version',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { 
      status: 500,
      headers: {
        'Cache-Control': 'no-cache'
      }
    })
  }
}
