#!/usr/bin/env node

// Test script to verify your Riot API key works

const https = require('https');

const API_KEY = 'RGAPI-0bb9070d-cfa5-427f-ad88-545d6e0fe7b9';
const REGION = 'na1';

function testRiotAPI() {
  return new Promise((resolve, reject) => {
    const url = `https://${REGION}.api.riotgames.com/lol/platform/v3/champion-rotations`;
    
    const options = {
      hostname: `${REGION}.api.riotgames.com`,
      path: '/lol/platform/v3/champion-rotations',
      method: 'GET',
      headers: {
        'X-Riot-Token': API_KEY,
        'User-Agent': 'LoLDB/1.0'
      }
    };

    console.log('🔑 Testing Riot API key...');
    console.log(`📍 Region: ${REGION}`);
    console.log(`🌐 URL: ${url}`);
    console.log('');

    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const result = {
            status: res.statusCode,
            headers: res.headers,
            data: data ? JSON.parse(data) : null
          };
          resolve(result);
        } catch (error) {
          reject(new Error(`Failed to parse response: ${error.message}`));
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.end();
  });
}

async function runTest() {
  try {
    const result = await testRiotAPI();
    
    console.log('📊 API Response:');
    console.log('=' .repeat(50));
    
    if (result.status === 200) {
      console.log('✅ SUCCESS! Your API key is working correctly.');
      console.log('');
      console.log('📋 Free Champion Rotation Data:');
      console.log(`   Free Champions: ${result.data.freeChampionIds.length} champions`);
      console.log(`   Champion IDs: [${result.data.freeChampionIds.slice(0, 5).join(', ')}...]`);
      console.log('');
      console.log(`   New Player Champions: ${result.data.freeChampionIdsForNewPlayers.length} champions`);
      console.log(`   New Player IDs: [${result.data.freeChampionIdsForNewPlayers.join(', ')}]`);
      console.log(`   Max New Player Level: ${result.data.maxNewPlayerLevel}`);
      console.log('');
      console.log('🎉 Your LoLDB will now show LIVE free champion rotation data!');
      
    } else if (result.status === 401) {
      console.log('❌ UNAUTHORIZED: API key is invalid or expired');
      console.log('   Please check your API key at: https://developer.riotgames.com/');
      
    } else if (result.status === 429) {
      console.log('⚠️  RATE LIMITED: Too many requests');
      console.log('   Development keys allow 100 requests per 2 minutes');
      console.log('   Wait a moment and try again');
      
    } else if (result.status === 403) {
      console.log('❌ FORBIDDEN: API key may not have access to this endpoint');
      console.log('   Make sure your API key has the correct permissions');
      
    } else {
      console.log(`❌ ERROR: HTTP ${result.status}`);
      console.log('Response:', result.data);
    }
    
  } catch (error) {
    console.log('❌ NETWORK ERROR:', error.message);
    console.log('');
    console.log('Possible causes:');
    console.log('- Internet connection issues');
    console.log('- Riot API servers are down');
    console.log('- Firewall blocking the request');
  }
  
  console.log('');
  console.log('🔧 Next Steps:');
  console.log('1. Start your development server: npm run dev');
  console.log('2. Visit: http://localhost:3000/api/free-rotation');
  console.log('3. Check: http://localhost:3000/champions/free-rotation');
}

runTest();
