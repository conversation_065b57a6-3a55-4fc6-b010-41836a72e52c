import { chromaService } from '@/lib/services/chroma-service'
import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    const refresh = searchParams.get('refresh') === 'true'

    

    // Clear cache if refresh is requested
    if (refresh) {
      chromaService.clearCache()
      
    }

    const result = await chromaService.getPaginatedChromas(page, limit)
    
    
    
    return NextResponse.json({
      success: true,
      data: result.chromas,
      pagination: {
        currentPage: result.currentPage,
        total: result.total,
        hasMore: result.hasMore,
        limit: limit
      },
      timestamp: new Date().toISOString()
    }, {
      headers: {
        'Cache-Control': 'public, s-maxage=3600, stale-while-revalidate=7200', // 1h cache, 2h stale
        'CDN-Cache-Control': 'public, s-maxage=3600',
        'Vercel-CDN-Cache-Control': 'public, s-maxage=3600'
      }
    })

  } catch (error) {
    console.error('❌ Failed to fetch paginated chromas data:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch chromas data',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}
