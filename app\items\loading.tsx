import SharedLayout from "@/components/shared-layout"
import { ItemGridSkeleton } from "@/components/items/item-skeleton"
import { FiltersSkeleton } from "@/components/items/filters-skeleton"
import { Sword } from "lucide-react"

export default function Loading() {
  return (
    <SharedLayout>
      <div className="container mx-auto px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-4">
            <Sword className="h-8 w-8 text-blue-400" />
            <h1 className="text-4xl font-bold text-white">Items</h1>
            <div className="h-6 w-6 bg-blue-400/20 rounded-full animate-pulse"></div>
          </div>
          <p className="text-gray-300 text-lg">Discover all items, builds, and equipment in League of Legends</p>
        </div>

        {/* Search and Filters Skeleton */}
        <FiltersSkeleton />

        {/* Filter Actions Skeleton */}
        <div className="flex items-center justify-between mb-8">
          <div className="h-5 bg-gray-700/50 rounded w-48 animate-pulse"></div>
        </div>

        {/* Items Grid Skeleton */}
        <div className="min-h-[60vh]">
          <ItemGridSkeleton />
        </div>
      </div>
    </SharedLayout>
  )
}
