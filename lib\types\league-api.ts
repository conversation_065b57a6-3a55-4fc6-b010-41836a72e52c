// TypeScript interfaces for League of Legends API data

export interface ChampionListItem {
  id: string
  name: string
  title: string
  difficulty: number
  image: string
  slug: string
  tags: string[]
  role?: string
  class?: string
  championId?: number
  recommendedPositions?: string[]
}

export interface ChampionWithStoreData extends ChampionListItem {
  // Override optional fields to be required for store champions
  role: string
  class: string
  championId: number
  recommendedPositions: string[]
  storeData?: {
    releaseDate: number
    ip: number // Blue Essence
    rp: number
  }
}

export interface ChampionDetailed {
  id: string
  key: string
  name: string
  title: string
  blurb: string
  lore: string
  info: {
    attack: number
    defense: number
    magic: number
    difficulty: number
  }
  stats: {
    hp: number
    hpperlevel: number
    mp: number
    mpperlevel: number
    movespeed: number
    armor: number
    armorperlevel: number
    spellblock: number
    spellblockperlevel: number
    attackrange: number
    hpregen: number
    hpregenperlevel: number
    mpregen: number
    mpregenperlevel: number
    crit: number
    critperlevel: number
    attackdamage: number
    attackdamageperlevel: number
    attackspeedperlevel: number
    attackspeed: number
  }
  spells: ChampionSpell[]
  passive: ChampionPassive
  skins: Champion<PERSON>kin[]
  tags: string[]
  partype: string
  image: {
    full: string
    sprite: string
    group: string
    x: number
    y: number
    w: number
    h: number
  }
}

export interface ChampionSpell {
  id: string
  name: string
  description: string
  tooltip: string
  leveltip?: {
    label: string[]
    effect: string[]
  }
  maxrank: number
  cooldown: number[]
  cooldownBurn: string
  cost: number[]
  costBurn: string
  datavalues: any
  effect: (number[] | null)[]
  effectBurn: (string | null)[]
  vars: any[]
  costType: string
  maxammo: string
  range: number[]
  rangeBurn: string
  image: {
    full: string
    sprite: string
    group: string
    x: number
    y: number
    w: number
    h: number
  }
  resource: string
}

export interface ChampionPassive {
  name: string
  description: string
  image: {
    full: string
    sprite: string
    group: string
    x: number
    y: number
    w: number
    h: number
  }
}

export interface ChampionSkin {
  id: string
  num: number
  name: string
  chromas: boolean
}

// Community Dragon Skin Data Interfaces
export interface CommunityDragonSkinTier {
  id: number
  name: string
  stage: number
  description: string
  splashPath: string
  uncenteredSplashPath: string
  tilePath: string
  loadScreenPath: string
  loadScreenVintagePath?: string
  shortName: string
  splashVideoPath?: string
  previewVideoUrl?: string | null
  collectionSplashVideoPath?: string | null
  collectionCardHoverVideoPath?: string | null
}

export interface CommunityDragonQuestSkinInfo {
  name: string
  productType: string
  collectionDescription: string
  descriptionInfo: any[]
  splashPath: string
  uncenteredSplashPath: string
  tilePath: string
  collectionCardPath: string
  tiers: CommunityDragonSkinTier[]
}

export interface CommunityDragonSkinLine {
  id: number
}

// Community Dragon Chroma Data Interfaces
export interface CommunityDragonChroma {
  id: number
  name: string
  chromaPath: string
  contentId: string
  colors: string[]
  descriptions: Array<{ region: string; description: string }>
  rarities: Array<{ region: string; rarity: number }>
  skinClassification?: string
}

export interface CommunityDragonNexusFinisher {
  name: string
  itemId: number
  contentId: string
  translatedName: string
  translatedDescription: string
  iconPath: string
  splashPath: string
  videoPath: string
}

export interface CommunityDragonSummonerIcon {
  id: number
  contentId: string
  title: string
  yearReleased: number
  isLegacy: boolean
  imagePath: string
  descriptions: {
    region: string
    description: string
  }[]
  rarities: {
    region: string
    rarity: number
  }[]
  disabledRegions: string[]
  esportsTeam?: string
  esportsRegion?: string
  esportsEvent?: string
}

export interface CommunityDragonEmote {
  id: number
  name: string
  contentId: string
  inventoryIcon: string
}

export interface CommunityDragonSkin {
  id: number
  contentId: string
  isBase: boolean
  name: string
  skinClassification: string
  splashPath: string
  uncenteredSplashPath: string
  tilePath: string
  loadScreenPath: string
  loadScreenVintagePath?: string
  splashVideoPath?: string
  previewVideoUrl?: string
  collectionSplashVideoPath?: string
  collectionCardHoverVideoPath?: string
  skinType: string
  rarity: string
  isLegacy: boolean
  featuresText?: string
  chromaPath?: string
  chromas?: CommunityDragonChroma[]
  emblems?: any
  regionRarityId: number
  rarityGemPath?: string
  skinLines: CommunityDragonSkinLine[] | null
  questSkinInfo?: CommunityDragonQuestSkinInfo
  description?: string
}

export interface CommunityDragonSkinsResponse {
  [key: string]: CommunityDragonSkin
}

// Processed Chroma Data Interface
export interface ProcessedChromaData {
  id: number
  name: string
  champion: string
  championKey: string
  skinName: string
  skinId: number
  skinTier: string
  chromaRarity: string
  color: string
  colors: string[]
  image: string
  chromaPath: string
  isDefault: boolean
  price: string // Usually "290 RP" or "Bundle Only"
  availability: string // Available, Bundle Only, Event Only, etc.
  set?: string // Skin line/set name
  releaseDate?: string
  description?: string // Chroma description from Community Dragon API
  contentId?: string // Content ID for mythic shop integration
}

// Enhanced Skin Data with Rarity Information
export interface EnhancedSkinData {
  name: string
  price: string
  rarity: string
  rarityTier: string // Normalized rarity (Regular, Epic, Legendary, etc.)
  isLegacy: boolean
  isBase: boolean
  image: string
  splashArt: string
  inGameImage: string
  hasInGameImage: boolean
  chromas: any[]
  skinNum: number
  communityDragonId?: number
}

export interface ItemData {
  id: string
  name: string
  description: string
  plaintext: string
  into?: string[]
  from?: string[]
  image: {
    full: string
    sprite: string
    group: string
    x: number
    y: number
    w: number
    h: number
  }
  gold: {
    base: number
    purchasable: boolean
    total: number
    sell: number
  }
  tags: string[]
  maps: Record<string, boolean>
  stats: Record<string, number>
}

export interface GameVersion {
  version: string
  current: boolean
}

export interface FreeRotationData {
  freeChampionIds: number[]
  freeChampionIdsForNewPlayers: number[]
  maxNewPlayerLevel: number
}

// API Response wrappers
export interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
  cached?: boolean
  timestamp?: number
}

export interface ChampionListResponse {
  data: Record<string, ChampionDetailed>
  type: string
  version: string
}

export interface ItemListResponse {
  data: Record<string, ItemData>
  type: string
  version: string
}
