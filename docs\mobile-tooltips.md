# Mobile-Friendly Tooltips

This project now includes mobile-friendly tooltips that work with both click (mobile) and hover (desktop) interactions.

## Features

- **Desktop**: Tooltips appear on hover and disappear on mouse leave
- **Mobile**: Tooltips appear on tap/click and disappear when tapping elsewhere
- **Automatic detection**: Detects mobile devices and touch screens
- **Click-outside handling**: Automatically closes tooltips when clicking outside
- **Seamless integration**: Works with existing Radix UI tooltip system

## Usage

### Option 1: MobileTooltip Component (Recommended)

```tsx
import { MobileTooltip } from "@/components/ui/tooltip"

<MobileTooltip
  content={
    <div className="text-center">
      <div className="font-semibold text-cyan-300">Blue Essence</div>
      <div className="text-sm text-gray-300 mt-1">
        Primary in-game currency earned from battle pass
      </div>
    </div>
  }
>
  <div className="cursor-help">
    <span>6,300 BE</span>
  </div>
</MobileTooltip>
```

### Option 2: Enhanced Existing Tooltips

For existing tooltips using the standard Radix UI pattern, you can wrap them with `EnhancedTooltip`:

```tsx
import { EnhancedTooltip, Tooltip, TooltipTrigger, TooltipContent } from "@/components/ui/tooltip"

<EnhancedTooltip>
  <Tooltip>
    <TooltipTrigger asChild>
      <div className="cursor-help">
        <span>Hover or click me</span>
      </div>
    </TooltipTrigger>
    <TooltipContent>
      <p>This tooltip works on both mobile and desktop!</p>
    </TooltipContent>
  </Tooltip>
</EnhancedTooltip>
```

## Props

### MobileTooltip Props

- `content`: React.ReactNode - The tooltip content
- `children`: React.ReactNode - The trigger element
- `disabled?`: boolean - Disable the tooltip
- `side?`: "top" | "right" | "bottom" | "left" - Tooltip position
- `sideOffset?`: number - Distance from trigger (default: 4)
- `className?`: string - Additional CSS classes for trigger wrapper
- `contentClassName?`: string - Additional CSS classes for tooltip content

## How It Works

1. **Device Detection**: Automatically detects mobile devices using touch capabilities and screen size
2. **Event Handling**: 
   - Mobile: Uses `onClick` and `onTouchStart` events
   - Desktop: Uses `onMouseEnter` and `onMouseLeave` events
3. **Click Outside**: Listens for clicks outside the tooltip to close it on mobile
4. **State Management**: Uses a custom hook (`useMobileTooltip`) to manage tooltip state

## Examples in the Codebase

Check out these files for working examples:

- `components/champion/champion-info-server.tsx` - Currency and skin count tooltips
- `components/ui/tooltip-demo.tsx` - Various tooltip examples
- `components/ui/skin-rarity-icons.tsx` - Skin rarity tooltips

## Migration Guide

To convert existing tooltips to mobile-friendly ones:

1. **Simple replacement**: Replace `Tooltip` wrapper with `MobileTooltip`
2. **Move content**: Move `TooltipContent` children to the `content` prop
3. **Move trigger**: Move `TooltipTrigger` children to the `children` prop
4. **Remove wrappers**: Remove `TooltipTrigger` and `TooltipContent` components

### Before:
```tsx
<Tooltip>
  <TooltipTrigger asChild>
    <button>Click me</button>
  </TooltipTrigger>
  <TooltipContent>
    <p>Tooltip content</p>
  </TooltipContent>
</Tooltip>
```

### After:
```tsx
<MobileTooltip content={<p>Tooltip content</p>}>
  <button>Click me</button>
</MobileTooltip>
```

## Browser Support

- All modern browsers with touch event support
- Graceful fallback to hover-only behavior on older browsers
- Works with both mouse and touch interactions
