"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Search } from "lucide-react"

interface DidYouMeanProps {
  suggestions: string[]
  onSuggestionClick: (suggestion: string) => void
  className?: string
  variant?: 'orange' | 'purple' | 'blue' | 'green' | 'amber'
}

export function DidYouMean({ suggestions, onSuggestionClick, className = "", variant = 'orange' }: DidYouMeanProps) {
  if (suggestions.length === 0) return null

  // Define color variants
  const getVariantClasses = (variant: string) => {
    switch (variant) {
      case 'purple':
        return "bg-purple-600/20 border-purple-500/50 text-purple-300 hover:bg-purple-600/30 hover:border-purple-400/70 hover:text-purple-200"
      case 'blue':
        return "bg-blue-600/20 border-blue-500/50 text-blue-300 hover:bg-blue-600/30 hover:border-blue-400/70 hover:text-blue-200"
      case 'green':
        return "bg-green-600/20 border-green-500/50 text-green-300 hover:bg-green-600/30 hover:border-green-400/70 hover:text-green-200"
      case 'amber':
        return "bg-amber-600/20 border-amber-500/50 text-amber-300 hover:bg-amber-600/30 hover:border-amber-400/70 hover:text-amber-200"
      case 'orange':
      default:
        return "bg-orange-600/20 border-orange-500/50 text-orange-300 hover:bg-orange-600/30 hover:border-orange-400/70 hover:text-orange-200"
    }
  }

  const variantClasses = getVariantClasses(variant)

  return (
    <div className={`bg-gray-900/50 border border-gray-700/50 rounded-lg p-4 ${className}`}>
      <div className="flex items-center gap-2 mb-3">
        <Search className="h-4 w-4 text-gray-400" />
        <span className="text-sm text-gray-300 font-medium">Did you mean?</span>
      </div>

      <div className="flex flex-wrap gap-2">
        {suggestions.map((suggestion, index) => (
          <Button
            key={index}
            variant="outline"
            size="sm"
            onClick={() => onSuggestionClick(suggestion)}
            onMouseDown={(e) => e.preventDefault()} // Prevent blur event on input
            className={`text-xs transition-all duration-200 font-medium shadow-sm hover:shadow-md ${variantClasses}`}
          >
            {suggestion}
          </Button>
        ))}
      </div>
    </div>
  )
}
