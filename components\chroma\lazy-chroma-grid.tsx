"use client"

import React, { useState, useEffect, useMemo, useRef } from 'react'
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import Image from "next/image"
import Link from "next/link"

import { ProcessedChromaData } from '@/lib/types/league-api'
import { formatChromaPrice, getColorDisplayName } from '@/lib/utils/chroma-utils'
import { createChromaURL } from '@/lib/utils/chroma-url-utils'
import { SkinRarityIconsSimple } from "@/components/ui/skin-rarity-icons"
import { ChromaRarityIconsSimple } from "@/components/ui/chroma-rarity-icons"
import { useChromaMythicPrices } from '@/hooks/use-chroma-mythic-prices'

interface LazyChromaGridProps {
  chromas: ProcessedChromaData[]
  isClient: boolean
}

const INITIAL_LOAD = 60
const LOAD_INCREMENT = 30

// Rarity color mapping (same as skins)
const rarityColors: Record<string, string> = {
  'Regular': '#299645',
  'Legacy': '#c1a26a', 
  'Epic': '#27d7e5',
  'Legendary': '#e81b23',
  'Mythic': '#b47bdf',
  'Ultimate': '#d7861f',
  'Exalted': '#d3ce5a',
  'Transcendent': '#cda4de'
}

// Get rarity color for a chroma based on its skin tier
const getRarityColor = (tier: string, isLegacy: boolean): string => {
  if (isLegacy) return rarityColors['Legacy']
  return rarityColors[tier] || rarityColors['Regular']
}

export default function LazyChromaGrid({ chromas, isClient }: LazyChromaGridProps) {
  const [displayCount, setDisplayCount] = useState(INITIAL_LOAD)
  const [isLoading, setIsLoading] = useState(false)
  const loadingTriggerRef = useRef<HTMLDivElement>(null)

  // Fetch mythic prices for chromas
  const { mythicPrices, loading: mythicLoading } = useChromaMythicPrices(chromas)

  // Get chromas to display
  const displayedChromas = useMemo(() => {
    return chromas.slice(0, displayCount)
  }, [chromas, displayCount])

  const hasMore = displayCount < chromas.length

  // Load more chromas
  const loadMore = () => {
    if (!isLoading && hasMore) {
      setIsLoading(true)
      // Reduced loading delay for smoother experience
      setTimeout(() => {
        setDisplayCount(prev => Math.min(prev + LOAD_INCREMENT, chromas.length))
        setIsLoading(false)
      }, 100)
    }
  }

  // Intersection Observer for lazy loading
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries
        if (entry.isIntersecting && hasMore && !isLoading) {
          loadMore()
        }
      },
      {
        rootMargin: '200px', // Start loading 200px before the trigger comes into view
        threshold: 0.1
      }
    )

    const currentTrigger = loadingTriggerRef.current
    if (currentTrigger) {
      observer.observe(currentTrigger)
    }

    return () => {
      if (currentTrigger) {
        observer.unobserve(currentTrigger)
      }
    }
  }, [hasMore, isLoading])

  // Reset display count when chromas change (due to filtering)
  useEffect(() => {
    setDisplayCount(INITIAL_LOAD)
  }, [chromas])

  if (!isClient) {
    return null
  }

  return (
    <>
      {/* Chromas Grid - Match mythic shop layout */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
        {displayedChromas.map((chroma) => {
          const chromaSlug = createChromaURL(chroma)
          const rarityColor = getRarityColor(chroma.skinTier, chroma.availability === 'Legacy')

          return (
            <Link
              key={chroma.id}
              href={`/skins/chromas/${chromaSlug}`}
              className="block"
            >
              <div className="bg-gradient-to-br from-gray-900/60 to-gray-800/40 border border-purple-700/30 rounded-xl overflow-hidden relative group transition-all duration-500 transform hover:scale-105 cursor-pointer hover:border-purple-400/70 hover:shadow-lg hover:shadow-purple-500/20">
                {/* Chroma Image - Centered display like mythic shop with correct height */}
                {chroma.image && (
                  <div className="h-48 flex items-center justify-center p-6">
                    <Image
                      src={chroma.image}
                      alt={`${chroma.name} - ${chroma.skinName}`}
                      width={170}
                      height={170}
                      className="object-contain"
                    />

                    {/* Color Indicators - Top Left */}
                    <div className="absolute top-3 left-3 flex space-x-1">
                      {(() => {
                        // Get unique colors only
                        const uniqueColors = [...new Set(chroma.colors)]
                        return uniqueColors.slice(0, 3).map((color, index) => (
                          <div
                            key={index}
                            className="w-4 h-4 rounded-full border-2 border-white/70 shadow-lg"
                            style={{ backgroundColor: color }}
                            title={getColorDisplayName(color)}
                          />
                        ))
                      })()}
                      {(() => {
                        const uniqueColors = [...new Set(chroma.colors)]
                        return uniqueColors.length > 3 && (
                          <div className="w-4 h-4 rounded-full bg-gray-600 border-2 border-white/70 shadow-lg flex items-center justify-center">
                            <span className="text-xs text-white font-bold">+</span>
                          </div>
                        )
                      })()}
                    </div>
                  </div>
                )}

                {/* Content overlay at bottom */}
                <div className="p-4 bg-gradient-to-t from-black/90 via-black/70 to-transparent">
                  <p
                    className="text-white font-semibold text-sm mb-2 truncate"
                    title={chroma.name}
                  >
                    {chroma.name}
                  </p>

                  {/* Price and rarity row */}
                  <div className="flex items-center justify-between">
                    {/* Price on the left */}
                    <div className="flex items-center space-x-1">
                      {(() => {
                        // Check if chroma has mythic price
                        const mythicPrice = chroma.contentId ? mythicPrices.get(chroma.contentId) : null

                        if (chroma.availability !== 'Available' && mythicPrice) {
                          // Show mythic price with "Last Recorded Price"
                          return (
                            <>
                              <Image
                                src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/mythic.svg"
                                alt="ME"
                                width={16}
                                height={16}
                                className="h-4 w-4"
                              />
                              <span className="font-medium" style={{ color: '#b47bdf' }}>
                                {mythicPrice.cost}
                              </span>
                            </>
                          )
                        } else if (chroma.availability === 'Legacy' || formatChromaPrice(chroma.price) === 'N/A') {
                          // Show N/A for legacy items without mythic price
                          return <span className="text-gray-400 font-medium">N/A</span>
                        } else {
                          // Show regular RP price
                          return (
                            <>
                              <Image
                                src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg"
                                alt="RP"
                                width={16}
                                height={16}
                                className="h-4 w-4"
                              />
                              <span className="font-medium" style={{ color: 'rgb(251, 146, 60)' }}>
                                {formatChromaPrice(chroma.price).replace(' RP', '')}
                              </span>
                            </>
                          )
                        }
                      })()}
                    </div>

                    {/* Rarity icons on the right */}
                    <div className="flex items-center gap-1 flex-shrink-0">
                      {/* Chroma Rarity Icon */}
                      <ChromaRarityIconsSimple
                        rarity={chroma.chromaRarity}
                        size={16}
                      />
                      {/* Skin Rarity Icon */}
                      <SkinRarityIconsSimple
                        rarity={chroma.skinTier}
                        isLegacy={chroma.availability === 'Legacy'}
                        isBase={false}
                        size={16}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </Link>
          )
        })}
      </div>

      {/* Intersection Observer Trigger */}
      {hasMore && (
        <div
          ref={loadingTriggerRef}
          className="w-full h-20 flex items-center justify-center"
        >
          {isLoading && (
            <div className="inline-flex items-center space-x-2">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-purple-400"></div>
              <span className="text-gray-300">Loading more chromas...</span>
            </div>
          )}
        </div>
      )}

      {/* End message */}
      {!hasMore && displayedChromas.length > INITIAL_LOAD && (
        <div className="text-center py-8">
          <p className="text-gray-400">
            You've reached the end! Showing all {displayedChromas.length} chromas.
          </p>
        </div>
      )}
    </>
  )
}
