"use client"

import SharedLayout from "@/components/shared-layout"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { useItemFilters, useItems } from "@/hooks/use-items"
import { AlertCircle, Search } from "lucide-react"
import { FilterIcon } from "@/components/ui/filter-icon"
import { ItemsIcon } from "@/components/navigation/navigation-data"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { SelectItemRightCheck } from "@/components/ui/select-item-right-check"
import { ItemTagsCombobox } from "@/components/ui/item-tags-combobox"
import Image from "next/image"
import { useEffect, useState } from "react"
import { useSearchParams, useRouter } from "next/navigation"
import { MobileTooltip } from "@/components/ui/tooltip"
import { SelectedItemFiltersDisplay } from "@/components/ui/selected-item-filters-display"
import LazyItemGrid from "@/components/items/lazy-item-grid"
import { ItemGridSkeleton } from "@/components/items/item-skeleton"
import { FiltersSkeleton } from "@/components/items/filters-skeleton"
import ItemSearchPanel from "@/components/search/item-search-panel"
import { RemoveFiltersIcon } from "@/components/ui/remove-filters-icon"



export default function ItemsPage() {
  const { items, tags, loading, error } = useItems()
  const { filteredItems, filters, updateFilters, resetFilters, removeFilter, clearCategory } = useItemFilters(items)
  const [isClient, setIsClient] = useState(false)
  const [isSearchPanelOpen, setIsSearchPanelOpen] = useState(false)
  const searchParams = useSearchParams()
  const router = useRouter()

  // Client-side rendering check
  useEffect(() => {
    setIsClient(true)
  }, [])

  // Handle URL parameters for initial filtering
  useEffect(() => {
    if (!isClient || loading) return

    const mainStatsParam = searchParams.get('mainStats')
    const tagsParam = searchParams.get('tags')
    const searchParam = searchParams.get('search')
    const sortFieldParam = searchParams.get('sortField')
    const sortDirectionParam = searchParams.get('sortDirection')

    if (mainStatsParam || tagsParam || searchParam || sortFieldParam || sortDirectionParam) {
      const newFilters: any = {}

      if (mainStatsParam) {
        newFilters.mainStats = [decodeURIComponent(mainStatsParam)]
      }

      if (tagsParam) {
        newFilters.tags = [decodeURIComponent(tagsParam)]
      }

      if (searchParam) {
        newFilters.search = decodeURIComponent(searchParam)
      }

      if (sortFieldParam) {
        newFilters.sortField = decodeURIComponent(sortFieldParam) as 'name' | 'price'
      }

      if (sortDirectionParam) {
        newFilters.sortDirection = decodeURIComponent(sortDirectionParam) as 'asc' | 'desc'
      }

      updateFilters(newFilters)
    }
  }, [isClient, loading, searchParams, updateFilters])

  // Add blue gradient background effect
  useEffect(() => {
    const blueGradient = 'linear-gradient(135deg, hsl(220 50% 8%) 0%, hsl(220 39% 11%) 50%, hsl(215 30% 6%) 100%)'

    document.body.style.background = blueGradient
    document.body.style.backgroundColor = 'hsl(220 39% 11%)'
    document.body.style.backgroundRepeat = 'no-repeat'
    document.documentElement.style.background = blueGradient
    document.documentElement.style.backgroundColor = 'hsl(220 39% 11%)'
    document.documentElement.style.backgroundRepeat = 'no-repeat'

    // Cleanup function
    return () => {
      document.body.style.background = ''
      document.body.style.backgroundColor = ''
      document.body.style.backgroundRepeat = ''
      document.documentElement.style.background = ''
      document.documentElement.style.backgroundColor = ''
      document.documentElement.style.backgroundRepeat = ''
    }
  }, [])

  // Custom reset handler that also navigates to clean URL
  const handleResetFilters = () => {
    resetFilters()
    // Navigate to clean URL without query parameters
    router.push('/items')
  }

  return (
    <SharedLayout>
      <div className="container mx-auto px-8 py-8 mobile-no-top-padding">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <ItemsIcon className="h-8 w-8 text-blue-400" size={32} />
              <h1 className="text-4xl font-bold text-white">Items</h1>
            </div>
            {/* Mobile Search Button - Next to Title */}
            <div className="lg:hidden">
              <Button
                onClick={() => setIsSearchPanelOpen(true)}
                variant="outline"
                size="sm"
                className="border-blue-700/30 text-blue-400 hover:bg-blue-400/10 hover:border-blue-400/50 p-2"
              >
                <FilterIcon className="h-4 w-4" />
              </Button>
            </div>
          </div>
          <p className="text-gray-300 text-lg">Discover all items, builds, and equipment in League of Legends</p>
        </div>

        {/* Error State */}
        {error && (
          <div className="mb-8 p-4 bg-red-900/20 border border-red-700/30 rounded-lg">
            <div className="flex items-center space-x-2 text-red-400">
              <AlertCircle className="h-5 w-5" />
              <span>Error loading items: {error}</span>
            </div>
          </div>
        )}



        {/* Search and Filters - Desktop Only */}
        <div className="hidden lg:block mb-8 space-y-4">
          {loading ? (
            <FiltersSkeleton />
          ) : (
            <div className="flex flex-col lg:flex-row gap-4 items-end">
              {/* Left side filters group */}
              <div className="flex flex-col lg:flex-row gap-4 flex-1">
                {/* Search Bar - Reduced width */}
                <div className="relative lg:flex-1 lg:max-w-md">
                  <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <Input
                    type="text"
                    placeholder="Search items..."
                    value={filters.search}
                    onChange={(e) => updateFilters({ search: e.target.value })}
                    className="pl-12 bg-gray-900/80 border-gray-700/50 text-white placeholder-gray-400 focus:border-blue-400/60"
                  />
                </div>

                {/* Tags Filter */}
                <ItemTagsCombobox
                  selectedTags={filters.tags}
                  onSelectionChange={(tags) => updateFilters({ tags })}
                  availableTags={tags}
                  placeholder="Select stats..."
                  className="w-full lg:w-48"
                />
              </div>

              {/* Alphabetical Filter & Reset - Far Right */}
              <div className="flex-shrink-0 flex items-center gap-2">
                <Select
                  value={`${filters.sortField}-${filters.sortDirection}`}
                  onValueChange={(value) => {
                    const [field, direction] = value.split('-') as [typeof filters.sortField, typeof filters.sortDirection]
                    updateFilters({ sortField: field, sortDirection: direction })
                  }}
                >
                  <SelectTrigger className="w-48 bg-gray-900/40 border-gray-700/20 text-white hover:bg-gray-800/50">
                    <SelectValue placeholder="Alphabetical Filter" />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-900 border-gray-700">
                    <SelectItemRightCheck value="name-asc" className="text-white hover:bg-blue-400/10 focus:bg-blue-400/10">
                      Name (A–Z)
                    </SelectItemRightCheck>
                    <SelectItemRightCheck value="name-desc" className="text-white hover:bg-blue-400/10 focus:bg-blue-400/10">
                      Name (Z–A)
                    </SelectItemRightCheck>
                    <SelectItemRightCheck value="price-asc" className="text-white hover:bg-blue-400/10 focus:bg-blue-400/10">
                      Price (Low to High)
                    </SelectItemRightCheck>
                    <SelectItemRightCheck value="price-desc" className="text-white hover:bg-blue-400/10 focus:bg-blue-400/10">
                      Price (High to Low)
                    </SelectItemRightCheck>
                  </SelectContent>
                </Select>

                {/* Reset Filters Button */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleResetFilters}
                  className="border-gray-700/20 text-gray-300 hover:bg-gray-800/50 hover:text-red-400 p-2 h-10 w-10"
                  title="Reset all filters"
                >
                  <RemoveFiltersIcon className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* Selected Filters Display - Desktop Only */}
        <div className="hidden lg:block">
          {!loading && (
            <SelectedItemFiltersDisplay
              filters={filters}
              onRemoveFilter={removeFilter}
              onClearCategory={clearCategory}
            />
          )}
        </div>

        {/* Filter Actions - Desktop Only */}
        <div className="hidden lg:flex items-center justify-between mb-8">
          {loading ? (
            <div className="h-5 bg-gray-700/50 rounded w-48 animate-pulse"></div>
          ) : (
            <p className="text-gray-400">
              Showing {filteredItems.length} of {items.length} items
            </p>
          )}
        </div>

        {/* Items Grid with Lazy Loading */}
        <div className="min-h-[60vh]">
          {loading ? (
            <ItemGridSkeleton />
          ) : filteredItems.length > 0 ? (
            <LazyItemGrid items={filteredItems} isClient={isClient} />
          ) : (
            <div className="text-center py-12">
              <div className="relative mx-auto mb-4 w-32 h-32">
                <Image
                  src="https://raw.communitydragon.org/latest/game/assets/loadouts/summoneremotes/champions/blitzcrank/blitzcrank_sad_confused_glow.png"
                  alt="No results glow"
                  width={128}
                  height={128}
                  className="absolute inset-0 opacity-60"
                  unoptimized
                />
                <Image
                  src="https://raw.communitydragon.org/latest/game/assets/loadouts/summoneremotes/champions/blitzcrank/blitzcrank_sad_confused_vfx.png"
                  alt="No results"
                  width={128}
                  height={128}
                  className="relative z-10"
                  unoptimized
                />
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">No items found</h3>
              <p className="text-gray-400 mb-4">
                {filters.search || filters.tags.length > 0 || filters.mainStats.length > 0 || (filters.priceRange[0] !== 0 || filters.priceRange[1] !== 5000)
                  ? "Try adjusting your filters to see more results"
                  : "No items are currently available"}
              </p>
              {(filters.search || filters.tags.length > 0 || filters.mainStats.length > 0 || (filters.priceRange[0] !== 0 || filters.priceRange[1] !== 5000)) && (
                <Button
                  variant="outline"
                  onClick={handleResetFilters}
                  className="border-gray-700 text-gray-300"
                >
                  Clear Filters
                </Button>
              )}
            </div>
          )}
        </div>

        {/* Mobile Search Panel */}
        <ItemSearchPanel
          isOpen={isSearchPanelOpen}
          onClose={() => setIsSearchPanelOpen(false)}
          filters={filters}
          onFiltersChange={updateFilters}
          onResetFilters={handleResetFilters}
          availableTags={tags}
          filteredCount={filteredItems.length}
          totalCount={items.length}
        />
      </div>
    </SharedLayout>
  )
}
