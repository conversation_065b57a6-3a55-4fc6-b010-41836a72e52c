/**
 * Hook for managing discount/sales data
 */

import { ProcessedDiscountItem, salesService } from '@/lib/services/sales-service'
import { calculateTimeRemaining, filterDiscountsByPriceRange, filterDiscountsBySearch, getDiscountStats, getPriceRange } from '@/lib/utils/discount-utils'
import { useCallback, useEffect, useMemo, useState } from 'react'

interface UseDiscountsReturn {
  discounts: ProcessedDiscountItem[]
  loading: boolean
  error: string | null
  stats: ReturnType<typeof getDiscountStats>
  timeRemaining: ReturnType<typeof calculateTimeRemaining>
  refreshDiscounts: () => Promise<void>
  clearCache: () => void
}

export function useDiscounts(): UseDiscountsReturn {
  const [discounts, setDiscounts] = useState<ProcessedDiscountItem[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [timeRemaining, setTimeRemaining] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0,
    totalMs: 0,
    expired: false
  })

  // Calculate stats from current discounts
  const stats = getDiscountStats(discounts)

  // Fetch discounts data
  const fetchDiscounts = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      

      // Add timeout to prevent infinite loading (increased to 60 seconds for discount processing)
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Request timeout - API took too long to respond')), 60000)
      )

      const dataPromise = salesService.getActiveDiscounts()

      const data = await Promise.race([dataPromise, timeoutPromise]) as any[]

      setDiscounts(data)
      

    } catch (err) {
      console.error('❌ HOOK: Failed to fetch discounts:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch discounts')
    } finally {
      setLoading(false)
    }
  }, [])

  // Refresh discounts (force fresh data)
  const refreshDiscounts = useCallback(async () => {
    salesService.clearCache()
    await fetchDiscounts()
  }, [fetchDiscounts])

  // Clear cache
  const clearCache = useCallback(() => {
    salesService.clearCache()
  }, [])

  // Update countdown timer
  useEffect(() => {
    if (discounts.length === 0) return

    const updateTimer = () => {
      // Use the earliest end date from stats
      if (stats.earliestEndDate) {
        const newTimeRemaining = calculateTimeRemaining(stats.earliestEndDate)
        setTimeRemaining(newTimeRemaining)
      }
    }

    // Update immediately
    updateTimer()

    // Update every second
    const interval = setInterval(updateTimer, 1000)

    return () => clearInterval(interval)
  }, [discounts, stats.earliestEndDate])

  // Initial fetch
  useEffect(() => {
    fetchDiscounts()
  }, [fetchDiscounts])

  return {
    discounts,
    loading,
    error,
    stats,
    timeRemaining,
    refreshDiscounts,
    clearCache
  }
}

/**
 * Hook for managing discount filters and sorting
 */
interface UseDiscountFiltersReturn {
  filteredDiscounts: ProcessedDiscountItem[]
  sortBy: 'discount' | 'price' | 'endDate' | 'name'
  sortOrder: 'asc' | 'desc'
  filterType: 'ALL' | 'CHAMPION_SKIN' | 'CHAMPION'
  searchTerm: string
  priceRange: [number, number]
  maxPrice: number
  setSortBy: (sortBy: 'discount' | 'price' | 'endDate' | 'name') => void
  setSortOrder: (order: 'asc' | 'desc') => void
  setFilterType: (type: 'ALL' | 'CHAMPION_SKIN' | 'CHAMPION') => void
  setSearchTerm: (term: string) => void
  setPriceRange: (range: [number, number]) => void
  resetFilters: () => void
  isSearching: boolean
}

export function useDiscountFilters(discounts: ProcessedDiscountItem[]): UseDiscountFiltersReturn {
  const [sortBy, setSortBy] = useState<'discount' | 'price' | 'endDate' | 'name'>('discount')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const [filterType, setFilterType] = useState<'ALL' | 'CHAMPION_SKIN' | 'CHAMPION'>('ALL')
  const [searchTerm, setSearchTerm] = useState('')
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('')

  // Calculate price range from current discounts
  const priceRangeData = useMemo(() => getPriceRange(discounts), [discounts])
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 1000])
  const [debouncedPriceRange, setDebouncedPriceRange] = useState<[number, number]>([0, 1000])

  // Debounce search term with 300ms delay
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm)
    }, 300)

    return () => clearTimeout(timer)
  }, [searchTerm])

  // Debounce price range with 300ms delay
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedPriceRange(priceRange)
    }, 300)

    return () => clearTimeout(timer)
  }, [priceRange])

  // Update price range when discounts change - always start from 0
  useEffect(() => {
    if (discounts.length > 0) {
      setPriceRange([0, priceRangeData.max])
      setDebouncedPriceRange([0, priceRangeData.max])
    }
  }, [discounts, priceRangeData])

  // Apply filters and sorting
  const filteredDiscounts = useMemo(() => {
    let filtered = discounts

    // Apply search filter - use debounced search term
    filtered = filterDiscountsBySearch(filtered, debouncedSearchTerm)

    // Apply type filter
    if (filterType !== 'ALL') {
      filtered = filtered.filter(discount => discount.type === filterType)
    }

    // Apply price range filter - use debounced price range
    filtered = filterDiscountsByPriceRange(filtered, debouncedPriceRange[0], debouncedPriceRange[1])

    // Apply sorting
    return filtered.sort((a, b) => {
      let comparison = 0

      switch (sortBy) {
        case 'discount':
          comparison = a.discount - b.discount
          break
        case 'price':
          comparison = a.salePrice - b.salePrice
          break
        case 'endDate':
          comparison = new Date(a.endDate).getTime() - new Date(b.endDate).getTime()
          break
        case 'name':
          const nameA = a.skinName || a.championName
          const nameB = b.skinName || b.championName
          comparison = nameA.localeCompare(nameB)
          break
      }

      return sortOrder === 'desc' ? -comparison : comparison
    })
  }, [discounts, debouncedSearchTerm, filterType, debouncedPriceRange, sortBy, sortOrder])

  const resetFilters = useCallback(() => {
    setSearchTerm('')
    setDebouncedSearchTerm('')
    setFilterType('ALL')
    setPriceRange([0, priceRangeData.max])
    setDebouncedPriceRange([0, priceRangeData.max])
  }, [priceRangeData])

  return {
    filteredDiscounts,
    sortBy,
    sortOrder,
    filterType,
    searchTerm,
    priceRange,
    maxPrice: priceRangeData.max,
    setSortBy,
    setSortOrder,
    setFilterType,
    setSearchTerm,
    setPriceRange,
    resetFilters,
    isSearching: searchTerm !== debouncedSearchTerm ||
                 priceRange[0] !== debouncedPriceRange[0] ||
                 priceRange[1] !== debouncedPriceRange[1]
  }
}

/**
 * Hook for countdown timer with custom end date
 */
export function useCountdown(endDate: string) {
  const [timeRemaining, setTimeRemaining] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0,
    totalMs: 0,
    expired: false
  })

  useEffect(() => {
    if (!endDate) return

    const updateTimer = () => {
      const newTimeRemaining = calculateTimeRemaining(endDate)
      setTimeRemaining(newTimeRemaining)
    }

    // Update immediately
    updateTimer()

    // Update every second
    const interval = setInterval(updateTimer, 1000)

    return () => clearInterval(interval)
  }, [endDate])

  return timeRemaining
}
