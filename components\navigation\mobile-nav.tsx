"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { usePathname } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import { Badge } from "@/components/ui/badge"
import {
  Menu,
  TrendingUp,
  Star,
  ChevronDown,
  Lock,
} from "lucide-react"
import { navigationData, featuredData, SubmenuIcon, DiscountsIcon } from "./navigation-data"

interface MobileNavProps {
  currentPath?: string
}

export default function MobileNav({ currentPath }: MobileNavProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [openSubmenus, setOpenSubmenus] = useState<string[]>([])
  const pathname = usePathname()

  // Close on route changes
  useEffect(() => {
    setIsOpen(false)
  }, [pathname])

  const toggleSubmenu = (itemLabel: string) => {
    setOpenSubmenus(prev =>
      prev.includes(itemLabel)
        ? prev.filter(label => label !== itemLabel)
        : [itemLabel] // Only keep the newly opened submenu, closing all others
    )
  }

  const handleLinkClick = (customAction?: string) => {
    // Handle custom actions for currency conversion
    if (customAction) {
      if (customAction === 'as-to-rp') {
        localStorage.setItem('conversionToolSelection', JSON.stringify({ from: 'as', to: 'rp' }))
      } else if (customAction === 'me-to-rp') {
        localStorage.setItem('conversionToolSelection', JSON.stringify({ from: 'me', to: 'rp' }))
      } else if (customAction === 'rp-to-usd') {
        localStorage.setItem('conversionToolSelection', JSON.stringify({ from: 'rp', to: 'usd' }))
      }
      // Dispatch custom event to notify the conversion tool page
      window.dispatchEvent(new CustomEvent('conversionToolSelectionChanged'))
    }
    setIsOpen(false)
  }

  return (
    <Sheet
      open={isOpen}
      onOpenChange={setIsOpen}
    >
      <SheetTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="md:hidden text-white hover:bg-orange-600/20"
        >
          <Menu className="h-6 w-6" />
          <span className="sr-only">Toggle navigation menu</span>
        </Button>
      </SheetTrigger>
      <SheetContent 
        side="left" 
        className="w-80 bg-gray-950/95 backdrop-blur-md border-orange-800/20 p-0"
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-orange-800/20">
            <div className="flex items-center space-x-3">
              <Image
                src="/images/LoLDB-Logo-Secondary.svg"
                alt="LoLDB Logo"
                width={32}
                height={32}
                className="w-8 h-8"
              />
              <span className="text-lg font-bold text-white">LoLDB</span>
            </div>
          </div>

          {/* Navigation */}
          <div className="flex-1 overflow-y-auto py-4 scrollbar-gutter-stable" style={{ paddingBottom: '120px' }}>
            {/* Quick Access Section */}
            <div className="px-4 pr-2">
              <h2 className="text-lg font-semibold text-white mb-6 flex items-center">
                <TrendingUp className="h-5 w-5 mr-2 text-orange-400" />
                Quick Access
              </h2>
              <nav className="space-y-2">
                {navigationData.map((item, index) => (
                  <div key={index}>
                    {item.submenu ? (
                      <div>
                        {/* Item with submenu - clickable main item */}
                        <div
                          className={`flex items-center justify-between p-3 text-gray-300 rounded-lg transition-colors group cursor-pointer ${
                            item.isComingSoon ? "opacity-60 cursor-not-allowed" : ""
                          } ${openSubmenus.includes(item.label) ? "bg-orange-600/20 text-white" : ""}`}
                          onClick={() => !item.isComingSoon && toggleSubmenu(item.label)}
                          onTouchEnd={(e) => {
                            // Remove focus after touch to prevent hover state persistence
                            e.currentTarget.blur()
                          }}
                        >
                          <div className="flex items-center space-x-3">
                            <item.icon className={`h-5 w-5 transition-colors ${
                              openSubmenus.includes(item.label) ? "text-orange-300" : "text-orange-400"
                            }`} />
                            <span className="font-medium">{item.label}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            {item.isLive && (
                              <Badge className="bg-red-600 text-white text-xs animate-pulse px-1.5 py-0.5">
                                Live
                              </Badge>
                            )}
                            {item.isComingSoon && (
                              <Badge className="bg-gray-600 text-gray-300 text-xs px-1.5 py-0.5">
                                Soon
                              </Badge>
                            )}
                            {!item.isLive && !item.isComingSoon && item.count && (
                              <span className="text-xs text-gray-400 bg-gray-800 px-2 py-1 rounded">
                                {item.count}
                              </span>
                            )}
                            <ChevronDown
                              className={`h-4 w-4 transition-transform ${
                                openSubmenus.includes(item.label) ? 'rotate-180' : ''
                              } ${item.isComingSoon ? 'text-gray-500' : 'text-gray-400 group-hover:text-white'}`}
                            />
                          </div>
                        </div>

                        {/* Submenu items */}
                        {openSubmenus.includes(item.label) && (
                          <div className="ml-8 mt-1 space-y-1">
                            {item.submenu?.map((subItem, subIndex) => (
                              subItem.isDisabled ? (
                                <div
                                  key={subIndex}
                                  className="flex items-center space-x-3 p-2 text-gray-600 cursor-not-allowed opacity-60"
                                >
                                  {/* Conditional rendering: Custom icon, regular icon, or bullet point */}
                                  {subItem.customIcon === "discounts" ? (
                                    <DiscountsIcon className="mr-1 text-gray-600 flex-shrink-0" />
                                  ) : subItem.icon ? (
                                    <SubmenuIcon
                                      src={subItem.icon}
                                      alt={subItem.label}
                                      className="w-4 h-4 mr-1 text-gray-600 flex-shrink-0"
                                    />
                                  ) : (
                                    <div className="w-2 h-2 bg-gray-600 rounded-full mr-1 flex-shrink-0"></div>
                                  )}
                                  <span className="text-sm flex-1">{subItem.label}</span>
                                  {subItem.isLocked && (
                                    <Lock className="h-3 w-3 text-gray-600 flex-shrink-0" />
                                  )}
                                </div>
                              ) : (
                                <Link
                                  key={subIndex}
                                  href={subItem.href}
                                  onClick={() => handleLinkClick(subItem.customAction)}
                                  className="flex items-center space-x-3 p-2 text-gray-400 hover:text-white hover:bg-orange-600/20 rounded-lg transition-colors"
                                >
                                  {/* Conditional rendering: Custom icon, regular icon, or bullet point */}
                                  {subItem.customIcon === "discounts" ? (
                                    <DiscountsIcon className="mr-1 group-hover:text-orange-400 transition-colors flex-shrink-0" />
                                  ) : subItem.icon ? (
                                    <SubmenuIcon
                                      src={subItem.icon}
                                      alt={subItem.label}
                                      className="w-4 h-4 mr-1 group-hover:text-orange-400 transition-colors flex-shrink-0"
                                    />
                                  ) : (
                                    <div className="w-2 h-2 bg-orange-400/60 rounded-full mr-1 group-hover:bg-orange-400 transition-colors flex-shrink-0"></div>
                                  )}
                                  <span className="text-sm flex-1">{subItem.label}</span>
                                  {subItem.isLocked && (
                                    <Lock className="h-3 w-3 text-gray-500 flex-shrink-0" />
                                  )}
                                </Link>
                              )
                            ))}
                          </div>
                        )}
                      </div>
                    ) : (
                      /* Regular item without submenu */
                      <Link
                        href={item.href}
                        onClick={() => handleLinkClick()}
                        className={`flex items-center justify-between p-3 text-gray-300 hover:text-white hover:bg-orange-600/20 rounded-lg transition-colors group ${
                          item.isComingSoon ? "opacity-60 cursor-not-allowed" : ""
                        }`}
                      >
                        <div className="flex items-center space-x-3">
                          <item.icon className="h-5 w-5 text-orange-400 group-hover:text-orange-300" />
                          <span className="font-medium">{item.label}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          {item.isLive && (
                            <Badge className="bg-red-600 text-white text-xs animate-pulse px-1.5 py-0.5">
                              Live
                            </Badge>
                          )}
                          {item.isComingSoon && (
                            <Badge className="bg-gray-600 text-gray-300 text-xs px-1.5 py-0.5">
                              Soon
                            </Badge>
                          )}
                          {!item.isLive && !item.isComingSoon && item.count && (
                            <span className="text-xs text-gray-400 bg-gray-800 px-2 py-1 rounded">
                              {item.count}
                            </span>
                          )}
                        </div>
                      </Link>
                    )}
                  </div>
                ))}
              </nav>

              {/* Featured section */}
              <div className="mt-8 pt-6 border-t border-gray-700/30">
                <h3 className="text-md font-semibold text-white mb-4 flex items-center">
                  <Star className="h-4 w-4 mr-2 text-amber-400" />
                  Featured
                </h3>
                <div className="space-y-3">
                  {featuredData.map((featured, index) => (
                    <Link
                      key={index}
                      href={featured.href}
                      onClick={() => handleLinkClick()}
                      className={`block p-3 rounded-lg bg-gray-800/30 border ${featured.borderColor} hover:bg-gray-700/40 ${featured.hoverBorderColor} transition-all duration-200 cursor-pointer group`}
                    >
                      <h4 className={`text-sm font-medium ${featured.color} group-hover:text-${featured.color.split('-')[1]}-200 transition-colors mb-1`}>
                        {featured.title}
                      </h4>
                      <p className="text-xs text-gray-400 group-hover:text-gray-300 transition-colors">
                        {featured.description}
                      </p>
                    </Link>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Fixed Bottom Buttons */}
          <div className="absolute bottom-0 left-0 right-0 bg-gray-950/95 backdrop-blur-md border-t border-orange-800/20 p-4 space-y-3">
            <Button
              size="sm"
              className="w-full bg-[#5865f2] hover:bg-[#5865f2]/80 text-white border-[#5865f2]"
              onClick={() => {
                window.open('https://discord.gg/NsuuwjPuZk', '_blank')
                setIsOpen(false)
              }}
            >
              <Image
                src="/images/discord-icon-svgrepo-com.svg"
                alt="Join Discord"
                width={16}
                height={16}
                className="w-4 h-4 mr-2"
              />
              Join Discord
            </Button>
            <Button
              size="sm"
              disabled
              className="w-full bg-gray-600 text-gray-400 cursor-not-allowed opacity-60"
            >
              <Image
                src="/images/log-in-01-svgrepo-com.svg"
                alt="Sign In"
                width={16}
                height={16}
                className="w-4 h-4 mr-2 opacity-60"
              />
              Sign In
            </Button>
          </div>

        </div>
      </SheetContent>
    </Sheet>
  )
}
