/**
 * Recommended Positions Service - Handles fetching and caching champion recommended positions
 */

export interface RecommendedPositionsApiResponse {
  success: boolean
  message: string
  timestamp: string
  data: {
    [championId: string]: {
      recommendedPositions: string | string[] // Can be space-separated string or array
    }
  }
}

export interface RecommendedPositionsData {
  [championId: string]: {
    recommendedPositions: string[]
  }
}

interface RecommendedPositionsCache {
  data: RecommendedPositionsData
  timestamp: number
}

class RecommendedPositionsService {
  private cache: RecommendedPositionsCache | null = null
  private readonly CACHE_TTL = 1000 * 60 * 60 * 24 // 24 hours
  private readonly API_URL = 'https://api.loldb.info/api/perks/recommended-champion-positions'

  /**
   * Get recommended positions for a specific champion by ID
   */
  async getChampionRecommendedPositions(championId: string): Promise<string[]> {
    const allData = await this.getAllRecommendedPositions()
    return allData[championId]?.recommendedPositions || []
  }

  /**
   * Get all recommended positions data
   */
  async getAllRecommendedPositions(): Promise<RecommendedPositionsData> {
    // Check cache first
    if (this.cache && this.isCacheValid()) {
      return this.cache.data
    }

    try {
      const response = await fetch(this.API_URL, {
        headers: {
          'User-Agent': 'LoLDB/1.0',
          'Accept': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const apiResponse: RecommendedPositionsApiResponse = await response.json()

      // Transform the API response to our internal format
      const data: RecommendedPositionsData = {}
      Object.entries(apiResponse.data || {}).forEach(([championId, championData]) => {
        // Handle both string and array formats
        let positions: string[] = []
        if (typeof championData.recommendedPositions === 'string') {
          positions = championData.recommendedPositions.split(' ')
        } else if (Array.isArray(championData.recommendedPositions)) {
          positions = championData.recommendedPositions
        }

        data[championId] = {
          recommendedPositions: positions
        }
      })

      // Update cache
      this.cache = {
        data,
        timestamp: Date.now()
      }

      return data

    } catch (error) {
      // Return cached data if available, even if expired
      if (this.cache) {
        return this.cache.data
      }

      // Throw error instead of returning empty object - no fallbacks
      throw new Error('Failed to fetch recommended positions data and no cache available')
    }
  }

  /**
   * Check if cache is still valid
   */
  private isCacheValid(): boolean {
    if (!this.cache) return false
    return (Date.now() - this.cache.timestamp) < this.CACHE_TTL
  }

  /**
   * Clear the cache (useful for testing or forced refresh)
   */
  clearCache(): void {
    this.cache = null
  }

  /**
   * Get cache status for debugging
   */
  getCacheStatus(): { cached: boolean; age: number; size: number } {
    if (!this.cache) {
      return { cached: false, age: 0, size: 0 }
    }

    return {
      cached: true,
      age: Date.now() - this.cache.timestamp,
      size: Object.keys(this.cache.data).length
    }
  }

  /**
   * Map position names from API to display names
   */
  mapPositionName(position: string): string {
    // Handle edge cases
    if (!position || typeof position !== 'string') {
      console.warn('Invalid position value passed to mapPositionName:', position);
      return 'Unknown';
    }

    const positionMap: Record<string, string> = {
      'TOP': 'Top',
      'JUNGLE': 'Jungle',
      'MIDDLE': 'Middle',
      'BOTTOM': 'Bottom',
      'SUPPORT': 'Support',
      'UTILITY': 'Support'
    }
    return positionMap[position.toUpperCase()] || position
  }

  /**
   * Get mapped recommended positions for a champion
   */
  async getChampionMappedPositions(championId: string): Promise<string[]> {
    const positions = await this.getChampionRecommendedPositions(championId)
    return positions.map(pos => this.mapPositionName(pos))
  }
}

// Export singleton instance
export const recommendedPositionsService = new RecommendedPositionsService()
