"use client"

import { useState, useEffect, useMemo, useRef, useCallback } from 'react'
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import Image from "next/image"
import Link from "next/link"
import { ProcessedSkinData } from '@/lib/services/skin-service'
import { SkinRarityIconsSimple } from "@/components/ui/skin-rarity-icons"
import { useMythicPrices } from '@/hooks/use-mythic-prices'
import CountdownTimer from "@/components/ui/countdown-timer"


interface LazySkinGridProps {
  skins: ProcessedSkinData[]
  isClient: boolean
}

const INITIAL_LOAD = 60
const LOAD_INCREMENT = 30

// Rarity color mapping
const rarityColors: Record<string, string> = {
  'Regular': '#299645',
  'Legacy': '#c1a26a', 
  'Epic': '#27d7e5',
  'Legendary': '#e81b23',
  'Mythic': '#b47bdf',
  'Ultimate': '#d7861f',
  'Exalted': '#d3ce5a', // emerald-500 equivalent
  'Transcendent': '#cda4de'
}

// Function to create URL-friendly skin names
function createSkinSlug(skinName: string): string {
  return skinName
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters except spaces and hyphens
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .replace(/^-|-$/g, '') // Remove leading/trailing hyphens
}

export default function LazySkinGrid({ skins, isClient }: LazySkinGridProps) {
  const [displayCount, setDisplayCount] = useState(INITIAL_LOAD)
  const [isLoading, setIsLoading] = useState(false)
  const loadingTriggerRef = useRef<HTMLDivElement>(null)

  // Fetch mythic prices for all skins (only mythic skins will have prices)
  const { mythicPrices, loading: mythicPricesLoading } = useMythicPrices(skins)


  // Get skins to display
  const displayedSkins = useMemo(() => {
    return skins.slice(0, displayCount)
  }, [skins, displayCount])

  const hasMore = displayCount < skins.length



  // Load more skins
  const loadMore = () => {
    if (!isLoading && hasMore) {
      setIsLoading(true)
      // Reduced loading delay for smoother experience
      setTimeout(() => {
        setDisplayCount(prev => Math.min(prev + LOAD_INCREMENT, skins.length))
        setIsLoading(false)
      }, 100)
    }
  }

  // Intersection Observer for lazy loading
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries
        if (entry.isIntersecting && hasMore && !isLoading) {
          loadMore()
        }
      },
      {
        rootMargin: '200px', // Start loading 200px before the trigger comes into view
        threshold: 0.1
      }
    )

    const currentTrigger = loadingTriggerRef.current
    if (currentTrigger) {
      observer.observe(currentTrigger)
    }

    return () => {
      if (currentTrigger) {
        observer.unobserve(currentTrigger)
      }
    }
  }, [hasMore, isLoading])

  // Reset display count when skins change (due to filtering)
  useEffect(() => {
    setDisplayCount(INITIAL_LOAD)
  }, [skins])



  const getRarityStyle = (tier: string, isLegacy: boolean) => {
    if (isLegacy) {
      return {
        backgroundColor: `${rarityColors['Legacy']}20`,
        borderColor: `${rarityColors['Legacy']}40`,
        color: rarityColors['Legacy']
      }
    }

    const color = rarityColors[tier] || rarityColors['Regular']
    return {
      backgroundColor: `${color}20`,
      borderColor: `${color}40`, 
      color: color
    }
  }

  return (
    <>
      {/* Skins Grid - Match mythic shop layout */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
        {displayedSkins.map((skin) => {
          const rarityStyle = getRarityStyle(skin.tier, skin.isLegacy)

          // Special handling for upcoming skins
          const isUpcoming = skin.isUpcoming
          const skinSlug = isUpcoming ? `${createSkinSlug(skin.name)}-soon` : createSkinSlug(skin.name)

          return (
            <Link
              key={skin.id}
              href={`/skins/${skinSlug}`}
              className="block"
            >
              <div className={`bg-gradient-to-br from-gray-900/60 to-gray-800/40 border rounded-xl overflow-hidden relative group transition-all duration-500 transform hover:scale-105 cursor-pointer hover:shadow-lg ${
                isUpcoming
                  ? 'border-purple-500/50 hover:border-purple-400/70 hover:shadow-purple-500/20'
                  : 'border-purple-700/30 hover:border-purple-400/70 hover:shadow-purple-500/20'
              }`}>
                {/* Skin Image - Full card background */}
                {skin.image && (
                  <div className="relative h-64 w-full">
                    <Image
                      src={skin.image}
                      alt={skin.name}
                      fill
                      className="object-cover group-hover:scale-110 transition-transform duration-700 ease-out"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent opacity-60 group-hover:opacity-40 transition-opacity duration-300" />
                  </div>
                )}

                {/* Upcoming badge */}
                {isUpcoming && (
                  <div className="absolute top-3 left-3 z-10">
                    <Badge className="bg-purple-600/90 text-white border-purple-500/50 text-xs font-semibold">
                      UPCOMING
                    </Badge>
                  </div>
                )}

                {/* Content overlay at bottom */}
                <div className="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/90 via-black/70 to-transparent">
                  <p
                    className="text-white font-bold text-sm mb-2 drop-shadow-lg truncate"
                    title={skin.name}
                  >
                    {skin.name}
                  </p>

                  {/* Countdown timer for upcoming skins */}
                  {isUpcoming && skin.releaseCountdown && (
                    <div className="mb-2">
                      <CountdownTimer
                        targetDate={skin.releaseCountdown}
                        compact={true}
                        showIcon={false}
                        className="text-xs"
                      />
                    </div>
                  )}

                  {/* Price and rarity row */}
                  <div className="flex items-center justify-between">
                    {/* Price on the left */}
                    <div className="flex items-center space-x-1">
                      {(() => {
                        // For mythic skins, show mythic price if available
                        if (skin.tier === 'Mythic') {
                          const mythicPrice = mythicPrices.get(skin.contentId)

                          if (mythicPrice) {
                            return (
                              <>
                                <Image
                                  src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/mythic.svg"
                                  alt="ME"
                                  width={16}
                                  height={16}
                                  className="h-4 w-4"
                                />
                                <span className="font-semibold text-purple-400">
                                  {mythicPrice.cost}
                                </span>
                              </>
                            )
                          } else {
                            return <span className="text-gray-400 font-semibold">N/A</span>
                          }
                        }

                        // Regular pricing logic for non-mythic skins
                        if (skin.price !== null && skin.price !== undefined && skin.price !== 'N/A' && skin.price !== 'Free') {
                          return (
                            <>
                              {skin.price.includes('BE') ? (
                                <Image
                                  src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-navigation/global/default/rewardicons_blueessence.png"
                                  alt="BE"
                                  width={16}
                                  height={16}
                                  className="h-4 w-4"
                                />
                              ) : skin.price.includes('ME') ? (
                                <Image
                                  src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/mythic.svg"
                                  alt="ME"
                                  width={16}
                                  height={16}
                                  className="h-4 w-4"
                                />
                              ) : skin.price.includes('AS') ? (
                                <Image
                                  src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/currency/icons/ancient_spark.svg"
                                  alt="AS"
                                  width={16}
                                  height={16}
                                  className="h-4 w-4"
                                />
                              ) : (
                                <Image
                                  src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg"
                                  alt="RP"
                                  width={16}
                                  height={16}
                                  className="h-4 w-4"
                                />
                              )}
                              <span className="font-semibold" style={{
                                color: skin.price.includes('BE') ? '#38c4dc' :
                                       skin.price.includes('ME') ? '#b47bdf' :
                                       skin.price.includes('AS') ? '#2babf0' : '#fb923c'
                              }}>
                                {skin.price.replace(' RP', '').replace(' ME', '').replace(' AS', '').replace(' BE', '')}
                              </span>
                            </>
                          )
                        } else if (skin.price === 'Free') {
                          return <span className="text-green-400 font-semibold">Free</span>
                        } else {
                          return <span className="text-gray-400 font-semibold">N/A</span>
                        }
                      })()}
                    </div>

                    {/* Rarity icon on the right */}
                    <div className="flex items-center gap-1 flex-shrink-0">
                      <SkinRarityIconsSimple rarity={skin.tier} isLegacy={skin.isLegacy} isBase={skin.isBase} />
                    </div>
                  </div>
                </div>
              </div>
            </Link>
          )
        })}
      </div>

      {/* Intersection Observer Trigger */}
      {hasMore && (
        <div
          ref={loadingTriggerRef}
          className="w-full h-20 flex items-center justify-center"
        >
          {isLoading && (
            <div className="inline-flex items-center space-x-2">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-purple-400"></div>
              <span className="text-gray-300">Loading more skins...</span>
            </div>
          )}
        </div>
      )}

      {/* End message */}
      {!hasMore && displayedSkins.length > INITIAL_LOAD && (
        <div className="text-center py-8">
          <p className="text-gray-400">
            You've reached the end! Showing all {displayedSkins.length} skins.
          </p>
        </div>
      )}
    </>
  )
}
