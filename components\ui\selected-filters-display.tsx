"use client"

import * as React from "react"
import { X } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { SkinRarityIconsSimple } from "@/components/ui/skin-rarity-icons"
import { SkinFilters } from "@/hooks/use-skin-filters"

interface SelectedFiltersDisplayProps {
  filters: SkinFilters
  onRemoveFilter: (filterType: keyof SkinFilters, value: string) => void
  onClearCategory: (filterType: keyof SkinFilters) => void
}

export function SelectedFiltersDisplay({
  filters,
  onRemoveFilter,
  onClearCategory
}: SelectedFiltersDisplayProps) {
  const hasFilters = filters.tiers.length > 0 || filters.champions.length > 0 || filters.skinLines.length > 0

  if (!hasFilters) {
    return null
  }

  const renderFilterGroup = (
    title: string,
    items: string[],
    filterType: keyof SkinFilters,
    color: string,
    showIcons: boolean = false
  ) => {
    if (items.length === 0) return null

    return (
      <div className="flex items-center space-x-2">
        <span className="text-gray-400 text-sm font-medium">{title}:</span>
        <div className="flex items-center space-x-1 flex-wrap">
          {items.length <= 3 ? (
            items.map(item => (
              <Badge
                key={item}
                variant="secondary"
                className={`${color} text-xs flex items-center space-x-1 cursor-pointer hover:opacity-80 transition-opacity`}
                onClick={() => onRemoveFilter(filterType, item)}
              >
                {showIcons && (
                  <div className="w-3 h-3 flex items-center justify-center">
                    <SkinRarityIconsSimple
                      rarity={item}
                      isLegacy={item === "Legacy"}
                      isBase={false}
                      size={12}
                    />
                  </div>
                )}
                <span>{item}</span>
                <X className="h-3 w-3" />
              </Badge>
            ))
          ) : (
            <Badge
              variant="secondary"
              className={`${color} text-xs flex items-center space-x-1 cursor-pointer hover:opacity-80 transition-opacity`}
              onClick={() => onClearCategory(filterType)}
            >
              <span>{items.length} Selected</span>
              <X className="h-3 w-3" />
            </Badge>
          )}
        </div>
      </div>
    )
  }

  return (
    <div className="mb-4 p-3 bg-gray-800/30 rounded-lg border border-gray-700/20">
      <div className="flex flex-wrap items-center gap-4">
        {renderFilterGroup(
          "Rarities",
          filters.tiers,
          "tiers",
          "bg-purple-600/20 text-purple-300 border-purple-600/30",
          true
        )}
        
        {renderFilterGroup(
          "Champions",
          filters.champions,
          "champions",
          "bg-blue-600/20 text-blue-300 border-blue-600/30"
        )}
        
        {renderFilterGroup(
          "Skin Lines",
          filters.skinLines,
          "skinLines",
          "bg-green-600/20 text-green-300 border-green-600/30"
        )}
      </div>
    </div>
  )
}
