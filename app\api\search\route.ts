import { chromaService } from '@/lib/services/chroma-service'
import { skinService } from '@/lib/services/skin-service'
import { NextRequest, NextResponse } from 'next/server'
import { createChromaURL } from '@/lib/utils/chroma-url-utils'

interface SearchResult {
  id: string
  name: string
  image: string
  tag: string
  href: string
  type: 'champion' | 'skin' | 'chroma' | 'item'
}



// Helper function to get skin slug
function getSkinSlug(skinName: string, championName: string): string {
  // Keep the full skin name including champion name to match other parts of the codebase
  // Convert to slug format using the same logic as other skin pages
  return skinName
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters except spaces and hyphens
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .replace(/^-|-$/g, '') // Remove leading/trailing hyphens
}


// Helper function to get item slug
function getItemSlug(itemName: string): string {
  return itemName.toLowerCase()
    .replace(/[^a-z0-9\s]/g, '')
    .replace(/\s+/g, '-')
    .replace(/^-+|-+$/g, '')
}

async function fetchChampions() {
  try {
    const versionsResponse = await fetch('https://ddragon.leagueoflegends.com/api/versions.json')
    const versions = await versionsResponse.json()
    const currentVersion = versions[0]

    const url = `https://ddragon.leagueoflegends.com/cdn/${currentVersion}/data/en_US/champion.json`
    const response = await fetch(url)
    const data = await response.json()
    
    return Object.values(data.data).map((champion: any) => ({
      id: champion.id,
      name: champion.name,
      image: `https://ddragon.leagueoflegends.com/cdn/${currentVersion}/img/champion/${champion.image.full}`,
      tag: 'Champions',
      href: `/champions/${champion.id}`,
      type: 'champion' as const
    }))
  } catch (error) {
    console.error('Failed to fetch champions:', error)
    return []
  }
}

// Helper function to identify legitimate historical items (copied from items API)
function isHistoricalItem(item: any): boolean {
  const hasProperName = item.name &&
    item.name.trim().length > 0 &&
    !item.name.toLowerCase().includes('test') &&
    !item.name.toLowerCase().includes('placeholder') &&
    !/^[\d\s]*$/.test(item.name)

  const hasMeaningfulContent = (item.description && item.description.trim().length > 0) ||
    (item.plaintext && item.plaintext.trim().length > 0)

  const hasGoldData = item.gold && typeof item.gold === 'object'

  const hasGameplayData = (item.stats && Object.keys(item.stats).length > 0) ||
    (item.from && item.from.length > 0) ||
    (item.into && item.into.length > 0) ||
    (item.tags && item.tags.length > 0)

  return hasProperName && hasMeaningfulContent && hasGoldData && hasGameplayData
}

async function fetchItems() {
  try {
    // Directly fetch from DDragon API instead of making internal API call
    // This avoids base URL issues in production and improves performance

    // Get current game version
    const versionsResponse = await fetch('https://ddragon.leagueoflegends.com/api/versions.json')
    if (!versionsResponse.ok) {
      throw new Error('Failed to fetch game versions')
    }
    const versions = await versionsResponse.json()
    const currentVersion = versions[0]

    // Get items data directly from DDragon
    const url = `https://ddragon.leagueoflegends.com/cdn/${currentVersion}/data/en_US/item.json`
    const response = await fetch(url)

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: Failed to fetch items data`)
    }

    const rawData = await response.json()

    // Process items using the SAME logic as the items API to ensure consistency
    const items: any[] = []

    Object.entries(rawData.data).forEach(([id, item]: [string, any]) => {
      // Exclude items with requiredChampion: "Gangplank"
      if (item.requiredChampion === "Gangplank") return

      // Exclude Arena-only items (map 30 or 33 true, map 11 false)
      if (item.maps && item.maps['11'] === false && (item.maps['30'] === true || item.maps['33'] === true)) {
        return
      }

      // Apply inclusion rules (SAME as items API):
      let shouldInclude = false

      // 1. INCLUDE regular items: maps["11"] === true (Summoner's Rift)
      if (item.maps && item.maps['11'] === true) {
        shouldInclude = true
      }
      // 2. INCLUDE ARAM items: maps["12"] === true
      else if (item.maps && item.maps['12'] === true) {
        shouldInclude = true
      }
      // 3. INCLUDE historical/legacy items for Summoner's Rift
      else if (item.inStore === false && item.maps && item.maps['11'] === false && isHistoricalItem(item)) {
        shouldInclude = true
      }
      // 4. INCLUDE old ARAM items
      else if (item.inStore === false && item.maps && item.maps['12'] === false && isHistoricalItem(item)) {
        shouldInclude = true
      }

      if (!shouldInclude) return

      // Must have a valid name
      if (!item.name || !item.name.trim()) return

      items.push({
        id,
        name: item.name,
        image: `https://ddragon.leagueoflegends.com/cdn/${currentVersion}/img/item/${item.image?.full || `${id}.png`}`,
        tag: 'Items',
        href: `/items/${getItemSlug(item.name)}`,
        type: 'item' as const
      })
    })

    return items
  } catch (error) {
    console.error('Failed to fetch items:', error)
    return []
  }
}

async function fetchSkins() {
  try {
    const allSkins = await skinService.getAllSkins()

    return allSkins
      .filter(skin => !skin.isBase)
      .map(skin => ({
        id: skin.id.toString(),
        name: skin.name,
        image: skin.splashArt,
        tag: 'Skins',
        href: `/skins/${getSkinSlug(skin.name, skin.champion)}`,
        type: 'skin' as const
      }))
  } catch (error) {
    console.error('Failed to fetch skins:', error)
    return []
  }
}

async function fetchChromas() {
  try {
    const allChromas = await chromaService.getAllChromas()

    return allChromas
      .filter(chroma => !chroma.isDefault)
      .map(chroma => ({
        id: chroma.id.toString(),
        name: chroma.name,
        image: chroma.image, // Use the processed image URL from the service
        tag: 'Chromas',
        href: `/skins/chromas/${createChromaURL(chroma)}`,
        type: 'chroma' as const
      }))
  } catch (error) {
    console.error('Failed to fetch chromas:', error)
    return []
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const query = searchParams.get('q')?.toLowerCase().trim() || ''
    const random = searchParams.get('random') === 'true'
    const loadAll = searchParams.get('loadAll') === 'true'

    // Load all data for client-side caching
    if (loadAll) {
      const [champions, skins, chromas, items] = await Promise.all([
        fetchChampions(),
        fetchSkins(),
        fetchChromas(),
        fetchItems()
      ])

      const allData: SearchResult[] = [
        ...champions,
        ...skins,
        ...chromas,
        ...items
      ]

      return NextResponse.json({
        success: true,
        data: allData,
        count: allData.length,
        type: 'loadAll',
        timestamp: new Date().toISOString()
      }, {
        headers: {
          'Cache-Control': 'public, s-maxage=1800, stale-while-revalidate=3600', // 30min cache, 1h stale
          'CDN-Cache-Control': 'public, s-maxage=1800',
          'Vercel-CDN-Cache-Control': 'public, s-maxage=1800'
        }
      })
    }

    if (random) {
      // Return 8 random suggestions (2 champions, 2 skins, 2 chromas, 2 items)
      const [champions, skins, chromas, items] = await Promise.all([
        fetchChampions(),
        fetchSkins(),
        fetchChromas(),
        fetchItems()
      ])

      const randomSuggestions: SearchResult[] = []

      // Get 2 random champions
      if (champions.length > 0) {
        const shuffledChampions = [...champions].sort(() => 0.5 - Math.random())
        randomSuggestions.push(...shuffledChampions.slice(0, 2))
      }

      // Get 2 random skins
      if (skins.length > 0) {
        const shuffledSkins = [...skins].sort(() => 0.5 - Math.random())
        randomSuggestions.push(...shuffledSkins.slice(0, 2))
      }

      // Get 2 random chromas
      if (chromas.length > 0) {
        const shuffledChromas = [...chromas].sort(() => 0.5 - Math.random())
        randomSuggestions.push(...shuffledChromas.slice(0, 2))
      }

      // Get 2 random items
      if (items.length > 0) {
        const shuffledItems = [...items].sort(() => 0.5 - Math.random())
        randomSuggestions.push(...shuffledItems.slice(0, 2))
      }

      return NextResponse.json({
        success: true,
        data: randomSuggestions,
        count: randomSuggestions.length,
        type: 'random',
        timestamp: new Date().toISOString()
      })
    }

    if (!query) {
      return NextResponse.json({
        success: true,
        data: [],
        count: 0,
        type: 'empty',
        timestamp: new Date().toISOString()
      })
    }

    // Search across all categories
    const [champions, skins, chromas, items] = await Promise.all([
      fetchChampions(),
      fetchSkins(),
      fetchChromas(),
      fetchItems()
    ])

    const allResults: SearchResult[] = []

    // Search champions
    const championResults = champions.filter(champion =>
      champion.name.toLowerCase().includes(query)
    ).slice(0, 2)
    allResults.push(...championResults)

    // Search skins
    const skinResults = skins.filter(skin =>
      skin.name.toLowerCase().includes(query)
    ).slice(0, 2)
    allResults.push(...skinResults)

    // Search chromas
    const chromaResults = chromas.filter(chroma =>
      chroma.name.toLowerCase().includes(query)
    ).slice(0, 2)
    allResults.push(...chromaResults)

    // Search items
    const itemResults = items.filter(item =>
      item.name.toLowerCase().includes(query)
    ).slice(0, 2)
    allResults.push(...itemResults)

    // Limit total results to 8
    const finalResults = allResults.slice(0, 8)

    

    return NextResponse.json({
      success: true,
      data: finalResults,
      count: finalResults.length,
      query,
      type: 'search',
      timestamp: new Date().toISOString()
    }, {
      headers: {
        'Cache-Control': 'public, s-maxage=300, stale-while-revalidate=600', // 5min cache, 10min stale
        'CDN-Cache-Control': 'public, s-maxage=300',
        'Vercel-CDN-Cache-Control': 'public, s-maxage=300'
      }
    })

  } catch (error) {
    console.error('❌ Search API error:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Failed to perform search',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}
