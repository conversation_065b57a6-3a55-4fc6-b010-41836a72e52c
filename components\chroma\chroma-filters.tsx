"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import { ChromaFilters } from "@/hooks/use-chroma-filters"
import { Search, Filter, X, ChevronDown, RotateCcw } from "lucide-react"

interface ChromaFiltersProps {
  filters: ChromaFilters
  onFiltersChange: (filters: Partial<ChromaFilters>) => void
  onResetFilters: () => void
  availableChampions: string[]
  availableSkinTiers: string[]
  availableSets: string[]
  availableColors: string[]
  availableAvailability: string[]
  availablePriceRanges: string[]
  className?: string
}

export default function ChromaFiltersComponent({
  filters,
  onFiltersChange,
  onResetFilters,
  availableChampions,
  availableSkinTiers,
  availableSets,
  availableColors,
  availableAvailability,
  availablePriceRanges,
  className = ""
}: ChromaFiltersProps) {
  const [openSections, setOpenSections] = useState<Record<string, boolean>>({
    champions: false,
    skinTiers: true,
    sets: false,
    colors: false,
    availability: false,
    priceRange: false
  })

  const toggleSection = (section: string) => {
    setOpenSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }))
  }

  const handleCheckboxChange = (
    filterKey: keyof ChromaFilters,
    value: string,
    checked: boolean
  ) => {
    const currentValues = filters[filterKey] as string[]
    let newValues: string[]

    if (checked) {
      newValues = [...currentValues, value]
    } else {
      newValues = currentValues.filter(v => v !== value)
    }

    onFiltersChange({ [filterKey]: newValues })
  }

  const removeFilter = (filterKey: keyof ChromaFilters, value: string) => {
    const currentValues = filters[filterKey] as string[]
    const newValues = currentValues.filter(v => v !== value)
    onFiltersChange({ [filterKey]: newValues })
  }

  const hasActiveFilters = () => {
    return (
      filters.search ||
      filters.champions.length > 0 ||
      filters.chromaRarities.length > 0 ||
      filters.chromaColors.length > 0 ||
      filters.availability.length > 0 ||
      filters.priceRange.length > 0
    )
  }

  const getActiveFilterCount = () => {
    return (
      (filters.search ? 1 : 0) +
      filters.champions.length +
      filters.chromaRarities.length +
      filters.chromaColors.length +
      filters.availability.length +
      filters.priceRange.length
    )
  }

  const renderFilterSection = (
    title: string,
    key: keyof ChromaFilters,
    options: string[],
    sectionKey: string
  ) => (
    <Collapsible
      open={openSections[sectionKey]}
      onOpenChange={() => toggleSection(sectionKey)}
    >
      <CollapsibleTrigger asChild>
        <Button
          variant="ghost"
          className="w-full justify-between p-2 h-auto text-left"
        >
          <span className="text-sm font-medium text-white">
            {title}
            {(filters[key] as string[]).length > 0 && (
              <Badge variant="secondary" className="ml-2 text-xs">
                {(filters[key] as string[]).length}
              </Badge>
            )}
          </span>
          <ChevronDown className={`h-4 w-4 transition-transform ${
            openSections[sectionKey] ? 'rotate-180' : ''
          }`} />
        </Button>
      </CollapsibleTrigger>
      <CollapsibleContent>
        <ScrollArea className="h-32">
          <div className="space-y-2 p-2">
            {options.map((option) => (
              <div key={option} className="flex items-center space-x-2">
                <Checkbox
                  id={`${sectionKey}-${option}`}
                  checked={(filters[key] as string[]).includes(option)}
                  onCheckedChange={(checked) =>
                    handleCheckboxChange(key, option, checked as boolean)
                  }
                />
                <label
                  htmlFor={`${sectionKey}-${option}`}
                  className="text-sm text-gray-300 cursor-pointer flex-1"
                >
                  {option}
                </label>
              </div>
            ))}
          </div>
        </ScrollArea>
      </CollapsibleContent>
    </Collapsible>
  )

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Filter className="h-5 w-5 text-purple-400" />
          <h3 className="text-lg font-semibold text-white">Filters</h3>
          {getActiveFilterCount() > 0 && (
            <Badge variant="secondary" className="bg-purple-600 text-white">
              {getActiveFilterCount()}
            </Badge>
          )}
        </div>
        {hasActiveFilters() && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onResetFilters}
            className="text-gray-400 hover:text-white"
          >
            <RotateCcw className="h-4 w-4 mr-1" />
            Reset
          </Button>
        )}
      </div>

      {/* Search */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        <Input
          placeholder="Search chromas, champions, skins..."
          value={filters.search}
          onChange={(e) => onFiltersChange({ search: e.target.value })}
          className="pl-10 bg-gray-800/50 border-gray-700 text-white placeholder-gray-400"
        />
      </div>

      {/* Active Filters */}
      {hasActiveFilters() && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-300">Active Filters:</h4>
          <div className="flex flex-wrap gap-2">
            {filters.search && (
              <Badge
                variant="secondary"
                className="bg-purple-600/20 text-purple-300 border-purple-600/30"
              >
                Search: {filters.search}
                <button
                  onClick={() => onFiltersChange({ search: '' })}
                  className="ml-1 hover:text-white"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            )}
            {filters.champions.map((champion) => (
              <Badge
                key={champion}
                variant="secondary"
                className="bg-blue-600/20 text-blue-300 border-blue-600/30"
              >
                {champion}
                <button
                  onClick={() => removeFilter('champions', champion)}
                  className="ml-1 hover:text-white"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            ))}
            {filters.chromaRarities.map((tier) => (
              <Badge
                key={tier}
                variant="secondary"
                className="bg-green-600/20 text-green-300 border-green-600/30"
              >
                {tier}
                <button
                  onClick={() => removeFilter('chromaRarities', tier)}
                  className="ml-1 hover:text-white"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            ))}
            {filters.chromaColors.map((color) => (
              <Badge
                key={color}
                variant="secondary"
                className="bg-orange-600/20 text-orange-300 border-orange-600/30"
              >
                {color}
                <button
                  onClick={() => removeFilter('chromaColors', color)}
                  className="ml-1 hover:text-white"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            ))}
            {filters.availability.map((availability) => (
              <Badge
                key={availability}
                variant="secondary"
                className="bg-yellow-600/20 text-yellow-300 border-yellow-600/30"
              >
                {availability}
                <button
                  onClick={() => removeFilter('availability', availability)}
                  className="ml-1 hover:text-white"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            ))}
            {filters.priceRange.map((price) => (
              <Badge
                key={price}
                variant="secondary"
                className="bg-red-600/20 text-red-300 border-red-600/30"
              >
                {price}
                <button
                  onClick={() => removeFilter('priceRange', price)}
                  className="ml-1 hover:text-white"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            ))}
          </div>
        </div>
      )}

      {/* Filter Sections */}
      <div className="space-y-2 border border-gray-700/50 rounded-lg p-3 bg-gray-900/20">
        {renderFilterSection('Champions', 'champions', availableChampions, 'champions')}
        {renderFilterSection('Chroma Rarities', 'chromaRarities', availableSkinTiers, 'chromaRarities')}
        {renderFilterSection('Chroma Colors', 'chromaColors', availableColors, 'chromaColors')}
        {renderFilterSection('Availability', 'availability', availableAvailability, 'availability')}
        {renderFilterSection('Price Range', 'priceRange', availablePriceRanges, 'priceRange')}
      </div>
    </div>
  )
}
