"use client"

import { ChampionWinRateGraph } from "@/components/champion/champion-winrate-graph"
import SharedLayout from "@/components/shared-layout"
import { useEffect } from "react"

export default function WinRateGraphDemo() {
  // Apply blue background
  useEffect(() => {
    const blueGradient = 'linear-gradient(135deg, hsl(220 50% 8%) 0%, hsl(220 39% 11%) 50%, hsl(215 30% 6%) 100%)'

    document.body.style.background = blueGradient
    document.body.style.backgroundColor = 'hsl(220 39% 11%)'
    document.body.style.backgroundRepeat = 'no-repeat'
    document.documentElement.style.background = blueGradient
    document.documentElement.style.backgroundColor = 'hsl(220 39% 11%)'
    document.documentElement.style.backgroundRepeat = 'no-repeat'

    return () => {
      document.body.style.background = ''
      document.body.style.backgroundColor = ''
      document.documentElement.style.background = ''
      document.documentElement.style.backgroundColor = ''
    }
  }, [])

  return (
    <SharedLayout>
      <div className="container mx-auto px-8 py-8">
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">Win Rate Graph Demo</h1>
          <p className="text-slate-400 text-lg">
            Interactive win rate graph component for League of Legends champions
          </p>
        </div>

        {/* Demo with different champions */}
        <div className="space-y-8">
          <ChampionWinRateGraph championName="Jinx" />
          <ChampionWinRateGraph championName="Yasuo" />
          <ChampionWinRateGraph championName="Ahri" />
        </div>

        {/* Features list */}
        <div className="mt-12 bg-slate-900/80 backdrop-blur-md rounded-lg border border-slate-700/50 p-6">
          <h2 className="text-2xl font-bold text-white mb-4">Features</h2>
          <ul className="text-slate-300 space-y-2">
            <li>• Interactive rank selector with rank icons</li>
            <li>• Smooth SVG-based line chart with hover effects</li>
            <li>• Custom tooltips showing detailed win rate data</li>
            <li>• Responsive design that works on all screen sizes</li>
            <li>• Mock data for all ranks from Iron to Challenger</li>
            <li>• Color-coded average win rate (green for positive, red for negative)</li>
            <li>• Matches League of Legends website color scheme</li>
          </ul>
        </div>
      </div>
    </SharedLayout>
  )
}
