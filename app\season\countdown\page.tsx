"use client"

import SharedLayout from "@/components/shared-layout"
import { Card, CardContent } from "@/components/ui/card"
import { Clock, Calendar, Trophy, Target } from "lucide-react"
import { useState, useEffect } from "react"

interface SeasonData {
  endDate: string
  title: string
}



export default function SeasonCountdownPage() {
  const [timeLeft, setTimeLeft] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0,
  })

  const [seasonData, setSeasonData] = useState<SeasonData | null>(null)
  const [loading, setLoading] = useState(true)
  const [apiError, setApiError] = useState(false)

  // Fetch season data from API
  useEffect(() => {
    const fetchSeasonData = async () => {
      try {
        const response = await fetch('https://api.loldb.info/api/activity-center/client-nav')
        if (!response.ok) {
          throw new Error(`Failed to fetch season data: HTTP ${response.status}`)
        }

        const data = await response.json()

        // Find the item with createdAt "2025-06-12T00:51:56Z" in the nested data array
        const seasonItem = data.data?.data?.find((item: any) => item.createdAt === "2025-06-12T00:51:56Z")

        if (seasonItem && seasonItem.endsAt) {
          setSeasonData({
            endDate: seasonItem.endsAt,
            title: seasonItem.title || "Season"
          })
        } else {
          throw new Error('Season data with specified createdAt not found in API response')
        }
      } catch (err) {
        console.error('❌ Failed to fetch season data:', err)
        setApiError(true)
      } finally {
        setLoading(false)
      }
    }

    fetchSeasonData()
  }, [])



  // Update countdown timer for main season countdown
  useEffect(() => {
    if (!seasonData?.endDate) return

    const targetDate = new Date(seasonData.endDate).getTime()

    const timer = setInterval(() => {
      const now = new Date().getTime()
      const difference = targetDate - now

      if (difference > 0) {
        setTimeLeft({
          days: Math.floor(difference / (1000 * 60 * 60 * 24)),
          hours: Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
          minutes: Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60)),
          seconds: Math.floor((difference % (1000 * 60)) / 1000),
        })
      } else {
        setTimeLeft({ days: 0, hours: 0, minutes: 0, seconds: 0 })
      }
    }, 1000)

    return () => clearInterval(timer)
  }, [seasonData])



  return (
    <SharedLayout>
      <div className="container mx-auto px-8 pb-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-4">
            <Clock className="h-8 w-8 text-purple-400" />
            <h1 className="text-4xl font-bold text-white">Season Countdown</h1>
          </div>
          <p className="text-gray-300 text-lg">Live countdown to important season milestones</p>
        </div>

        {/* Main Countdown */}
        <div className="mb-8">
          <Card className="bg-gradient-to-r from-purple-900/20 to-blue-900/20 border-purple-700/30">
            <CardContent className="p-8 text-center">
              <div className="flex items-center justify-center space-x-3 mb-6">
                <Trophy className="h-8 w-8 text-yellow-400" />
                <h2 className="text-3xl font-bold text-white">Season 2 - Act 2 Ends In</h2>
              </div>

              {loading ? (
                <div className="py-12">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-400 mx-auto mb-4"></div>
                  <p className="text-gray-400">Loading season data...</p>
                </div>
              ) : (
                <>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                    <div className="bg-gray-900/40 rounded-lg p-4">
                      <div className="text-4xl font-bold text-purple-400 mb-2">{timeLeft.days}</div>
                      <div className="text-gray-400 text-sm">Days</div>
                    </div>
                    <div className="bg-gray-900/40 rounded-lg p-4">
                      <div className="text-4xl font-bold text-blue-400 mb-2">{timeLeft.hours}</div>
                      <div className="text-gray-400 text-sm">Hours</div>
                    </div>
                    <div className="bg-gray-900/40 rounded-lg p-4">
                      <div className="text-4xl font-bold text-cyan-400 mb-2">{timeLeft.minutes}</div>
                      <div className="text-gray-400 text-sm">Minutes</div>
                    </div>
                    <div className="bg-gray-900/40 rounded-lg p-4">
                      <div className="text-4xl font-bold text-green-400 mb-2">{timeLeft.seconds}</div>
                      <div className="text-gray-400 text-sm">Seconds</div>
                    </div>
                  </div>
                  <div className="flex items-center justify-center space-x-2 mb-4">
                    {apiError ? (
                      <>
                        <div className="w-2 h-2 bg-red-400 rounded-full"></div>
                        <p className="text-gray-300 text-sm">Data not Found</p>
                      </>
                    ) : seasonData ? (
                      <>
                        <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                        <p className="text-gray-300 text-sm">Live data from API</p>
                      </>
                    ) : null}
                  </div>
                </>
              )}

              <p className="text-gray-300">Make sure to secure your rank before the split ends!</p>
            </CardContent>
          </Card>
        </div>

        {/* Upcoming Milestones */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-white mb-6 flex items-center">
            <Target className="h-6 w-6 mr-2 text-orange-400" />
            Upcoming Milestones
          </h2>

          <div className="space-y-4">
            <Card className="bg-gray-900/40 border-red-700/20">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-2">Season Two Ends in</h3>
                    <div className="flex items-center text-gray-400 text-sm">
                      <Calendar className="h-4 w-4 mr-2" />
                      {seasonData?.endDate ?
                        new Date(seasonData.endDate).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric',
                          hour: 'numeric',
                          minute: '2-digit',
                          timeZoneName: 'short'
                        }) :
                        'Loading...'
                      }
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-red-400">{timeLeft.days}</div>
                    <div className="text-gray-400 text-sm">days left</div>
                  </div>
                </div>
              </CardContent>
            </Card>


          </div>
        </div>

        {/* Important Notes */}
        <div className="bg-gray-900/40 border border-purple-700/20 rounded-lg p-6">
          <h3 className="text-xl font-semibold text-white mb-4">Important Reminders</h3>
          <div className="space-y-3 text-gray-300">
            <p>• Ranked rewards are distributed based on your current rank when the season ends, not your highest rank achieved</p>
            <p>• Make sure to play at least one ranked game every 28 days to avoid decay (applies only to Diamond+ ranks)</p>
            <p>• Season rewards include exclusive Victorious skins for players who win 15 ranked games</p>
            <p>• End-of-season rewards require Honor level 3 or higher</p>
            <p>• Ranked queues may be disabled briefly before season end for maintenance</p>
          </div>
        </div>
      </div>
    </SharedLayout>
  )
}
