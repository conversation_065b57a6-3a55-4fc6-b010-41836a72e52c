"use client"

import React, { useEffect } from 'react'

interface ConversionBackgroundProps {
  customImageUrl?: string
  children: React.ReactNode
  blur?: number
  brightness?: number
  overlay?: string
}

export default function ConversionBackground({
  customImageUrl,
  children,
  blur = 10,
  brightness = 0.25,
  overlay = 'bg-gradient-to-b from-black/40 via-black/20 to-black/60'
}: ConversionBackgroundProps) {
  const backgroundUrl = customImageUrl || '/images/SkinSalesCards.jpg'

  useEffect(() => {
    if (backgroundUrl) {
      // Create background element with blur effect
      const backgroundElement = document.createElement('div')
      backgroundElement.id = 'conversion-background-image'
      backgroundElement.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        z-index: 0;
        background-image: url('${backgroundUrl}');
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        filter: blur(${blur}px) brightness(${brightness}) saturate(1.2);
        transform: scale(1.1);
        pointer-events: none;
      `
      document.body.insertBefore(backgroundElement, document.body.firstChild)

      // Add lighter overlay
      const overlay = document.createElement('div')
      overlay.id = 'conversion-background-overlay'
      overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        z-index: 2;
        background: linear-gradient(to bottom, rgba(0,0,0,0.2), rgba(0,0,0,0.1), rgba(0,0,0,0.3)),
                    linear-gradient(to top, rgba(17, 24, 39, 0.7), transparent, rgba(17, 24, 39, 0.3));
        pointer-events: none;
      `
      document.body.insertBefore(overlay, document.body.firstChild)

      // Set main content z-index for proper layering
      const main = document.querySelector('main')
      if (main instanceof HTMLElement) {
        main.style.position = 'relative'
        main.style.zIndex = '10'
      }

      // Set footer z-index for proper layering
      const footer = document.querySelector('footer')
      if (footer instanceof HTMLElement) {
        footer.style.position = 'relative'
        footer.style.zIndex = '10'
      }

      // Handle sidebar separately to preserve its fixed positioning
      const sidebar = document.querySelector('aside')
      if (sidebar instanceof HTMLElement) {
        sidebar.style.zIndex = '50' // Higher than background but lower than header
        // Don't change position as it's already fixed
      }
    }

    // Cleanup function
    return () => {
      const backgroundElement = document.getElementById('conversion-background-image')
      const overlayElement = document.getElementById('conversion-background-overlay')
      
      if (backgroundElement) {
        backgroundElement.remove()
      }
      if (overlayElement) {
        overlayElement.remove()
      }

      // Reset main content z-index
      const main = document.querySelector('main')
      if (main instanceof HTMLElement) {
        main.style.position = ''
        main.style.zIndex = ''
      }

      // Reset footer z-index
      const footer = document.querySelector('footer')
      if (footer instanceof HTMLElement) {
        footer.style.position = ''
        footer.style.zIndex = ''
      }

      // Reset sidebar z-index
      const sidebar = document.querySelector('aside')
      if (sidebar instanceof HTMLElement) {
        sidebar.style.zIndex = ''
      }
    }
  }, [backgroundUrl, blur, brightness])

  return (
    <div className="relative min-h-screen z-10">
      {children}
    </div>
  )
}
