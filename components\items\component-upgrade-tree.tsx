"use client"

import { useState, useEffect } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { Card, CardContent } from '@/components/ui/card'

interface ItemData {
  id: string
  name: string
  description: string
  plaintext: string
  image: string
  category: string
  formattedPrice: string
  sellPrice: string
  components: string[]
  buildsInto: string[]
  isPurchasable: boolean
  tags: string[]
  gold: {
    base: number
    total: number
    sell: number
    purchasable: boolean
  }
  stats: Record<string, number>
  mainStats: string[]
  isFullItem: boolean
  maps: Record<string, boolean>
  availability: 'Available' | 'Legacy' | 'Removed'
}

interface ComponentUpgradeTreeProps {
  item: ItemData
  allItems: ItemData[]
  onItemClick: (itemSlug: string) => void
}

// Function to create URL-friendly item names
function createItemSlug(itemName: string): string {
  return itemName
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters except spaces and hyphens
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .replace(/^-|-$/g, '') // Remove leading/trailing hyphens
}

export default function ComponentUpgradeTree({ item, allItems, onItemClick }: ComponentUpgradeTreeProps) {
  const [upgradeItems, setUpgradeItems] = useState<ItemData[]>([])

  useEffect(() => {
    if (item && allItems.length > 0) {
      // Find all items this component can upgrade into
      const upgrades = allItems.filter(i => item.buildsInto.includes(i.id))

      // Filter to only show secondary items (items that have both "from" and "into")
      const secondaryItems = upgrades.filter(upgrade =>
        upgrade.components && upgrade.components.length > 0 &&
        upgrade.buildsInto && upgrade.buildsInto.length > 0
      )

      setUpgradeItems(secondaryItems)
    }
  }, [item, allItems])

  // Only show this component for items that have "into" but no "from" (component items)
  const isComponentItem = (!item.components || item.components.length === 0) && item.buildsInto && item.buildsInto.length > 0

  // Check if this is a standalone item (no build path at all)
  const isStandaloneItem = (!item.components || item.components.length === 0) &&
                           (!item.buildsInto || item.buildsInto.length === 0)

  if (!isComponentItem) {
    if (isStandaloneItem) {
      // Show upgrade path layout with placeholder for standalone items
      return (
        <Card className="bg-gray-900/40 border-blue-700/20 overflow-hidden">
          <CardContent className="p-4">
            <div className="flex flex-col items-center">
              <h3 className="text-base sm:text-lg font-semibold text-white mb-1 sm:mb-2 text-center">Component Upgrades</h3>
              <p className="text-xs sm:text-sm text-gray-400 mb-3 sm:mb-4 text-center">This item has no upgrade path</p>

              {/* Standalone Item Layout */}
              <div className="w-full max-w-xs sm:max-w-md md:max-w-2xl lg:max-w-4xl">
                <div className="flex flex-col items-center w-full space-y-4 sm:space-y-6 md:space-y-8">

                  {/* Placeholder for upgrades - TOP */}
                  <div className="flex justify-center items-center gap-2 sm:gap-3 md:gap-4 lg:gap-6">
                    <Card className="bg-gray-800/40 border-gray-600/30 w-14 h-20 sm:w-16 sm:h-24 md:w-20 md:h-28">
                      <CardContent className="p-1 sm:p-1.5 md:p-2 h-full">
                        <div className="flex flex-col items-center text-center h-full justify-center">
                          <div className="text-lg sm:text-xl md:text-2xl font-bold text-gray-400 mb-1">
                            N/A
                          </div>
                          <div className="text-[8px] sm:text-[10px] text-gray-500 leading-tight">
                            No Upgrades
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Standalone Item - BOTTOM CENTER */}
                  <div>
                    <Card className="bg-blue-900/40 border-blue-400/50 ring-2 ring-blue-400/30 shadow-lg shadow-blue-400/20 w-14 h-20 sm:w-16 sm:h-24 md:w-20 md:h-28">
                      <CardContent className="p-1 sm:p-1.5 md:p-2 h-full">
                        <div className="flex flex-col items-center text-center h-full justify-between">
                          <div className="w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 flex-shrink-0">
                            <Image
                              src={item.image || "/placeholder.svg"}
                              alt={item.name}
                              width={40}
                              height={40}
                              className="w-full h-full object-contain"
                            />
                          </div>
                          <h4 className="text-[10px] sm:text-xs font-medium line-clamp-2 text-blue-200 flex-grow flex items-center min-h-[1.5rem] sm:min-h-[2rem] mb-0.5 sm:mb-1">
                            <span className="leading-tight">{item.name}</span>
                          </h4>
                          <div className="flex items-center gap-0.5 sm:gap-1 flex-shrink-0">
                            {item.formattedPrice === 'Legacy' || item.formattedPrice === 'N/A' ? (
                              <span className="text-[10px] sm:text-xs text-gray-400">
                                {item.formattedPrice}
                              </span>
                            ) : (
                              <>
                                <span className="text-[10px] sm:text-xs" style={{ color: '#fb923c' }}>
                                  {item.gold.total}
                                </span>
                                <Image
                                  src="https://raw.communitydragon.org/latest/game/assets/ux/tft/regionportals/icon/gold.png"
                                  alt="Gold"
                                  width={8}
                                  height={8}
                                  className="w-1.5 h-1.5 sm:w-2 sm:h-2"
                                  style={{
                                    userSelect: 'none',
                                    pointerEvents: 'none',
                                    filter: 'brightness(0) saturate(100%) invert(73%) sepia(85%) saturate(1234%) hue-rotate(359deg) brightness(95%) contrast(95%)'
                                  }}
                                  draggable={false}
                                />
                              </>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </div>

              {/* Legend */}
              <div className="mt-3 sm:mt-4 flex flex-wrap justify-center gap-2 sm:gap-3 text-[10px] sm:text-xs">
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 sm:w-2.5 sm:h-2.5 rounded border border-blue-400/50 bg-blue-900/40"></div>
                  <span className="text-gray-300">Standalone Item</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 sm:w-2.5 sm:h-2.5 rounded border border-gray-600/30 bg-gray-800/40"></div>
                  <span className="text-gray-300">No Upgrade Path</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )
    }

    // For regular items that are not component items, show regular image
    return (
      <Card className="bg-gray-900/40 border-blue-700/20 overflow-hidden">
        <CardContent className="p-0">
          <div className="aspect-square relative">
            <Image
              src={item.image || "/placeholder.svg"}
              alt={item.name}
              fill
              className="object-contain p-8"
              priority
            />
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="bg-gray-900/40 border-blue-700/20 overflow-hidden">
      <CardContent className="p-4">
        <div className="flex flex-col items-center">
          <h3 className="text-base sm:text-lg font-semibold text-white mb-1 sm:mb-2 text-center">Component Upgrades</h3>
          <p className="text-xs sm:text-sm text-gray-400 mb-3 sm:mb-4 text-center">Click on items to view their details</p>

          {/* Upgrade Tree Layout */}
          <div className="w-full max-w-xs sm:max-w-md md:max-w-2xl lg:max-w-4xl">
            <div className="flex flex-col items-center w-full space-y-4 sm:space-y-6 md:space-y-8">
              
              {/* Upgraded Items - TOP */}
              {upgradeItems.length > 0 && (
                <div className="flex justify-center items-center gap-2 sm:gap-3 md:gap-4 lg:gap-6 flex-wrap">
                  {/* Show first 4 items */}
                  {upgradeItems.slice(0, 4).map((upgradeItem) => (
                    <Link
                      key={upgradeItem.id}
                      href={`/items/${createItemSlug(upgradeItem.name)}`}
                      className="block"
                    >
                      <Card className="bg-blue-900/40 border-blue-400/50 hover:border-blue-300/70 transition-all duration-300 cursor-pointer hover:scale-105 hover:shadow-md w-14 h-20 sm:w-16 sm:h-24 md:w-20 md:h-28">
                      <CardContent className="p-1 sm:p-1.5 md:p-2 h-full">
                        <div className="flex flex-col items-center text-center h-full justify-between">
                          <div className="w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 flex-shrink-0">
                            <Image
                              src={upgradeItem.image || "/placeholder.svg"}
                              alt={upgradeItem.name}
                              width={40}
                              height={40}
                              className="w-full h-full object-contain"
                            />
                          </div>
                          <h4 className="text-[10px] sm:text-xs font-medium line-clamp-2 text-blue-200 flex-grow flex items-center min-h-[1.5rem] sm:min-h-[2rem] mb-0.5 sm:mb-1">
                            <span className="leading-tight">{upgradeItem.name}</span>
                          </h4>
                          <div className="flex items-center gap-0.5 sm:gap-1 flex-shrink-0">
                            {upgradeItem.formattedPrice === 'Legacy' || upgradeItem.formattedPrice === 'N/A' ? (
                              <span className="text-[10px] sm:text-xs text-gray-400">
                                {upgradeItem.formattedPrice}
                              </span>
                            ) : (
                              <>
                                <span className="text-[10px] sm:text-xs" style={{ color: '#fb923c' }}>
                                  {upgradeItem.gold.total}
                                </span>
                                <Image
                                  src="https://raw.communitydragon.org/latest/game/assets/ux/tft/regionportals/icon/gold.png"
                                  alt="Gold"
                                  width={8}
                                  height={8}
                                  className="w-1.5 h-1.5 sm:w-2 sm:h-2"
                                  style={{
                                    userSelect: 'none',
                                    pointerEvents: 'none',
                                    filter: 'brightness(0) saturate(100%) invert(73%) sepia(85%) saturate(1234%) hue-rotate(359deg) brightness(95%) contrast(95%)'
                                  }}
                                  draggable={false}
                                />
                              </>
                            )}
                          </div>
                        </div>
                      </CardContent>
                      </Card>
                    </Link>
                  ))}

                  {/* Show +X card if there are more than 4 items */}
                  {upgradeItems.length > 4 && (
                    <Card className="bg-orange-900/40 border-orange-400/50 hover:border-orange-300/70 transition-all duration-300 w-14 h-20 sm:w-16 sm:h-24 md:w-20 md:h-28">
                      <CardContent className="p-1 sm:p-1.5 md:p-2 h-full">
                        <div className="flex flex-col items-center text-center h-full justify-center">
                          <div className="text-lg sm:text-xl md:text-2xl font-bold text-orange-200 mb-1">
                            +{upgradeItems.length - 4}
                          </div>
                          <div className="text-[8px] sm:text-[10px] text-orange-300 leading-tight">
                            More Items
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </div>
              )}

              {/* Base Component Item - BOTTOM CENTER */}
              <div>
                <Card className="bg-green-900/40 border-green-400/50 ring-2 ring-green-400/30 shadow-lg shadow-green-400/20 w-14 h-20 sm:w-16 sm:h-24 md:w-20 md:h-28">
                  <CardContent className="p-1 sm:p-1.5 md:p-2 h-full">
                    <div className="flex flex-col items-center text-center h-full justify-between">
                      <div className="w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 flex-shrink-0">
                        <Image
                          src={item.image || "/placeholder.svg"}
                          alt={item.name}
                          width={40}
                          height={40}
                          className="w-full h-full object-contain"
                        />
                      </div>
                      <h4 className="text-[10px] sm:text-xs font-medium line-clamp-2 text-green-200 flex-grow flex items-center min-h-[1.5rem] sm:min-h-[2rem] mb-0.5 sm:mb-1">
                        <span className="leading-tight">{item.name}</span>
                      </h4>
                      <div className="flex items-center gap-0.5 sm:gap-1 flex-shrink-0">
                        {item.formattedPrice === 'Legacy' || item.formattedPrice === 'N/A' ? (
                          <span className="text-[10px] sm:text-xs text-gray-400">
                            {item.formattedPrice}
                          </span>
                        ) : (
                          <>
                            <span className="text-[10px] sm:text-xs" style={{ color: '#fb923c' }}>
                              {item.gold.total}
                            </span>
                            <Image
                              src="https://raw.communitydragon.org/latest/game/assets/ux/tft/regionportals/icon/gold.png"
                              alt="Gold"
                              width={8}
                              height={8}
                              className="w-1.5 h-1.5 sm:w-2 sm:h-2"
                              style={{
                                userSelect: 'none',
                                pointerEvents: 'none',
                                filter: 'brightness(0) saturate(100%) invert(73%) sepia(85%) saturate(1234%) hue-rotate(359deg) brightness(95%) contrast(95%)'
                              }}
                              draggable={false}
                            />
                          </>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>

          {/* Legend */}
          <div className="mt-3 sm:mt-4 flex flex-wrap justify-center gap-2 sm:gap-3 text-[10px] sm:text-xs">
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 sm:w-2.5 sm:h-2.5 rounded border border-green-400/50 bg-green-900/40"></div>
              <span className="text-gray-300">Base Component</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 sm:w-2.5 sm:h-2.5 rounded border border-blue-400/50 bg-blue-900/40"></div>
              <span className="text-gray-300">Secondary Item</span>
            </div>
            {upgradeItems.length > 4 && (
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 sm:w-2.5 sm:h-2.5 rounded border border-orange-400/50 bg-orange-900/40"></div>
                <span className="text-gray-300">More Items</span>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
