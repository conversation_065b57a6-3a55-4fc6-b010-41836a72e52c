"use client"

import { useState, useEffect } from 'react'
import { Clock } from 'lucide-react'

interface CountdownTimerProps {
  targetDate: string // ISO date string
  className?: string
  showIcon?: boolean
  compact?: boolean // For smaller displays
}

interface TimeRemaining {
  days: number
  hours: number
  minutes: number
  seconds: number
  expired: boolean
}

function calculateTimeRemaining(targetDate: string): TimeRemaining {
  const now = new Date().getTime()
  const target = new Date(targetDate).getTime()
  const difference = target - now

  if (difference <= 0) {
    return {
      days: 0,
      hours: 0,
      minutes: 0,
      seconds: 0,
      expired: true
    }
  }

  return {
    days: Math.floor(difference / (1000 * 60 * 60 * 24)),
    hours: Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
    minutes: Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60)),
    seconds: Math.floor((difference % (1000 * 60)) / 1000),
    expired: false
  }
}

export default function CountdownTimer({ 
  targetDate, 
  className = "", 
  showIcon = true, 
  compact = false 
}: CountdownTimerProps) {
  const [timeRemaining, setTimeRemaining] = useState<TimeRemaining>(() => 
    calculateTimeRemaining(targetDate)
  )

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeRemaining(calculateTimeRemaining(targetDate))
    }, 1000)

    return () => clearInterval(timer)
  }, [targetDate])

  if (timeRemaining.expired) {
    return (
      <div className={`flex items-center gap-2 text-gray-400 ${className}`}>
        {showIcon && <Clock className="h-4 w-4" />}
        <span className="text-sm">Released</span>
      </div>
    )
  }

  if (compact) {
    // Compact version for skin cards
    return (
      <div className={`flex items-center gap-2 text-purple-300 ${className}`}>
        {showIcon && <Clock className="h-4 w-4" />}
        <span className="text-sm font-medium">
          {timeRemaining.days > 0 
            ? `${timeRemaining.days}d ${timeRemaining.hours}h`
            : timeRemaining.hours > 0
            ? `${timeRemaining.hours}h ${timeRemaining.minutes}m`
            : `${timeRemaining.minutes}m ${timeRemaining.seconds}s`
          }
        </span>
      </div>
    )
  }

  // Full version for detail pages
  return (
    <div className={`${className}`}>
      <div className="flex items-center gap-2 mb-3">
        {showIcon && <Clock className="h-5 w-5 text-purple-400" />}
        <span className="text-lg font-semibold text-white">Releases In</span>
      </div>
      
      <div className="grid grid-cols-4 gap-3">
        <div className="bg-gray-900/40 rounded-lg p-3 text-center">
          <div className="text-2xl font-bold text-purple-400 mb-1">{timeRemaining.days}</div>
          <div className="text-gray-400 text-xs">Days</div>
        </div>
        <div className="bg-gray-900/40 rounded-lg p-3 text-center">
          <div className="text-2xl font-bold text-blue-400 mb-1">{timeRemaining.hours}</div>
          <div className="text-gray-400 text-xs">Hours</div>
        </div>
        <div className="bg-gray-900/40 rounded-lg p-3 text-center">
          <div className="text-2xl font-bold text-cyan-400 mb-1">{timeRemaining.minutes}</div>
          <div className="text-gray-400 text-xs">Minutes</div>
        </div>
        <div className="bg-gray-900/40 rounded-lg p-3 text-center">
          <div className="text-2xl font-bold text-green-400 mb-1">{timeRemaining.seconds}</div>
          <div className="text-gray-400 text-xs">Seconds</div>
        </div>
      </div>
    </div>
  )
}
