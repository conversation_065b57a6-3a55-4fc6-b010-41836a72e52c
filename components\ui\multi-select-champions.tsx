"use client"

import * as React from "react"
import { Check, ChevronDown, X, Search } from "lucide-react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Input } from "@/components/ui/input"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Badge } from "@/components/ui/badge"

interface MultiSelectChampionsProps {
  selectedChampions: string[]
  onSelectionChange: (champions: string[]) => void
  availableChampions: string[]
  placeholder?: string
  className?: string
}

export function MultiSelectChampions({
  selectedChampions,
  onSelectionChange,
  availableChampions,
  placeholder = "Select champions...",
  className
}: MultiSelectChampionsProps) {
  const [open, setOpen] = React.useState(false)
  const [searchTerm, setSearchTerm] = React.useState("")

  const filteredChampions = React.useMemo(() => {
    if (!searchTerm.trim()) return availableChampions
    
    return availableChampions.filter(champion =>
      champion.toLowerCase().includes(searchTerm.toLowerCase())
    )
  }, [availableChampions, searchTerm])

  const handleChampionToggle = (champion: string) => {
    const newSelection = selectedChampions.includes(champion)
      ? selectedChampions.filter(c => c !== champion)
      : [...selectedChampions, champion]
    
    onSelectionChange(newSelection)
  }

  const handleClearAll = () => {
    onSelectionChange([])
  }

  const renderSelectedBadges = () => {
    if (selectedChampions.length === 0) {
      return <span className="text-gray-400">{placeholder}</span>
    }

    return (
      <span className="text-gray-300">
        Champions +{selectedChampions.length}
      </span>
    )
  }

  return (
    <div className={className}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between bg-gray-900/40 border-gray-700/20 text-white hover:bg-gray-800/50 h-[40px] items-center"
          >
            <div className="flex-1 text-left">
              {renderSelectedBadges()}
            </div>
            <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0 bg-gray-900 border-gray-700" align="start">
          <div className="p-3">
            <div className="flex items-center justify-between mb-3">
              <span className="text-sm font-medium text-gray-300">Select Champions</span>
              {selectedChampions.length > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleClearAll}
                  className="text-xs text-gray-400 hover:text-white h-6"
                >
                  Clear All
                </Button>
              )}
            </div>
            
            {/* Search Input */}
            <div className="relative mb-3">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                type="text"
                placeholder="Search champions..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-gray-800/50 border-gray-600/50 text-white placeholder-gray-400 focus:border-blue-400/60"
              />
            </div>

            <div className="space-y-1 max-h-60 overflow-y-auto">
              {filteredChampions.length === 0 ? (
                <div className="text-center py-4 text-gray-400 text-sm">
                  No champions found
                </div>
              ) : (
                filteredChampions.map((champion) => (
                  <div
                    key={champion}
                    className="flex items-center space-x-3 p-1 rounded hover:bg-gray-800/50 cursor-pointer"
                    onClick={() => handleChampionToggle(champion)}
                  >
                    <Checkbox
                      checked={selectedChampions.includes(champion)}
                      onCheckedChange={() => handleChampionToggle(champion)}
                      className="border-gray-600 data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
                    />
                    <span className="text-gray-300">{champion}</span>
                  </div>
                ))
              )}
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  )
}
