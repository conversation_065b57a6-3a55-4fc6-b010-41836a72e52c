"use client"

import { useState, useEffect } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { Card, CardContent } from '@/components/ui/card'

interface ItemData {
  id: string
  name: string
  description: string
  plaintext: string
  image: string
  category: string
  formattedPrice: string
  sellPrice: string
  components: string[]
  buildsInto: string[]
  isPurchasable: boolean
  tags: string[]
  gold: {
    base: number
    total: number
    sell: number
    purchasable: boolean
  }
  stats: Record<string, number>
  mainStats: string[]
  isFullItem: boolean
  maps: Record<string, boolean>
  availability: 'Available' | 'Legacy' | 'Removed'
}

interface BuildTreeNode {
  item: ItemData
  components: BuildTreeNode[]
  level: number
}

interface ItemBuildTreeProps {
  item: ItemData
  allItems: ItemData[]
  onItemClick: (itemSlug: string) => void
}

// Function to create URL-friendly item names
function createItemSlug(itemName: string): string {
  return itemName
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters except spaces and hyphens
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .replace(/^-|-$/g, '') // Remove leading/trailing hyphens
}

export default function ItemBuildTree({ item, allItems, onItemClick }: ItemBuildTreeProps) {
  const [buildTree, setBuildTree] = useState<BuildTreeNode | null>(null)

  // Build the tree structure recursively
  const buildTreeRecursive = (currentItem: ItemData, level: number = 0): BuildTreeNode => {
    const componentNodes: BuildTreeNode[] = []
    
    if (currentItem.components && currentItem.components.length > 0) {
      currentItem.components.forEach(componentId => {
        const componentItem = allItems.find(i => i.id === componentId)
        if (componentItem) {
          componentNodes.push(buildTreeRecursive(componentItem, level + 1))
        }
      })
    }

    return {
      item: currentItem,
      components: componentNodes,
      level
    }
  }

  useEffect(() => {
    if (item && allItems.length > 0) {
      const tree = buildTreeRecursive(item)
      setBuildTree(tree)
    }
  }, [item, allItems])



  // Create a dynamic grid structure without empty placeholders
  const renderDynamicGridTree = (node: BuildTreeNode) => {
    const finalItem = node.item

    // Get all actual items in the tree
    const allTreeItems = new Map<string, ItemData>()
    const collectItems = (currentNode: BuildTreeNode) => {
      allTreeItems.set(currentNode.item.id, currentNode.item)
      currentNode.components.forEach(child => collectItems(child))
    }
    collectItems(node)

    // Create parent-child mapping
    const parentChildMap = new Map<string, string[]>()
    const buildParentChildMap = (currentNode: BuildTreeNode) => {
      if (currentNode.components.length > 0) {
        parentChildMap.set(currentNode.item.id, currentNode.components.map(c => c.item.id))
        currentNode.components.forEach(child => buildParentChildMap(child))
      }
    }
    buildParentChildMap(node)

    // Get actual secondary items (only those that exist)
    const actualSecondaryItems = (parentChildMap.get(finalItem.id) || [])
      .map(id => allTreeItems.get(id))
      .filter(Boolean) as ItemData[]

    // Create component groups for each secondary item
    const componentGroups = actualSecondaryItems.map(secondaryItem => {
      const componentIds = parentChildMap.get(secondaryItem.id) || []
      const components = componentIds
        .map(id => allTreeItems.get(id))
        .filter(Boolean) as ItemData[]
      return {
        secondary: secondaryItem,
        components
      }
    })



    return (
      <div className="flex flex-col items-center w-full space-y-4 sm:space-y-6 md:space-y-8">
        {/* Level 0: Final Item */}
        <div>
          <Card className="bg-blue-900/40 border-blue-400/50 ring-2 ring-blue-400/30 shadow-lg shadow-blue-400/20 w-14 h-20 sm:w-16 sm:h-24 md:w-20 md:h-28">
            <CardContent className="p-1 sm:p-1.5 md:p-2 h-full">
              <div className="flex flex-col items-center text-center h-full justify-between">
                <div className="w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 flex-shrink-0">
                  <Image
                    src={finalItem.image || "/placeholder.svg"}
                    alt={finalItem.name}
                    width={40}
                    height={40}
                    className="w-full h-full object-contain"
                  />
                </div>
                <h4 className="text-[10px] sm:text-xs font-medium line-clamp-2 text-blue-200 flex-grow flex items-center min-h-[1.5rem] sm:min-h-[2rem] mb-0.5 sm:mb-1">
                  <span className="leading-tight">{finalItem.name}</span>
                </h4>
                <div className="flex items-center gap-0.5 sm:gap-1 flex-shrink-0">
                  {finalItem.formattedPrice === 'Legacy' || finalItem.formattedPrice === 'N/A' ? (
                    <span className="text-[10px] sm:text-xs text-gray-400">
                      {finalItem.formattedPrice}
                    </span>
                  ) : (
                    <>
                      <span className="text-[10px] sm:text-xs" style={{ color: '#fb923c' }}>
                        {finalItem.gold.total}
                      </span>
                      <Image
                        src="https://raw.communitydragon.org/latest/game/assets/ux/tft/regionportals/icon/gold.png"
                        alt="Gold"
                        width={8}
                        height={8}
                        className="w-1.5 h-1.5 sm:w-2 sm:h-2"
                        style={{
                          userSelect: 'none',
                          pointerEvents: 'none',
                          filter: 'brightness(0) saturate(100%) invert(73%) sepia(85%) saturate(1234%) hue-rotate(359deg) brightness(95%) contrast(95%)'
                        }}
                        draggable={false}
                      />
                    </>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Level 1 & 2: All Secondary Items positioned in a single row */}
        <div className="flex justify-center items-start gap-3 sm:gap-4 md:gap-6 lg:gap-8">
          {actualSecondaryItems.map((secondaryItem, itemIndex) => {
            // Find the component group for this secondary item
            const componentGroup = componentGroups.find(group => group.secondary.id === secondaryItem.id)
            const components = componentGroup ? componentGroup.components : []

            return (
              <div key={secondaryItem.id} className="flex flex-col items-center gap-3 sm:gap-4 md:gap-6 lg:gap-8">
                {/* Secondary Item positioned above its components */}
                <div>
                  <Link href={`/items/${createItemSlug(secondaryItem.name)}`} className="block">
                    <Card className="bg-gray-900/40 border-blue-700/20 hover:border-blue-400/50 transition-all duration-300 cursor-pointer hover:scale-105 hover:shadow-md w-14 h-20 sm:w-16 sm:h-24 md:w-20 md:h-28">
                    <CardContent className="p-1 sm:p-1.5 md:p-2 h-full">
                      <div className="flex flex-col items-center text-center h-full justify-between">
                        <div className="w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 flex-shrink-0">
                          <Image
                            src={secondaryItem.image || "/placeholder.svg"}
                            alt={secondaryItem.name}
                            width={40}
                            height={40}
                            className="w-full h-full object-contain"
                          />
                        </div>
                        <h4 className="text-[10px] sm:text-xs font-medium line-clamp-2 text-white flex-grow flex items-center min-h-[1.5rem] sm:min-h-[2rem] mb-0.5 sm:mb-1">
                          <span className="leading-tight">{secondaryItem.name}</span>
                        </h4>
                        <div className="flex items-center gap-0.5 sm:gap-1 flex-shrink-0">
                          {secondaryItem.formattedPrice === 'Legacy' || secondaryItem.formattedPrice === 'N/A' ? (
                            <span className="text-[10px] sm:text-xs text-gray-400">
                              {secondaryItem.formattedPrice}
                            </span>
                          ) : (
                            <>
                              <span className="text-[10px] sm:text-xs" style={{ color: '#fb923c' }}>
                                {secondaryItem.gold.total}
                              </span>
                              <Image
                                src="https://raw.communitydragon.org/latest/game/assets/ux/tft/regionportals/icon/gold.png"
                                alt="Gold"
                                width={8}
                                height={8}
                                className="w-1.5 h-1.5 sm:w-2 sm:h-2"
                                style={{
                                  userSelect: 'none',
                                  pointerEvents: 'none',
                                  filter: 'brightness(0) saturate(100%) invert(73%) sepia(85%) saturate(1234%) hue-rotate(359deg) brightness(95%) contrast(95%)'
                                }}
                                draggable={false}
                              />
                            </>
                          )}
                        </div>
                      </div>
                    </CardContent>
                    </Card>
                  </Link>
                </div>

                {/* Components positioned below their secondary item (if any) */}
                {components.length > 0 && (
                  <div className="flex gap-1 sm:gap-1.5 md:gap-2">
                    {components.map((component, componentIndex) => (
                    <Link
                      key={component.id}
                      href={`/items/${createItemSlug(component.name)}`}
                      className="block"
                    >
                      <Card className="bg-green-900/40 border-green-400/50 hover:border-green-300/70 transition-all duration-300 cursor-pointer hover:scale-105 hover:shadow-md w-14 h-20 sm:w-16 sm:h-24 md:w-20 md:h-28">
                      <CardContent className="p-1 sm:p-1.5 md:p-2 h-full">
                        <div className="flex flex-col items-center text-center h-full justify-between">
                          <div className="w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 flex-shrink-0">
                            <Image
                              src={component.image || "/placeholder.svg"}
                              alt={component.name}
                              width={40}
                              height={40}
                              className="w-full h-full object-contain"
                            />
                          </div>
                          <h4 className="text-[10px] sm:text-xs font-medium line-clamp-2 text-green-200 flex-grow flex items-center min-h-[1.5rem] sm:min-h-[2rem] mb-0.5 sm:mb-1">
                            <span className="leading-tight">{component.name}</span>
                          </h4>
                          <div className="flex items-center gap-0.5 sm:gap-1 flex-shrink-0">
                            {component.formattedPrice === 'Legacy' || component.formattedPrice === 'N/A' ? (
                              <span className="text-[10px] sm:text-xs text-gray-400">
                                {component.formattedPrice}
                              </span>
                            ) : (
                              <>
                                <span className="text-[10px] sm:text-xs" style={{ color: '#fb923c' }}>
                                  {component.gold.total}
                                </span>
                                <Image
                                  src="https://raw.communitydragon.org/latest/game/assets/ux/tft/regionportals/icon/gold.png"
                                  alt="Gold"
                                  width={8}
                                  height={8}
                                  className="w-1.5 h-1.5 sm:w-2 sm:h-2"
                                  style={{
                                    userSelect: 'none',
                                    pointerEvents: 'none',
                                    filter: 'brightness(0) saturate(100%) invert(73%) sepia(85%) saturate(1234%) hue-rotate(359deg) brightness(95%) contrast(95%)'
                                  }}
                                  draggable={false}
                                />
                              </>
                            )}
                          </div>
                        </div>
                      </CardContent>
                        </Card>
                      </Link>
                    ))}
                  </div>
                )}
              </div>
            )
          })}
        </div>


      </div>
    )
  }

  if (!buildTree) {
    return (
      <Card className="bg-gray-900/40 border-blue-700/20">
        <CardContent className="p-6">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-400 mx-auto mb-4"></div>
            <p className="text-gray-300">Building item tree...</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  // If item has no components, check if it's a standalone item (no from/into)
  if (!item.components || item.components.length === 0) {
    // Check if this is a standalone item (no build path at all)
    const isStandaloneItem = (!item.components || item.components.length === 0) &&
                             (!item.buildsInto || item.buildsInto.length === 0)

    if (isStandaloneItem) {
      // Show build path layout with placeholder for standalone items
      return (
        <Card className="bg-gray-900/40 border-blue-700/20 overflow-hidden">
          <CardContent className="p-2 sm:p-3 md:p-4">
            <div className="flex flex-col items-center">
              <h3 className="text-base sm:text-lg font-semibold text-white mb-1 sm:mb-2 text-center">Build Path</h3>
              <p className="text-xs sm:text-sm text-gray-400 mb-3 sm:mb-4 text-center">This item has no build path</p>

              {/* Standalone Item Layout */}
              <div className="w-full max-w-xs sm:max-w-md md:max-w-2xl lg:max-w-4xl">
                <div className="flex flex-col items-center w-full space-y-4 sm:space-y-6 md:space-y-8">

                  {/* Final Item - TOP */}
                  <div>
                    <Card className="bg-blue-900/40 border-blue-400/50 ring-2 ring-blue-400/30 shadow-lg shadow-blue-400/20 w-14 h-20 sm:w-16 sm:h-24 md:w-20 md:h-28">
                      <CardContent className="p-1 sm:p-1.5 md:p-2 h-full">
                        <div className="flex flex-col items-center text-center h-full justify-between">
                          <div className="w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 flex-shrink-0">
                            <Image
                              src={item.image || "/placeholder.svg"}
                              alt={item.name}
                              width={40}
                              height={40}
                              className="w-full h-full object-contain"
                            />
                          </div>
                          <h4 className="text-[10px] sm:text-xs font-medium line-clamp-2 text-blue-200 flex-grow flex items-center min-h-[1.5rem] sm:min-h-[2rem] mb-0.5 sm:mb-1">
                            <span className="leading-tight">{item.name}</span>
                          </h4>
                          <div className="flex items-center gap-0.5 sm:gap-1 flex-shrink-0">
                            {item.formattedPrice === 'Legacy' || item.formattedPrice === 'N/A' ? (
                              <span className="text-[10px] sm:text-xs text-gray-400">
                                {item.formattedPrice}
                              </span>
                            ) : (
                              <>
                                <span className="text-[10px] sm:text-xs" style={{ color: '#fb923c' }}>
                                  {item.gold.total}
                                </span>
                                <Image
                                  src="https://raw.communitydragon.org/latest/game/assets/ux/tft/regionportals/icon/gold.png"
                                  alt="Gold"
                                  width={8}
                                  height={8}
                                  className="w-1.5 h-1.5 sm:w-2 sm:h-2"
                                  style={{
                                    userSelect: 'none',
                                    pointerEvents: 'none',
                                    filter: 'brightness(0) saturate(100%) invert(73%) sepia(85%) saturate(1234%) hue-rotate(359deg) brightness(95%) contrast(95%)'
                                  }}
                                  draggable={false}
                                />
                              </>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Placeholder for components - BOTTOM */}
                  <div className="flex justify-center items-center gap-3 sm:gap-4 md:gap-6 lg:gap-8">
                    <Card className="bg-gray-800/40 border-gray-600/30 w-14 h-20 sm:w-16 sm:h-24 md:w-20 md:h-28">
                      <CardContent className="p-1 sm:p-1.5 md:p-2 h-full">
                        <div className="flex flex-col items-center text-center h-full justify-center">
                          <div className="text-lg sm:text-xl md:text-2xl font-bold text-gray-400 mb-1">
                            N/A
                          </div>
                          <div className="text-[8px] sm:text-[10px] text-gray-500 leading-tight">
                            No Components
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </div>

              {/* Legend */}
              <div className="mt-3 sm:mt-4 flex flex-wrap justify-center gap-2 sm:gap-3 text-[10px] sm:text-xs">
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 sm:w-2.5 sm:h-2.5 rounded border border-blue-400/50 bg-blue-900/40"></div>
                  <span className="text-gray-300">Standalone Item</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 sm:w-2.5 sm:h-2.5 rounded border border-gray-600/30 bg-gray-800/40"></div>
                  <span className="text-gray-300">No Build Path</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )
    }

    // For items with no components but have buildsInto, show regular image
    return (
      <Card className="bg-gray-900/40 border-blue-700/20 overflow-hidden">
        <CardContent className="p-0">
          <div className="aspect-square relative">
            <Image
              src={item.image || "/placeholder.svg"}
              alt={item.name}
              fill
              className="object-contain p-8"
              priority
            />
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="bg-gray-900/40 border-blue-700/20 overflow-hidden">
      <CardContent className="p-2 sm:p-3 md:p-4">
        <div className="flex flex-col items-center">
          <h3 className="text-base sm:text-lg font-semibold text-white mb-1 sm:mb-2 text-center">Build Path</h3>
          <p className="text-xs sm:text-sm text-gray-400 mb-3 sm:mb-4 text-center">Click on components to view their details</p>

          {/* Dynamic Grid Tree Layout */}
          <div className="w-full max-w-xs sm:max-w-md md:max-w-2xl lg:max-w-4xl">
            {renderDynamicGridTree(buildTree)}
          </div>

          {/* Legend */}
          <div className="mt-3 sm:mt-4 flex flex-wrap justify-center gap-2 sm:gap-3 text-[10px] sm:text-xs">
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 sm:w-2.5 sm:h-2.5 rounded border border-blue-400/50 bg-blue-900/40"></div>
              <span className="text-gray-300">Final Item</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 sm:w-2.5 sm:h-2.5 rounded border border-blue-700/20 bg-gray-900/40"></div>
              <span className="text-gray-300">Secondary Item</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 sm:w-2.5 sm:h-2.5 rounded border border-green-400/50 bg-green-900/40"></div>
              <span className="text-gray-300">Basic Component</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
