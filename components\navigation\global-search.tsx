"use client"

import { useState, useRef, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/sheet"
import { DidYouMean } from "@/components/ui/did-you-mean"
import { Search, X, ChevronRight } from "lucide-react"
import { useSearch, useMobileSearch } from "@/hooks/use-search"
import { useIsMobile } from "@/hooks/use-mobile"
import Image from "next/image"

export default function GlobalSearch() {
  const [isDesktopSearchOpen, setIsDesktopSearchOpen] = useState(false)
  const [showDropdown, setShowDropdown] = useState(false)
  const [canScrollDown, setCanScrollDown] = useState(false)
  const searchInputRef = useRef<HTMLInputElement>(null)
  const dropdownRef = useRef<HTMLDivElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const scrollContainerRef = useRef<HTMLDivElement>(null)
  const isMobile = useIsMobile()
  
  // Use mobile search hook for mobile, regular search hook for desktop
  const mobileSearch = useMobileSearch()
  const desktopSearch = useSearch()

  // Check if container can scroll down
  const checkScrollability = () => {
    if (scrollContainerRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = scrollContainerRef.current
      // Show indicator only when content overflows AND user hasn't scrolled yet
      setCanScrollDown(scrollHeight > clientHeight && scrollTop === 0)
    }
  }

  // Check scrollability when results change
  useEffect(() => {
    checkScrollability()
  }, [mobileSearch.currentSuggestions, mobileSearch.loading])

  // Focus input when desktop search opens
  useEffect(() => {
    if (isDesktopSearchOpen && searchInputRef.current) {
      searchInputRef.current.focus()
    }
  }, [isDesktopSearchOpen])

  // Click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setShowDropdown(false)
        setIsDesktopSearchOpen(false)
        desktopSearch.clearSearch()
      }
    }

    if (isDesktopSearchOpen) {
      document.addEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isDesktopSearchOpen, desktopSearch])

  // Prevent main page scroll when scrolling in dropdown
  useEffect(() => {
    const handleWheel = (event: WheelEvent) => {
      if (dropdownRef.current && dropdownRef.current.contains(event.target as Node)) {
        const dropdown = dropdownRef.current.querySelector('[data-scrollable]') as HTMLElement
        if (dropdown) {
          const { scrollTop, scrollHeight, clientHeight } = dropdown
          const isScrollingUp = event.deltaY < 0
          const isScrollingDown = event.deltaY > 0
          
          // Prevent main page scroll if we're at the boundaries
          if (
            (isScrollingUp && scrollTop === 0) ||
            (isScrollingDown && scrollTop + clientHeight >= scrollHeight)
          ) {
            event.preventDefault()
          }
        }
      }
    }

    if (isDesktopSearchOpen) {
      document.addEventListener('wheel', handleWheel, { passive: false })
    }

    return () => {
      document.removeEventListener('wheel', handleWheel)
    }
  }, [isDesktopSearchOpen])

  // Handle desktop search open/close
  const handleDesktopSearchToggle = () => {
    if (isDesktopSearchOpen) {
      setShowDropdown(false)
      setIsDesktopSearchOpen(false)
      desktopSearch.clearSearch()
    } else {
      setIsDesktopSearchOpen(true)
      // Fetch random suggestions immediately when opening
      if (desktopSearch.randomSuggestions.length === 0) {
        desktopSearch.fetchRandomSuggestions()
      }
      // Show dropdown after animation completes (300ms)
      setTimeout(() => {
        setShowDropdown(true)
      }, 300)
    }
  }

  // Handle result click for desktop
  const handleDesktopResultClick = (href: string) => {
    window.location.href = href
    setShowDropdown(false)
    setIsDesktopSearchOpen(false)
    desktopSearch.clearSearch()
  }

  // Get tag color based on type
  const getTagColor = (tag: string) => {
    switch (tag) {
      case "Champions":
        return "bg-orange-500/20 text-orange-300 border-orange-500/30"
      case "Items":
        return "bg-blue-500/20 text-blue-300 border-blue-500/30"
      case "Skins":
        return "bg-purple-500/20 text-purple-300 border-purple-500/30"
      case "Chromas":
        return "bg-pink-500/20 text-pink-300 border-pink-500/30"
      default:
        return "bg-gray-600/20 text-gray-300 border-gray-500/30"
    }
  }

  if (isMobile) {
    // Mobile: Full-screen overlay
    return (
      <Sheet open={mobileSearch.isOpen} onOpenChange={(open) => {
        if (open) {
          mobileSearch.openSearch()
        } else {
          mobileSearch.closeSearch()
        }
      }}>
        <SheetTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className="text-white hover:bg-orange-600/20"
          >
            <Search className="h-5 w-5" />
            <span className="sr-only">Global Search</span>
          </Button>
        </SheetTrigger>
        <SheetContent
          side="top"
          className="h-screen bg-gray-950/95 backdrop-blur-md border-orange-800/20 p-0 [&>button]:hidden"
        >
          <SheetTitle className="sr-only">Search Everything</SheetTitle>
          <div className="p-6 h-full flex flex-col">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-white">Search Everything</h2>
              <Button
                variant="ghost"
                size="icon"
                onClick={mobileSearch.closeSearch}
                className="text-gray-400 hover:text-white"
              >
                <X className="h-5 w-5" />
              </Button>
            </div>

            <div className="flex-1 flex flex-col max-h-[38vh] overflow-hidden">
              <div className="relative mb-4">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  type="text"
                  placeholder="Search champions, items, skins, chromas..."
                  value={mobileSearch.searchQuery}
                  onChange={(e) => mobileSearch.setSearchQuery(e.target.value)}
                  className="pl-10 pr-4 py-3 bg-gray-800/50 border-gray-600 text-white placeholder-gray-400 focus:border-orange-400"
                  autoFocus
                />
              </div>

              {/* Search Results Container - Fixed height to prevent layout shift */}
              <div
                ref={scrollContainerRef}
                className="flex-1 min-h-[250px] max-h-[300px] overflow-y-auto scrollbar-thin scrollbar-thumb-orange-400/60 scrollbar-track-gray-800/40 relative border-t border-b border-gray-700/30"
                onScroll={checkScrollability}
                onWheel={(e) => {
                  // Always prevent main page scroll when scrolling in mobile dropdown
                  e.stopPropagation()

                  const target = e.currentTarget
                  const { scrollTop, scrollHeight, clientHeight } = target
                  const isScrollingUp = e.deltaY < 0
                  const isScrollingDown = e.deltaY > 0

                  // Prevent scrolling beyond boundaries
                  if (
                    (isScrollingUp && scrollTop <= 0) ||
                    (isScrollingDown && scrollTop + clientHeight >= scrollHeight)
                  ) {
                    e.preventDefault()
                  }
                }}
              >
                {(mobileSearch.loading || mobileSearch.isSearching) ? (
                  <div className="flex items-center justify-center p-4 h-full">
                    <div className="text-center">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-orange-400 mx-auto mb-2"></div>
                      <p className="text-gray-400 text-sm">Searching...</p>
                    </div>
                  </div>
                ) : mobileSearch.currentSuggestions.length > 0 ? (
                  <div className="space-y-2">
                    {mobileSearch.currentSuggestions.map((suggestion) => (
                      <div
                        key={`${suggestion.type}-${suggestion.id}`}
                        onClick={() => mobileSearch.handleResultClick(suggestion.href)}
                        className="flex items-center p-3 hover:bg-gray-800/50 rounded-lg transition-all duration-150 group/mobile-item cursor-pointer"
                      >
                        <div className="w-10 h-10 mr-3 rounded-lg overflow-hidden bg-gray-800/50 flex-shrink-0">
                          <Image
                            src={suggestion.image}
                            alt={suggestion.name}
                            width={40}
                            height={40}
                            className="w-full h-full object-cover group-hover/mobile-item:scale-105 transition-transform duration-200"
                          />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="text-white font-medium text-sm mb-1 group-hover/mobile-item:text-orange-300 transition-colors">
                            {suggestion.name}
                          </div>
                          <div className="flex items-center">
                            <span
                              className={`inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium border ${getTagColor(suggestion.tag)}`}
                            >
                              {suggestion.tag}
                            </span>
                          </div>
                        </div>
                        <ChevronRight className="h-4 w-4 text-gray-500 transition-all duration-150 flex-shrink-0 group-hover/mobile-item:text-orange-400 group-hover/mobile-item:translate-x-1" />
                      </div>
                    ))}
                  </div>
                ) : mobileSearch.searchQuery.trim() ? (
                  // No results found - show suggestions or sad bee emote
                  <div className="h-full">
                    {mobileSearch.showSuggestions ? (
                      <div className="p-4">
                        <div className="flex items-center justify-center mb-6">
                          <div className="text-center">
                            <div className="relative mx-auto mb-3 w-16 h-16">
                              <Image
                                src="https://raw.communitydragon.org/latest/game/assets/loadouts/summoneremotes/flairs/em_bee_sad_glow.png"
                                alt="No results glow"
                                width={64}
                                height={64}
                                className="absolute inset-0 opacity-60"
                                unoptimized
                              />
                              <Image
                                src="https://raw.communitydragon.org/latest/game/assets/loadouts/summoneremotes/flairs/em_bee_sad_vfx.png"
                                alt="No results"
                                width={64}
                                height={64}
                                className="relative z-10"
                                unoptimized
                              />
                            </div>
                            <p className="text-gray-400 text-sm">
                              No exact matches found
                            </p>
                          </div>
                        </div>
                        <DidYouMean
                          suggestions={mobileSearch.searchSuggestions}
                          onSuggestionClick={mobileSearch.handleSuggestionClick}
                          variant="orange"
                        />
                      </div>
                    ) : (
                      <div className="flex items-center justify-center h-full">
                        <div className="text-center">
                          <div className="relative mx-auto mb-4 w-20 h-20">
                            <Image
                              src="https://raw.communitydragon.org/latest/game/assets/loadouts/summoneremotes/flairs/em_bee_sad_glow.png"
                              alt="No results glow"
                              width={80}
                              height={80}
                              className="absolute inset-0 opacity-60"
                              unoptimized
                            />
                            <Image
                              src="https://raw.communitydragon.org/latest/game/assets/loadouts/summoneremotes/flairs/em_bee_sad_vfx.png"
                              alt="No results"
                              width={80}
                              height={80}
                              className="relative z-10"
                              unoptimized
                            />
                          </div>
                          <h3 className="text-lg font-semibold text-white mb-2">No results found</h3>
                          <p className="text-gray-400 text-sm">
                            Try searching for champions, skins, items, or chromas
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  // Default state - show popular searches
                  <div className="flex items-center justify-center h-full">
                    <div className="text-center">
                      <Search className="h-8 w-8 mx-auto mb-3 text-gray-500" />
                      <p className="text-gray-400 text-sm">Start typing to search everything...</p>
                    </div>
                  </div>
                )}

                {/* Scroll indicator */}
                {canScrollDown && (
                  <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-gray-950/95 to-transparent pointer-events-none flex items-end justify-center pb-1 z-10">
                    <div className="flex items-center text-orange-400/70 text-xs animate-bounce bg-gray-950/90 px-2 py-1 rounded-full border border-orange-400/20">
                      <span className="mr-1">Scroll for more</span>
                      <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                      </svg>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Clickable area beneath popular searches to close search */}
            <div
              className="flex-1 cursor-pointer"
              onClick={mobileSearch.closeSearch}
              aria-label="Close search"
            />
          </div>
        </SheetContent>
      </Sheet>
    )
  }

  // Desktop: Animated search bar
  return (
    <div className="relative flex items-center" ref={containerRef}>
      {/* Search Input - animates from icon position */}
      <div
        className={`relative transition-all duration-300 ease-out origin-right ${
          isDesktopSearchOpen
            ? 'w-80 opacity-100 scale-x-100'
            : 'w-80 opacity-0 scale-x-0'
        }`}
        style={{
          transformOrigin: 'right center'
        }}
      >
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        <Input
          ref={searchInputRef}
          type="text"
          placeholder="Search everything..."
          value={desktopSearch.searchQuery}
          onChange={(e) => desktopSearch.setSearchQuery(e.target.value)}
          className="pl-10 pr-4 py-2 w-full bg-gray-800/50 border-gray-600 text-white placeholder-gray-400 focus:border-orange-400"
        />

        {/* Desktop Search Results Dropdown - positioned relative to search input */}
        {showDropdown && (
          <div
            ref={dropdownRef}
            className="absolute top-full left-0 mt-2 w-80 bg-gray-900 border border-gray-700 rounded-lg shadow-xl overflow-hidden z-[9999]"
            style={{
              position: 'absolute',
              top: '100%',
              left: '0',
              marginTop: '8px',
              zIndex: 9999
            }}
          >
            {/* Fixed height container to prevent layout shift */}
            <div className="h-[240px] overflow-y-auto scrollbar-thin scrollbar-thumb-orange-400/20 scrollbar-track-gray-800/20">
              <div className="p-2 h-full">
                {(desktopSearch.loading || desktopSearch.isSearching) ? (
                  <div className="flex items-center justify-center h-full">
                    <div className="text-center">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-orange-400 mx-auto mb-2"></div>
                      <p className="text-gray-400 text-sm">
                        Searching...
                      </p>
                    </div>
                  </div>
                ) : desktopSearch.currentSuggestions.length > 0 ? (
                  <div
                    className="space-y-1"
                    onWheel={(e) => {
                      // Always prevent main page scroll when scrolling in dropdown
                      e.stopPropagation()

                      const target = e.currentTarget.parentElement as HTMLElement
                      if (target) {
                        const { scrollTop, scrollHeight, clientHeight } = target
                        const isScrollingUp = e.deltaY < 0
                        const isScrollingDown = e.deltaY > 0

                        // Prevent scrolling beyond boundaries
                        if (
                          (isScrollingUp && scrollTop <= 0) ||
                          (isScrollingDown && scrollTop + clientHeight >= scrollHeight)
                        ) {
                          e.preventDefault()
                        }
                      }
                    }}
                  >
                    {desktopSearch.currentSuggestions.map((suggestion) => (
                      <div
                        key={`${suggestion.type}-${suggestion.id}`}
                        onClick={() => handleDesktopResultClick(suggestion.href)}
                        className="flex items-center p-3 hover:bg-gray-800/50 rounded-lg cursor-pointer group transition-all duration-150"
                      >
                        <div className="w-10 h-10 lg:w-12 lg:h-12 mr-3 rounded-lg overflow-hidden bg-gray-800/50 flex-shrink-0">
                          <Image
                            src={suggestion.image}
                            alt={suggestion.name}
                            width={48}
                            height={48}
                            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                          />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="text-white font-medium text-sm mb-1 group-hover:text-orange-300 transition-colors">
                            {suggestion.name}
                          </div>
                          <div className="flex items-center">
                            <span
                              className={`inline-flex items-center px-2 py-0.5 rounded-md text-xs font-medium border ${getTagColor(
                                suggestion.tag
                              )}`}
                            >
                              {suggestion.tag}
                            </span>
                          </div>
                        </div>
                        <ChevronRight className="h-4 w-4 text-gray-500 group-hover:text-orange-400 group-hover:translate-x-1 transition-all duration-150 flex-shrink-0" />
                      </div>
                    ))}
                  </div>
                ) : desktopSearch.searchQuery.trim() ? (
                  // No results found - show suggestions or sad bee emote
                  <div className="h-full">
                    {desktopSearch.showSuggestions ? (
                      <div className="p-3">
                        <div className="flex items-center justify-center mb-4">
                          <div className="text-center">
                            <div className="relative mx-auto mb-3 w-12 h-12">
                              <Image
                                src="https://raw.communitydragon.org/latest/game/assets/loadouts/summoneremotes/flairs/em_bee_sad_glow.png"
                                alt="No results glow"
                                width={48}
                                height={48}
                                className="absolute inset-0 opacity-60"
                                unoptimized
                              />
                              <Image
                                src="https://raw.communitydragon.org/latest/game/assets/loadouts/summoneremotes/flairs/em_bee_sad_vfx.png"
                                alt="No results"
                                width={48}
                                height={48}
                                className="relative z-10"
                                unoptimized
                              />
                            </div>
                            <p className="text-gray-400 text-xs">
                              No exact matches found
                            </p>
                          </div>
                        </div>
                        <DidYouMean
                          suggestions={desktopSearch.searchSuggestions}
                          onSuggestionClick={desktopSearch.handleSuggestionClick}
                          variant="orange"
                        />
                      </div>
                    ) : (
                      <div className="flex items-center justify-center h-full">
                        <div className="text-center">
                          <div className="relative mx-auto mb-4 w-16 h-16">
                            <Image
                              src="https://raw.communitydragon.org/latest/game/assets/loadouts/summoneremotes/flairs/em_bee_sad_glow.png"
                              alt="No results glow"
                              width={64}
                              height={64}
                              className="absolute inset-0 opacity-60"
                              unoptimized
                            />
                            <Image
                              src="https://raw.communitydragon.org/latest/game/assets/loadouts/summoneremotes/flairs/em_bee_sad_vfx.png"
                              alt="No results"
                              width={64}
                              height={64}
                              className="relative z-10"
                              unoptimized
                            />
                          </div>
                          <h3 className="text-base font-semibold text-white mb-2">No results found</h3>
                          <p className="text-gray-400 text-sm">
                            Try searching for champions, skins, items, or chromas
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  // Default state - show click to see suggestions
                  <div className="flex items-center justify-center h-full">
                    <div className="text-center text-gray-400">
                      <Search className="h-6 w-6 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">
                        Click to see suggestions
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Search/Close Icon Button - always visible, morphs between states */}
      <Button
        variant="ghost"
        size="icon"
        className={`text-white hover:bg-orange-600/20 transition-all duration-300 ${
          isDesktopSearchOpen ? 'ml-2' : 'ml-0'
        }`}
        onClick={handleDesktopSearchToggle}
      >
        <div className="relative w-5 h-5">
          {/* Search Icon */}
          <Search
            className={`absolute inset-0 h-5 w-5 transition-all duration-300 ${
              isDesktopSearchOpen
                ? 'opacity-0 rotate-90 scale-75'
                : 'opacity-100 rotate-0 scale-100'
            }`}
          />
          {/* X Icon */}
          <X
            className={`absolute inset-0 h-4 w-4 transition-all duration-300 ${
              isDesktopSearchOpen
                ? 'opacity-100 rotate-0 scale-100'
                : 'opacity-0 -rotate-90 scale-75'
            }`}
          />
        </div>
        <span className="sr-only">
          {isDesktopSearchOpen ? 'Close Search' : 'Global Search'}
        </span>
      </Button>
    </div>
  )
}
