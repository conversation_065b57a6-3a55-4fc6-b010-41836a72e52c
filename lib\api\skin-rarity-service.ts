// Skin Rarity Service - Fetches and caches skin rarity data from Community Dragon API

import { CommunityDragonSkin, CommunityDragonSkinsResponse } from '@/lib/types/league-api';

/**
 * Service for fetching and managing skin rarity data from Community Dragon API
 */
export class SkinRarityService {
  private static instance: SkinRarityService
  private cache: Map<string, { data: CommunityDragonSkinsResponse; timestamp: number }> = new Map()
  private readonly CACHE_TTL = 24 * 60 * 60 * 1000 // 24 hours
  private readonly API_URL = 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/skins.json'

  private constructor() {}

  public static getInstance(): SkinRarityService {
    if (!SkinRarityService.instance) {
      SkinRarityService.instance = new SkinRarityService()
    }
    return SkinRarityService.instance
  }

  /**
   * Fetch all skin rarity data from Community Dragon API
   */
  public async getAllSkinsRarityData(): Promise<CommunityDragonSkinsResponse> {
    const cacheKey = 'all_skins_rarity'
    const cached = this.cache.get(cacheKey)

    // Check if cached data is still valid
    if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
      
      return cached.data
    }

    try {
      
      const response = await fetch(this.API_URL)
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: Failed to fetch skin rarity data`)
      }

      const data: CommunityDragonSkinsResponse = await response.json()
      
      // Cache the data
      this.cache.set(cacheKey, {
        data,
        timestamp: Date.now()
      })

      
      return data

    } catch (error) {
      console.error('❌ Failed to fetch skin rarity data:', error)
      
      // Return cached data if available, even if expired
      if (cached) {
        
        return cached.data
      }
      
      throw error
    }
  }

  /**
   * Get skin rarity data for a specific skin ID
   */
  public async getSkinRarityData(skinId: number): Promise<CommunityDragonSkin | null> {
    try {
      const allSkins = await this.getAllSkinsRarityData()
      const skin = allSkins[skinId.toString()]

      if (skin) {
        return skin
      }

      // Check if this might be a tiered skin (stage 2) - look for parent skin
      const parentSkinId = this.findParentSkinId(skinId, allSkins)
      if (parentSkinId) {
        const parentSkin = allSkins[parentSkinId.toString()]
        if (parentSkin && parentSkin.questSkinInfo) {
          // Return parent skin data for tiered skins to inherit rarity
          
          return parentSkin
        }
      }

      return null
    } catch (error) {
      console.error(`❌ Failed to get rarity data for skin ID ${skinId}:`, error)
      return null
    }
  }

  /**
   * Find parent skin ID for tiered skins (stage 2 skins)
   */
  private findParentSkinId(skinId: number, allSkins: CommunityDragonSkinsResponse): number | null {
    // Look for a skin with questSkinInfo that contains this skinId in its tiers
    for (const [id, skin] of Object.entries(allSkins)) {
      if (skin.questSkinInfo && skin.questSkinInfo.tiers) {
        const foundTier = skin.questSkinInfo.tiers.find(tier => tier.id === skinId)
        if (foundTier && foundTier.stage > 1) {
          // This is a stage 2+ skin, return the parent skin ID
          return parseInt(id)
        }
      }
    }
    return null
  }

  /**
   * Get skin rarity data for multiple skin IDs
   */
  public async getMultipleSkinRarityData(skinIds: number[]): Promise<Record<number, CommunityDragonSkin | null>> {
    try {
      const allSkins = await this.getAllSkinsRarityData()
      const result: Record<number, CommunityDragonSkin | null> = {}
      
      for (const skinId of skinIds) {
        result[skinId] = allSkins[skinId.toString()] || null
      }
      
      return result
    } catch (error) {
      console.error('❌ Failed to get multiple skin rarity data:', error)
      return skinIds.reduce((acc, id) => ({ ...acc, [id]: null }), {})
    }
  }

  /**
   * Normalize Community Dragon rarity to display rarity
   */
  public static normalizeRarity(cdRarity: string): string {
    const rarityMap: Record<string, string> = {
      'kNoRarity': 'Regular',
      'kRare': 'Regular', 
      'kEpic': 'Epic',
      'kLegendary': 'Legendary',
      'kUltimate': 'Ultimate',
      'kMythic': 'Mythic',
      'kTranscendent': 'Transcendent',
      'kExalted': 'Exalted'
    }
    
    return rarityMap[cdRarity] || 'Regular'
  }

  /**
   * Get rarity icon URL based on rarity tier
   */
  public static getRarityIconUrl(rarity: string): string | null {
    const iconMap: Record<string, string> = {
      'Regular': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-parties/global/default/summoner-icon-rare.png',
      'Epic': 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/cn-gem-3.png',
      'Legendary': 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/legendary.png',
      'Ultimate': 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/ultimate.png',
      'Mythic': 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/cn-gem-8.png',
      'Transcendent': 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/cn-gem-11.png',
      'Exalted': 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/exalted.png'
    }

    return iconMap[rarity] || null
  }

  /**
   * Get legacy icon URL
   */
  public static getLegacyIconUrl(): string {
    return 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/summoner-icon/icon-legacy.png'
  }

  /**
   * Calculate skin ID from champion key and skin number
   */
  public static calculateSkinId(championKey: string, skinNum: number): number {
    return parseInt(championKey) * 1000 + skinNum
  }

  /**
   * Clear cache (useful for testing or manual refresh)
   */
  public clearCache(): void {
    this.cache.clear()
    
  }

  /**
   * Get cache statistics
   */
  public getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    }
  }
}

// Export singleton instance
export const skinRarityService = SkinRarityService.getInstance()
