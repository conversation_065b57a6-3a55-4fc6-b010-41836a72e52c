import { ProcessedChromaData } from '@/lib/types/league-api'
import { getColorDisplayName } from '@/lib/utils/chroma-utils'

/**
 * Create a URL-friendly slug from chroma data
 * Standard format: skin-name-chroma-color (e.g., withered-rose-syndra-rose-quartz)
 * Special format for Victorious chromas: skin-name-rank-queue (e.g., victorious-blitzcrank-challenger-flex)
 * Uses the chroma name from store API when available
 */
export function createChromaURL(chroma: ProcessedChromaData): string {
  // Special handling for Victorious Blitzcrank and Victorious Sejuani chromas
  // These have duplicate colors but different queue requirements
  if ((chroma.skinName.toLowerCase().includes('victorious blitzcrank') ||
       chroma.skinName.toLowerCase().includes('victorious sejuani')) &&
      chroma.description) {

    const skinName = chroma.skinName.toLowerCase().trim()
    const skinSlug = skinName
      .replace(/[^a-z0-9\s-]/g, '') // Remove special characters except spaces and hyphens
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
      .replace(/^-|-$/g, '') // Remove leading/trailing hyphens
      .trim()

    // Extract rank and queue type from description
    const rankAndQueue = extractRankAndQueueFromDescription(chroma.description)
    if (rankAndQueue) {
      return `${skinSlug}-${rankAndQueue.rank}-${rankAndQueue.queue}`
    }
  }

  // Extract chroma color from the chroma name if it contains parentheses
  const chromaNameMatch = chroma.name.match(/\(([^)]+)\)/)

  if (chromaNameMatch) {
    // Use the name inside parentheses as the chroma color
    const chromaColor = chromaNameMatch[1].toLowerCase().trim()
    const skinName = chroma.skinName.toLowerCase().trim()

    // Create the slug: skin-name-chroma-color
    const skinSlug = skinName
      .replace(/[^a-z0-9\s-]/g, '') // Remove special characters except spaces and hyphens
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
      .replace(/^-|-$/g, '') // Remove leading/trailing hyphens
      .trim()

    const chromaSlug = chromaColor
      .replace(/[^a-z0-9\s-]/g, '') // Remove special characters except spaces and hyphens
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
      .replace(/^-|-$/g, '') // Remove leading/trailing hyphens
      .trim()

    return `${skinSlug}-${chromaSlug}`
  }

  // Fallback to old format if no parentheses found
  const cleanSkin = chroma.skinName.toLowerCase().trim()

  // Check if chroma has no price (Legacy, N/A, etc.) and has multiple colors
  const hasNoPrice = !chroma.price || chroma.price === 'Legacy' || chroma.price === 'N/A' || chroma.availability === 'Legacy'
  const hasMultipleColors = chroma.colors && chroma.colors.length > 1

  let chromaColorName: string

  if (hasNoPrice && hasMultipleColors) {
    // Get color names for the first two colors and remove duplicates
    const colorNames = chroma.colors.slice(0, 2).map(color => getColorDisplayName(color).toLowerCase().trim())
    const uniqueColorNames = [...new Set(colorNames)] // Remove duplicates
    chromaColorName = uniqueColorNames.join('-')
  } else {
    // Use single color as before
    chromaColorName = getColorDisplayName(chroma.color).toLowerCase().trim()
  }

  const skinSlug = cleanSkin
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters except spaces and hyphens
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .replace(/^-|-$/g, '') // Remove leading/trailing hyphens
    .trim()

  const chromaSlug = chromaColorName
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters except spaces and hyphens
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .replace(/^-|-$/g, '') // Remove leading/trailing hyphens
    .trim()

  return `${skinSlug}-${chromaSlug}`
}

/**
 * Extract rank and queue type from chroma description for Victorious chromas
 * Returns object with rank and queue type, or null if not found
 * Expected format: "This Chroma was awarded to players who reached X rank in Y ranked queue"
 */
function extractRankAndQueueFromDescription(description: string): { rank: string; queue: string } | null {
  const lowerDescription = description.toLowerCase()

  // Pattern to match: "reached X rank in Y ranked queue"
  const flexMatch = lowerDescription.match(/reached\s+(\w+)\s+rank\s+in\s+flex\s+ranked\s+queue/)
  if (flexMatch) {
    return {
      rank: flexMatch[1].toLowerCase(),
      queue: 'flex'
    }
  }

  const soloMatch = lowerDescription.match(/reached\s+(\w+)\s+rank\s+in\s+solo\/duo\s+ranked\s+queue/)
  if (soloMatch) {
    return {
      rank: soloMatch[1].toLowerCase(),
      queue: 'solo'
    }
  }

  return null
}

/**
 * Parse chroma slug back to components
 * Standard format: skin-name-chroma-color (e.g., withered-rose-syndra-rose-quartz)
 * Special format for Victorious chromas: skin-name-rank-queue (e.g., victorious-blitzcrank-challenger-flex)
 *
 * This function handles multi-word chroma colors by trying different combinations
 * to find the best match between skin name and chroma color.
 */
export function parseChromaURL(slug: string): { chromaColorName: string; skinName: string } | null {
  const parts = slug.split('-')
  if (parts.length < 2) return null

  // Special handling for Victorious chromas with rank-queue-specific URLs
  // Format: victorious-blitzcrank-challenger-flex or victorious-sejuani-diamond-solo
  if ((slug.includes('victorious-blitzcrank') || slug.includes('victorious-sejuani')) &&
      (slug.endsWith('-flex') || slug.endsWith('-solo'))) {

    const queueType = slug.endsWith('-flex') ? 'flex' : 'solo'
    const withoutQueue = slug.slice(0, -(queueType.length + 1)) // Remove queue type and hyphen

    // Extract rank from the end of the remaining slug
    const parts = withoutQueue.split('-')
    if (parts.length >= 3) { // victorious-champion-rank
      const rank = parts[parts.length - 1]
      const skinName = parts.slice(0, -1).join('-') // Everything except the rank

      // For Victorious chromas, we'll use rank-queue as the "chroma color" for identification
      return {
        chromaColorName: `${rank}-${queueType}`,
        skinName
      }
    }

    // Fallback to old format if parsing fails
    const skinName = withoutQueue
    return {
      chromaColorName: queueType,
      skinName
    }
  }

  // Simple parsing - just split on the last hyphen
  // Since we use exact URL matching in the detail page, this is only for basic needs
  const chromaColorName = parts[parts.length - 1]
  const skinName = parts.slice(0, -1).join('-')

  return {
    chromaColorName,
    skinName
  }
}
