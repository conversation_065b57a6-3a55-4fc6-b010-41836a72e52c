"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import Link from "next/link"
import { LucideIcon } from "lucide-react"

interface CardData {
  href: string
  icon: LucideIcon
  title: string
  description: string
  borderColor: string
  hoverColor: string
  iconColor: string
  titleHoverColor: string
}

interface MobileCardCarouselProps {
  cards: CardData[]
  autoRotateInterval?: number
}

export default function MobileCardCarousel({
  cards,
  autoRotateInterval = 2000
}: MobileCardCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isPressed, setIsPressed] = useState(false)

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % cards.length)
    }, autoRotateInterval)

    return () => clearInterval(interval)
  }, [cards.length, autoRotateInterval])

  const currentCard = cards[currentIndex]
  const IconComponent = currentCard.icon

  // Create dynamic classes based on current card
  const getCardClasses = () => {
    const baseClasses = "bg-gray-900/40 transition-all duration-300 cursor-pointer"

    // Map border colors
    const borderColorMap: Record<string, string> = {
      "orange-700/20": "border-orange-700/20",
      "green-700/20": "border-green-700/20",
      "blue-700/20": "border-blue-700/20",
      "amber-700/20": "border-amber-700/20",
      "yellow-700/20": "border-yellow-700/20"
    }

    // Map hover border colors for pressed state
    const hoverBorderColorMap: Record<string, string> = {
      "orange-400/50": "border-orange-400/50",
      "green-400/50": "border-green-400/50",
      "blue-400/50": "border-blue-400/50",
      "amber-400/50": "border-amber-400/50",
      "yellow-400/50": "border-yellow-400/50"
    }

    const borderClass = borderColorMap[currentCard.borderColor] || "border-gray-700/20"
    const hoverBorderClass = hoverBorderColorMap[currentCard.hoverColor] || "border-gray-400/50"
    const pressedClasses = isPressed ? `${hoverBorderClass} -translate-y-2` : ""

    return `${baseClasses} ${borderClass} ${pressedClasses}`
  }

  const getIconClasses = () => {
    const baseClasses = "h-12 w-12 mx-auto mb-4 transition-transform duration-300"

    const iconColorMap: Record<string, string> = {
      "orange-400": "text-orange-400",
      "green-400": "text-green-400",
      "blue-400": "text-blue-400",
      "amber-400": "text-amber-400",
      "yellow-400": "text-yellow-400"
    }

    const iconClass = iconColorMap[currentCard.iconColor] || "text-gray-400"
    const pressedScale = isPressed ? "scale-110" : ""

    return `${baseClasses} ${iconClass} ${pressedScale}`
  }

  const getTitleClasses = () => {
    const baseClasses = "text-xl font-semibold mb-2 transition-colors duration-300"

    // Map title hover colors for pressed state
    const titleHoverColorMap: Record<string, string> = {
      "orange-300": "text-orange-300",
      "green-300": "text-green-300",
      "blue-300": "text-blue-300",
      "amber-300": "text-amber-300",
      "yellow-300": "text-yellow-300"
    }

    const titleColor = isPressed
      ? titleHoverColorMap[currentCard.titleHoverColor] || "text-gray-300"
      : "text-white"

    return `${baseClasses} ${titleColor}`
  }

  const handleTouchStart = () => {
    setIsPressed(true)
  }

  const handleTouchEnd = () => {
    setIsPressed(false)
  }

  return (
    <div className="md:hidden">
      <div className="flex justify-center">
        <Link href={currentCard.href} className="block w-full max-w-sm">
          <Card
            className={getCardClasses()}
            onTouchStart={handleTouchStart}
            onTouchEnd={handleTouchEnd}
            onMouseDown={handleTouchStart}
            onMouseUp={handleTouchEnd}
            onMouseLeave={handleTouchEnd}
          >
            <CardContent className="p-6 text-center">
              <IconComponent className={getIconClasses()} />
              <h3 className={getTitleClasses()}>
                {currentCard.title}
              </h3>
              <p className="text-gray-400">{currentCard.description}</p>
            </CardContent>
          </Card>
        </Link>
      </div>

      {/* Dots indicator */}
      <div className="flex justify-center mt-4 space-x-2">
        {cards.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentIndex(index)}
            className={`w-2 h-2 rounded-full transition-colors duration-300 ${
              index === currentIndex ? 'bg-orange-400' : 'bg-gray-600'
            }`}
          />
        ))}
      </div>
    </div>
  )
}
