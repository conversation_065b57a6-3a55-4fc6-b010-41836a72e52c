@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 220 39% 11%;
    --foreground: 210 40% 98%;
    --card: 220 39% 11%;
    --card-foreground: 210 40% 98%;
    --popover: 220 39% 11%;
    --popover-foreground: 210 40% 98%;
    --primary: 199 89% 48%;
    --primary-foreground: 210 40% 98%;
    --secondary: 220 39% 15%;
    --secondary-foreground: 210 40% 98%;
    --muted: 220 39% 15%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 220 39% 15%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 220 39% 15%;
    --input: 220 39% 15%;
    --ring: 199 89% 48%;
    --radius: 0.75rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  /* Remove default focus outline */
  *:focus {
    outline: none !important;
  }

  /* Remove focus outline from buttons and inputs */
  button:focus,
  input:focus,
  select:focus,
  textarea:focus {
    outline: none !important;
    box-shadow: none !important;
  }
  
  html, body {
    @apply text-foreground;
    height: 100%;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    /* scrollbar-gutter: stable; */ 
    background: linear-gradient(135deg, hsl(220 50% 8%) 0%, hsl(220 39% 11%) 50%, hsl(215 30% 6%) 100%) !important;
    background-color: hsl(220 39% 11%) !important;
    background-repeat: no-repeat !important;
    background-attachment: fixed !important;
    background-size: 100% 100% !important;
  }
  
  /* Ensure the root container can show backgrounds */
  #__next,
  [data-nextjs-router] {
    background: transparent !important;
    min-height: 100vh;
  }
}

/* Custom scrollbar - Desktop only */
@media (min-width: 768px) {
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: rgb(30 41 59);
  }

  ::-webkit-scrollbar-thumb {
    background: rgb(51 65 85);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: rgb(71 85 105);
  }
}

/* Hide scrollbar on mobile */
@media (max-width: 767px) {
  ::-webkit-scrollbar {
    display: none;
  }

  /* For Firefox */
  * {
    scrollbar-width: none;
  }
}

/* Scrollbar hide utility class */
.scrollbar-hide {
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Safari and Chrome */
}

/* Hide scrollbar on PC only (768px and above) */
@media (min-width: 768px) {
  .scrollbar-hide-pc {
    -ms-overflow-style: none;  /* Internet Explorer 10+ */
    scrollbar-width: none;  /* Firefox */
  }

  .scrollbar-hide-pc::-webkit-scrollbar {
    display: none;  /* Safari and Chrome */
  }
}

/* Mobile horizontal scroll improvements */
@media (min-width: 475px) and (max-width: 767px) {
  .mobile-horizontal-scroll {
    scroll-behavior: auto;
    -webkit-overflow-scrolling: touch;
    scroll-snap-type: none;
    position: relative;
    overflow-x: auto;
    overflow-y: hidden;
  }

  /* Remove scroll snap for smoother dragging */
  .mobile-horizontal-scroll > div > * {
    scroll-snap-align: none;
  }

  /* Fade-out edges to indicate scrollability - improved */
  .mobile-horizontal-scroll::before,
  .mobile-horizontal-scroll::after {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    width: 20px;
    z-index: 10;
    pointer-events: none;
  }

  .mobile-horizontal-scroll::before {
    left: 0;
    background: linear-gradient(to right, hsl(220, 39%, 11%) 0%, hsla(220, 39%, 11%, 0.8) 50%, transparent 100%);
  }

  .mobile-horizontal-scroll::after {
    right: 0;
    background: linear-gradient(to left, hsl(220, 39%, 11%) 0%, hsla(220, 39%, 11%, 0.8) 50%, transparent 100%);
  }

  /* Ensure smooth container behavior */
  .mobile-nav-container {
    display: flex !important;
    flex-direction: row !important;
    align-items: stretch !important;
    width: max-content !important;
  }
}

/* Extra small screens (below 475px) - Complete redesign */
@media (max-width: 474px) {
  /* Remove fade edges for vertical layout */
  .mobile-horizontal-scroll::before,
  .mobile-horizontal-scroll::after {
    display: none;
  }

  /* Disable horizontal scrolling for very small screens */
  .mobile-horizontal-scroll {
    overflow-x: visible !important;
    scroll-behavior: auto;
    -webkit-overflow-scrolling: auto;
    scroll-snap-type: none;
  }

  /* Ensure container doesn't overflow on very small screens */
  .container {
    padding-left: 0.75rem !important;
    padding-right: 0.75rem !important;
  }

  /* Mobile navigation container - switch to vertical layout with left alignment */
  .mobile-nav-container {
    display: block !important;
    width: 100% !important;
    padding: 0 !important;
    margin: 0 !important;
  }

  /* Mobile navigation items - consistent width and spacing */
  .mobile-nav-item {
    display: block !important;
    width: 100% !important;
    max-width: 100% !important;
    margin-bottom: 0.5rem !important;
    box-sizing: border-box !important;
  }

  .mobile-nav-item:last-child {
    margin-bottom: 0 !important;
  }

  /* Mobile navigation cards - force full width and perfect alignment */
  .mobile-nav-card {
    width: 100% !important;
    min-width: 100% !important;
    max-width: 100% !important;
    display: block !important;
    box-sizing: border-box !important;
    margin: 0 !important;
    position: relative !important;
  }

  /* Mobile navigation content - horizontal layout with consistent spacing */
  .mobile-nav-content {
    display: flex !important;
    align-items: center !important;
    text-align: left !important;
    padding: 1rem !important;
    gap: 0.75rem !important;
    width: 100% !important;
    box-sizing: border-box !important;
  }

  /* Mobile navigation icons - fixed width for perfect alignment */
  .mobile-nav-icon {
    margin: 0 !important;
    flex-shrink: 0 !important;
    width: 2rem !important;
    height: 2rem !important;
    min-width: 2rem !important;
    max-width: 2rem !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    position: relative !important;
  }

  /* Ensure all SVG icons are perfectly centered and same size */
  .mobile-nav-icon svg {
    width: 1.5rem !important;
    height: 1.5rem !important;
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
  }

  /* Mobile navigation text container */
  .mobile-nav-text {
    flex: 1 !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;
    min-width: 0 !important;
  }

  /* Mobile navigation titles */
  .mobile-nav-title {
    font-size: 1rem !important;
    margin-bottom: 0 !important;
  }

  /* Mobile navigation descriptions - hide on very small screens */
  .mobile-nav-desc {
    display: none !important;
  }

  /* Champion card grids - perfect alignment for very small screens */
  /* Additional padding for very small screens */
  .container {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }

  /* Ensure champion cards are perfectly centered */
  .champions-featured-grid,
  .champions-roles-grid {
    padding-left: 0.5rem !important;
    padding-right: 0.5rem !important;
  }
}


/* Glow effects */
.glow-cyan {
  box-shadow: 0 0 20px rgba(34, 211, 238, 0.3);
}

.glow-blue {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

/* REMOVED THE PROBLEMATIC LINE - DO NOT ADD BACKGROUND TO min-h-screen */
/* .min-h-screen {
  background: transparent;
} */

/* Responsive utilities */
@layer utilities {
  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  /* Mobile-first responsive text sizes */
  .text-responsive-xs {
    @apply text-xs sm:text-sm;
  }

  .text-responsive-sm {
    @apply text-sm sm:text-base;
  }

  .text-responsive-base {
    @apply text-base sm:text-lg;
  }

  .text-responsive-lg {
    @apply text-lg sm:text-xl;
  }

  .text-responsive-xl {
    @apply text-xl sm:text-2xl;
  }

  .text-responsive-2xl {
    @apply text-2xl sm:text-3xl;
  }

  .text-responsive-3xl {
    @apply text-3xl sm:text-4xl lg:text-5xl;
  }

  /* Responsive spacing */
  .p-responsive {
    @apply p-3 sm:p-4 lg:p-6;
  }

  .px-responsive {
    @apply px-4 sm:px-6 lg:px-8;
  }

  .py-responsive {
    @apply py-4 sm:py-6 lg:py-8;
  }

  /* Mobile-friendly touch targets */
  .touch-target {
    @apply min-h-[44px] min-w-[44px];
  }

  /* Mobile container centering */
  .mobile-container {
    @apply w-full max-w-full mx-auto;
  }

  /* Ensure proper mobile grid alignment */
  .mobile-grid-center {
    @apply justify-items-start items-start;
  }

  /* Mobile-specific container fixes */


}

/* Champion background specific styles */
.champion-background {
  position: relative;
  min-height: 100vh;
  background: transparent;
}

.champion-background-layer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -50;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  transform: scale(1.1);
  filter: blur(10px) brightness(0.25) saturate(1.2);
}

/* Prevent layout shifts from dropdowns */
[data-radix-popper-content-wrapper] {
  position: fixed !important;
  z-index: 9999 !important;
  contain: layout style paint !important;
}

/* Ensure Sheet components appear above background-boosted headers */
[data-radix-dialog-overlay] {
  z-index: 65 !important;
}

[data-radix-dialog-content] {
  z-index: 70 !important;
}

/* Ensure proper z-index hierarchy for header and overlays */
/* Desktop: Header should be above most content but below overlays */
@media (min-width: 768px) {
  header {
    z-index: 60 !important;
  }
}

/* Mobile-only: Ensure Sheet components (sidebar, global search) appear above header */
@media (max-width: 767px) {
  /* Lower header z-index on mobile to allow overlays to appear above */
  header {
    z-index: 40 !important;
  }

  [data-radix-dialog-overlay][data-state="open"] {
    z-index: 75 !important;
  }

  [data-radix-dialog-content][data-state="open"] {
    z-index: 80 !important;
  }
}

/* Ensure dropdown content doesn't affect page layout */
[data-radix-dropdown-menu-content] {
  position: fixed !important;
  z-index: 9999 !important;
  contain: layout style paint !important;
  will-change: transform, opacity !important;
}

/* Fix dropdown positioning to stay within viewport */
[data-radix-select-content] {
  position: fixed !important;
  z-index: 9999 !important;
  max-width: calc(100vw - 2rem) !important;
  max-height: calc(100vh - 2rem) !important;
  overflow-y: auto !important;
}

/* Ensure select content stays within screen bounds */
[data-radix-select-viewport] {
  max-width: calc(100vw - 2rem) !important;
  max-height: calc(100vh - 4rem) !important;
  overflow-y: auto !important;
}

/* Mobile dropdown fixes */
@media (max-width: 768px) {
  [data-radix-select-content] {
    max-width: calc(100vw - 1rem) !important;
    margin: 0.5rem !important;
  }

  [data-radix-select-viewport] {
    max-width: calc(100vw - 1rem) !important;
  }
}

/* Fix popover width to match trigger button */
[data-radix-popover-content] {
  width: var(--radix-popover-trigger-width) !important;
  min-width: var(--radix-popover-trigger-width) !important;
  max-width: var(--radix-popover-trigger-width) !important;
}

/* Ensure popover content doesn't exceed viewport on mobile */
@media (max-width: 768px) {
  [data-radix-popover-content] {
    max-width: calc(100vw - 2rem) !important;
  }
}

/* Prevent layout shifts in header buttons */
.responsive-header-buttons {
  contain: layout style !important;
  width: fit-content !important;
  min-width: max-content !important;
}

/* Prevent logo movement during mobile detection */
.mobile-logo-container {
  contain: layout style !important;
}

/* Mobile logo positioning using CSS media queries to prevent layout shifts */
@media (max-width: 767px) {
  .mobile-logo-container {
    position: absolute !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
  }
}

/* Desktop logo positioning - static positioning */
@media (min-width: 768px) {
  .mobile-logo-container {
    position: relative !important;
  }
}

/* Ensure stable header layout */
header .flex.items-center.justify-between {
  contain: layout !important;
}

/* Ensure stable layout for champion cards and other elements */
.champion-card,
.stats-card,
.win-rate-graph {
  contain: layout style !important;
}

/* Matchup grid column separators for PC */
@media (min-width: 640px) {
  .matchup-grid {
    position: relative;
  }

  /* Add vertical separators between columns */
  .matchup-grid::before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 50%;
    width: 1px;
    background: linear-gradient(to bottom, transparent, rgba(156, 163, 175, 0.3), transparent);
    transform: translateX(-50%);
    z-index: 1;
    pointer-events: none;
  }
}

@media (min-width: 1024px) {
  /* For 3-column layout, add two separators */
  .matchup-grid::before {
    left: 33.333%;
  }

  .matchup-grid::after {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 66.666%;
    width: 1px;
    background: linear-gradient(to bottom, transparent, rgba(156, 163, 175, 0.3), transparent);
    transform: translateX(-50%);
    z-index: 1;
    pointer-events: none;
  }
}

.champion-background-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -49;
  background: linear-gradient(to bottom, rgba(0,0,0,0.4), rgba(0,0,0,0.2), rgba(0,0,0,0.6));
}

.champion-background-overlay-2 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -48;
  background: linear-gradient(to right, rgba(0,0,0,0.6), rgba(0,0,0,0.2), rgba(0,0,0,0.6));
}

.champion-background-overlay-3 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -47;
  background: linear-gradient(to top, rgba(17, 24, 39, 0.9), transparent, rgba(17, 24, 39, 0.5));
}

/* Free Rotation Specific Styles */
.free-rotation-info-grid {
  display: grid !important;
  grid-template-columns: 1fr !important;
  gap: 1rem !important;
  margin-bottom: 2rem !important;
}

/* Free Rotation Champion Grid - Same responsive behavior as general champion grid */
.free-rotation-champion-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr)) !important;
  gap: clamp(0.5rem, 2vw, 1.5rem) !important;
  width: 100% !important;
  min-width: 0 !important;
  overflow: visible !important;
  margin-bottom: 1.5rem !important;
}

/* Mobile fallback for free rotation champion grid */
@media (max-width: 639px) {
  .free-rotation-champion-grid {
    grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
    gap: 0.5rem !important;
  }
}

/* Free rotation info grid - keep existing behavior */
@media (min-width: 640px) {
  .free-rotation-info-grid {
    grid-template-columns: repeat(3, 1fr) !important;
    gap: 1.5rem !important;
  }
}

/* General Champion Grid Styles - Responsive auto-fill grid */
.champion-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr)) !important;
  gap: clamp(0.5rem, 2vw, 1.5rem) !important;
  width: 100% !important;
  min-width: 0 !important;
  overflow: visible !important;
  margin-bottom: 1.5rem !important;
}

/* Mobile fallback for champion grid */
@media (max-width: 639px) {
  .champion-grid {
    grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
    gap: 0.5rem !important;
  }
}

/* Skins Grid Styles - Responsive auto-fill grid */
.skins-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr)) !important;
  gap: clamp(0.5rem, 2vw, 1.5rem) !important;
  width: 100% !important;
  min-width: 0 !important;
  overflow: visible !important;
}

/* Mobile fallback for skins grid */
@media (max-width: 639px) {
  .skins-grid {
    grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
    gap: 0.5rem !important;
  }
}

/* Remove top padding for specific pages on all devices */
.no-top-padding {
  padding-top: 0rem !important;
}

/* Legacy class for backward compatibility */
.mobile-no-top-padding {
  padding-top: 0rem !important;
}
