"use client"

import { Card, CardContent } from "@/components/ui/card"

interface ItemSkeletonProps {
  count?: number
}

export function ItemSkeleton({ count = 1 }: ItemSkeletonProps) {
  return (
    <>
      {Array.from({ length: count }).map((_, index) => (
        <Card
          key={index}
          className="bg-gray-900/40 border-purple-700/20 animate-pulse"
        >
          <CardContent className="p-3">
            <div className="flex items-center gap-3">
              {/* Item Icon Skeleton */}
              <div className="flex-shrink-0">
                <div className="w-12 h-12 bg-gray-700/50 rounded-lg"></div>
              </div>

              {/* Item Details Skeleton */}
              <div className="flex-1 min-w-0">
                {/* Item Name Skeleton */}
                <div className="h-4 bg-gray-700/50 rounded mb-2 w-3/4"></div>

                {/* Tags Skeleton */}
                <div className="flex gap-1">
                  <div className="h-5 bg-gray-700/50 rounded w-16"></div>
                  <div className="h-5 bg-gray-700/50 rounded w-12"></div>
                  <div className="h-5 bg-gray-700/50 rounded w-8"></div>
                </div>
              </div>

              {/* Price Skeleton */}
              <div className="flex-shrink-0">
                <div className="flex items-center gap-1">
                  <div className="h-4 bg-gray-700/50 rounded w-8"></div>
                  <div className="w-3 h-3 bg-gray-700/50 rounded"></div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </>
  )
}

export function ItemGridSkeleton() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
      <ItemSkeleton count={30} />
    </div>
  )
}
