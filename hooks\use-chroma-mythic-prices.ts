import { useState, useEffect } from 'react'
import { mythicCostService, MythicCostData } from '@/lib/services/mythic-cost-service'
import { ProcessedChromaData } from '@/lib/types/league-api'

interface UseChromaMythicPricesReturn {
  mythicPrices: Map<string, MythicCostData | null>
  loading: boolean
  error: string | null
}

export function useChromaMythicPrices(chromas: ProcessedChromaData[]): UseChromaMythicPricesReturn {
  const [mythicPrices, setMythicPrices] = useState<Map<string, MythicCostData | null>>(new Map())
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchMythicPrices = async () => {
    try {
      setLoading(true)
      setError(null)

      // Filter and prepare chromas data for mythic pricing
      const chromasWithContentId = chromas
        .filter(chroma => chroma.contentId && chroma.availability !== 'Available')
        .map(chroma => ({
          contentId: chroma.contentId,
          availability: chroma.availability
        }))

      if (chromasWithContentId.length === 0) {
        setMythicPrices(new Map())
        return
      }

      const prices = await mythicCostService.getBulkChromaMythicPrices(chromasWithContentId)
      setMythicPrices(prices)

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch chroma mythic prices'
      setError(errorMessage)
      console.error('❌ Failed to fetch chroma mythic prices:', errorMessage)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (chromas.length > 0) {
      fetchMythicPrices()
    }
  }, [chromas])

  return {
    mythicPrices,
    loading,
    error
  }
}
