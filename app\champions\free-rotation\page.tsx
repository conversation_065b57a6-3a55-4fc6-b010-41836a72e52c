import FreeRotationClient from "./client";
import { generateSEOMetadata } from "@/components/SEO";
import type { Metadata } from 'next';

export async function generateMetadata(): Promise<Metadata> {
  return generateSEOMetadata({
    title: 'League of Legends Free Champion Rotation',
    description: 'Play 20 free LoL champions this week! View the current free champion rotation, filter by role and class, and try new champions before purchasing them.',
    keywords: [
      'League of Legends free rotation',
      'LoL free champions',
      'free champion rotation',
      'free to play champions',
      'weekly rotation',
      'try champions free',
      'champion rotation schedule'
    ],
    url: '/champions/free-rotation',
    canonical: 'https://loldb.info/champions/free-rotation',
    type: 'website',
    section: 'Champions'
  });
}

export default function FreeRotationPage() {
  return <FreeRotationClient />;
}