"use client"

import * as React from "react"
import { Check, ChevronDown, X, Search } from "lucide-react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Input } from "@/components/ui/input"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Badge } from "@/components/ui/badge"

interface MultiSelectSkinLinesProps {
  selectedSkinLines: string[]
  onSelectionChange: (skinLines: string[]) => void
  availableSkinLines: string[]
  placeholder?: string
  className?: string
}

export function MultiSelectSkinLines({
  selectedSkinLines,
  onSelectionChange,
  availableSkinLines,
  placeholder = "Select skin lines...",
  className
}: MultiSelectSkinLinesProps) {
  const [open, setOpen] = React.useState(false)
  const [searchTerm, setSearchTerm] = React.useState("")

  const filteredSkinLines = React.useMemo(() => {
    if (!searchTerm.trim()) return availableSkinLines
    
    return availableSkinLines.filter(skinLine =>
      skinLine.toLowerCase().includes(searchTerm.toLowerCase())
    )
  }, [availableSkinLines, searchTerm])

  const handleSkinLineToggle = (skinLine: string) => {
    const newSelection = selectedSkinLines.includes(skinLine)
      ? selectedSkinLines.filter(sl => sl !== skinLine)
      : [...selectedSkinLines, skinLine]
    
    onSelectionChange(newSelection)
  }

  const handleClearAll = () => {
    onSelectionChange([])
  }

  const renderSelectedBadges = () => {
    if (selectedSkinLines.length === 0) {
      return <span className="text-gray-400">{placeholder}</span>
    }

    return (
      <span className="text-gray-300">
        Skin Lines +{selectedSkinLines.length}
      </span>
    )
  }

  return (
    <div className={className}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between bg-gray-900/40 border-gray-700/20 text-white hover:bg-gray-800/50 h-[40px] items-center"
          >
            <div className="flex-1 text-left">
              {renderSelectedBadges()}
            </div>
            <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0 bg-gray-900 border-gray-700" align="start">
          <div className="p-3">
            <div className="flex items-center justify-between mb-3">
              <span className="text-sm font-medium text-gray-300">Select Skin Lines</span>
              {selectedSkinLines.length > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleClearAll}
                  className="text-xs text-gray-400 hover:text-white h-6"
                >
                  Clear All
                </Button>
              )}
            </div>
            
            {/* Search Input */}
            <div className="relative mb-3">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                type="text"
                placeholder="Search skin lines..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-gray-800/50 border-gray-600/50 text-white placeholder-gray-400 focus:border-green-400/60"
              />
            </div>

            <div className="space-y-1 max-h-60 overflow-y-auto">
              {filteredSkinLines.length === 0 ? (
                <div className="text-center py-4 text-gray-400 text-sm">
                  No skin lines found
                </div>
              ) : (
                filteredSkinLines.map((skinLine) => (
                  <div
                    key={skinLine}
                    className="flex items-center space-x-3 p-1 rounded hover:bg-gray-800/50 cursor-pointer"
                    onClick={() => handleSkinLineToggle(skinLine)}
                  >
                    <Checkbox
                      checked={selectedSkinLines.includes(skinLine)}
                      onCheckedChange={() => handleSkinLineToggle(skinLine)}
                      className="border-gray-600 data-[state=checked]:bg-green-600 data-[state=checked]:border-green-600"
                    />
                    <span className="text-gray-300">{skinLine}</span>
                  </div>
                ))
              )}
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  )
}
