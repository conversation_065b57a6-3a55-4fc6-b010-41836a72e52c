export function transformToCDNUrl(localUrl: string | null | undefined): string | null {
  // Gracefully handle null, undefined, or non-string inputs
  if (!localUrl || typeof localUrl !== 'string') {
    return null
  }
  
  const trimmedUrl = localUrl.trim()
  if (!trimmedUrl) {
    return null
  }

  const baseUrl =
    'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/'
  const marker = '/lol-game-data/assets/'

  const lowerCaseLocalUrl = trimmedUrl.toLowerCase()
  const markerIndex = lowerCaseLocalUrl.indexOf(marker)

  let path: string

  if (markerIndex !== -1) {
    // Extract everything after the marker
    path = trimmedUrl.substring(markerIndex + marker.length)
  } else {
    // If marker is not found, assume the entire string is the relative path.
    path = trimmedUrl
  }

  // Remove query string or hash from the path
  path = path.split(/[?#]/)[0]

  // Normalize path separators to forward slashes
  path = path.replace(/\\/g, '/')

  // Collapse multiple slashes into a single slash
  path = path.replace(/\/{2,}/g, '/')

  // Remove a leading slash to prevent double slashes with the base URL
  if (path.startsWith('/')) {
    path = path.substring(1)
  }
  
  // Lowercase the final path for consistency
  path = path.toLowerCase()

  if (!path) {
    return null
  }

  return baseUrl + path
}
