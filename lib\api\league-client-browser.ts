// Browser-compatible League of Legends API client
// This version works in client-side components

class LeagueApiClientBrowser {
  private baseUrl: string = 'https://ddragon.leagueoflegends.com'
  private currentVersion: string = '15.11.1' // Default fallback
  private communityDragonSkinsCache: { data: any; timestamp: number } | null = null
  private readonly CACHE_TTL = 1000 * 60 * 60 * 24 // 24 hours

  constructor() {
    // Browser-only initialization
  }

  // Get current version (browser-compatible)
  getCurrentVersion(): string {
    return this.currentVersion
  }

  // 🔄 DYNAMIC ASSET URLS (Browser-compatible)
  getChampionSplashUrl(championId: string, skinNum: number = 0): string {
    return `${this.baseUrl}/cdn/img/champion/splash/${championId}_${skinNum}.jpg`
  }

  /**
   * Get champion splash art using Community Dragon skins.json API with uncenteredSplashPath
   * For base skins: championKey + "000" (e.g., 266000 for Aatrox)
   * For other skins: use direct skin ID from DDragon
   */
  async getChampionSplashUrlNew(championId: string, skinNum: number = 0, championKey?: string): Promise<string> {
    try {
      // Load Community Dragon skins data
      const skinsData = await this.loadCommunityDragonSkinsData()

      // If we don't have championKey, fall back to old method
      if (!championKey) {
        console.warn('Champion key not provided for splash art, falling back to DDragon')
        return this.getChampionSplashUrl(championId, skinNum)
      }

      let skinId: string
      if (skinNum === 0) {
        // Base skin: championKey + "000"
        skinId = `${championKey}000`
      } else {
        // Other skins: championKey + skinNum (padded to 3 digits)
        skinId = `${championKey}${skinNum.toString().padStart(3, '0')}`
      }

      // Find the skin in Community Dragon data
      const skinData = skinsData[skinId]
      if (!skinData) {
        console.warn(`Skin data not found for ID ${skinId}, falling back to DDragon`)
        return this.getChampionSplashUrl(championId, skinNum)
      }

      // Try uncenteredSplashPath first, then splashPath as fallback
      const splashPath = skinData.uncenteredSplashPath || skinData.splashPath
      if (!splashPath) {
        console.warn(`No splash path found for skin ${skinId}, falling back to DDragon`)
        return this.getChampionSplashUrl(championId, skinNum)
      }

      // Extract the path after "/ASSETS/" and convert to lowercase
      const assetsIndex = splashPath.indexOf('/ASSETS/')
      if (assetsIndex === -1) {
        console.warn(`Invalid splash path format for skin ${skinId}: ${splashPath}`)
        return this.getChampionSplashUrl(championId, skinNum)
      }

      const pathAfterAssets = splashPath.substring(assetsIndex + 1) // +1 to remove the leading slash
      const lowercasePath = pathAfterAssets.toLowerCase()

      // Construct the final Community Dragon URL
      const baseUrl = 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default'
      const finalUrl = `${baseUrl}/${lowercasePath}`

      return finalUrl
    } catch (error) {
      console.error('Error in getChampionSplashUrlNew:', error)
      return this.getChampionSplashUrl(championId, skinNum)
    }
  }

  getChampionSquareUrl(championId: string): string {
    return `${this.baseUrl}/cdn/${this.currentVersion}/img/champion/${championId}.png`
  }

  getChampionLoadingUrl(championId: string, skinNum: number = 0): string {
    return `${this.baseUrl}/cdn/img/champion/loading/${championId}_${skinNum}.jpg`
  }

  /**
   * Load and cache Community Dragon skins data
   */
  private async loadCommunityDragonSkinsData(): Promise<any> {
    // Check if cache is still valid
    if (this.communityDragonSkinsCache && (Date.now() - this.communityDragonSkinsCache.timestamp) < this.CACHE_TTL) {
      return this.communityDragonSkinsCache.data
    }

    try {
      const response = await fetch('https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/skins.json')
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: Failed to fetch Community Dragon skins data`)
      }

      const skinsData = await response.json()

      // Cache the data
      this.communityDragonSkinsCache = {
        data: skinsData,
        timestamp: Date.now()
      }

      return skinsData
    } catch (error) {
      console.error('Failed to load Community Dragon skins data:', error)
      throw error
    }
  }

  /**
   * Get champion icon using Community Dragon skins.json API
   * For base skins: championKey + "000" (e.g., 266000 for Aatrox)
   * For other skins: use direct skin ID from DDragon
   */
  async getChampionCircleUrl(championId: string, skinNum: number = 0, skinName?: string, allSkins?: any[], championKey?: string): Promise<string> {
    try {
      // Load Community Dragon skins data
      const skinsData = await this.loadCommunityDragonSkinsData()

      // If we don't have championKey, we need to get it from DDragon API
      if (!championKey && allSkins && allSkins.length > 0) {
        // Try to extract champion key from the champion data if available
        // This is a fallback - ideally championKey should be passed
        console.warn('Champion key not provided, falling back to old method')
        return this.getChampionSquareUrl(championId)
      }

      let skinId: string
      if (skinNum === 0) {
        // Base skin: championKey + "000"
        skinId = `${championKey}000`
      } else {
        // Other skins: championKey + skinNum (padded to 3 digits)
        skinId = `${championKey}${skinNum.toString().padStart(3, '0')}`
      }

      // Find the skin in Community Dragon data
      let skinData = skinsData[skinId]

      // If not found, this might be a tiered skin where only the base tier exists
      // For tiered skins, all tiers share the same base entry (e.g., 147001 contains all Seraphine tiers)
      if (!skinData && skinNum > 0) {
        // Search through all skins for this champion to find one with tiers
        // that contains our target tier ID in its tiers array
        const championSkins = Object.entries(skinsData).filter(([id, data]) =>
          id.startsWith(championKey?.toString() || '')
        )

        for (const [baseSkinId, baseSkinData] of championSkins) {
          // Check if this skin has a tiers array (regardless of productType)
          if (baseSkinData.questSkinInfo?.tiers && Array.isArray(baseSkinData.questSkinInfo.tiers)) {

            // Check if this tiered skin contains our target tier
            const targetTier = baseSkinData.questSkinInfo.tiers.find((tier: any) => tier.id.toString() === skinId)
            if (targetTier) {
              skinData = baseSkinData
              break
            }
          }
        }
      }

      if (!skinData) {
        console.warn(`Skin data not found for ID ${skinId}, falling back to square icon`)
        return this.getChampionSquareUrl(championId)
      }



      // Handle tiered skins - check if skin has questSkinInfo.tiers array
      let tilePath: string

      if (skinData.questSkinInfo?.tiers && Array.isArray(skinData.questSkinInfo.tiers) && skinData.questSkinInfo.tiers.length > 0) {
        // For tiered skins, find the matching tier by ID
        const matchingTier = skinData.questSkinInfo.tiers.find((tier: any) => tier.id.toString() === skinId)

        if (matchingTier) {
          tilePath = matchingTier.tilePath
        } else {
          // Fallback to first tier if no exact match
          const firstTier = skinData.questSkinInfo.tiers[0]
          tilePath = firstTier.tilePath
        }

        if (!tilePath) {
          console.warn(`No tilePath found in tier for tiered skin ${skinId}`)
          return this.getChampionSquareUrl(championId)
        }
      } else {
        // For regular skins, use the root level tilePath
        tilePath = skinData.tilePath

        if (!tilePath) {
          console.warn(`No tilePath found for skin ${skinId}, falling back to square icon`)
          return this.getChampionSquareUrl(championId)
        }
      }

      // Extract the path after "/ASSETS/" and convert to lowercase
      const assetsIndex = tilePath.indexOf('/ASSETS/')
      if (assetsIndex === -1) {
        console.warn(`Invalid tilePath format for skin ${skinId}: ${tilePath}`)
        return this.getChampionSquareUrl(championId)
      }

      const pathAfterAssets = tilePath.substring(assetsIndex + 1) // +1 to remove the leading slash
      const lowercasePath = pathAfterAssets.toLowerCase()

      // Construct the final Community Dragon URL
      const baseUrl = 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default'
      const finalUrl = `${baseUrl}/${lowercasePath}`

      return finalUrl
    } catch (error) {
      console.error('Error in getChampionCircleUrl:', error)
      return this.getChampionSquareUrl(championId)
    }
  }

  getChampionPassiveUrl(passiveImage: string): string {
    return `${this.baseUrl}/cdn/${this.currentVersion}/img/passive/${passiveImage}`
  }

  getChampionSpellUrl(spellImage: string): string {
    return `${this.baseUrl}/cdn/${this.currentVersion}/img/spell/${spellImage}`
  }

  getItemImageUrl(itemImage: string): string {
    return `${this.baseUrl}/cdn/${this.currentVersion}/img/item/${itemImage}`
  }

  // Get in-game image URL from Community Dragon
  getChampionInGameImageUrl(championKey: string, skinId: string): string {
    return `https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/champion-chroma-images/${championKey}/${skinId}.png`
  }

  // Get chroma in-game image URL from Community Dragon
  getChromaInGameImageUrl(championKey: string, chromaId: string): string {
    return `https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/champion-chroma-images/${championKey}/${chromaId}.png`
  }

  // Generate skin ID from champion key and skin number
  generateSkinId(championKey: string, skinNum: number): string {
    return `${championKey}${skinNum.toString().padStart(3, '0')}`
  }

  // Fetch champion data to get champion key/ID
  async getChampionKey(championName: string): Promise<string | null> {
    try {
      const response = await fetch(`https://ddragon.leagueoflegends.com/cdn/${this.currentVersion}/data/en_US/champion/${championName}.json`)
      if (!response.ok) return null

      const data = await response.json()
      const championData = data.data[championName]
      return championData?.key || null
    } catch (error) {
      console.error(`Failed to fetch champion key for ${championName}:`, error)
      return null
    }
  }

  // Fetch chroma data from Community Dragon
  async getChromaData(championKey: string): Promise<any> {
    try {
      const response = await fetch(`https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/champions/${championKey}.json`)
      if (!response.ok) return null

      const data = await response.json()
      return data
    } catch (error) {
      console.error(`Failed to fetch chroma data for champion ${championKey}:`, error)
      return null
    }
  }

  // Browser-compatible champion detail fetching (uses API routes)
  async getChampionDetail(championId: string) {
    try {
      const response = await fetch(`/api/champions/${championId}`)
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch champion')
      }
      
      return result.data
    } catch (error) {
      console.error(`❌ Failed to fetch champion ${championId}:`, error)
      throw error
    }
  }

  // Browser-compatible champions list fetching (uses API routes)
  async getChampions() {
    try {
      const response = await fetch('/api/champions')
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch champions')
      }
      
      return result.data
    } catch (error) {
      console.error('❌ Failed to fetch champions:', error)
      throw error
    }
  }

  // Browser-compatible items fetching (uses API routes)
  async getItems() {
    try {
      const response = await fetch('/api/items')
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch items')
      }
      
      return result.data
    } catch (error) {
      console.error('❌ Failed to fetch items:', error)
      throw error
    }
  }

  // Browser-compatible free rotation fetching (uses API routes)
  async getFreeChampionRotation() {
    try {
      const response = await fetch('/api/free-rotation')
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch free rotation')
      }
      
      return result.data
    } catch (error) {
      console.error('❌ Failed to fetch free rotation:', error)
      throw error
    }
  }

  // Browser-compatible game versions fetching (uses API routes)
  async getVersions(): Promise<string[]> {
    try {
      const response = await fetch('/api/game-version')
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch versions')
      }
      
      return result.data.all || []
    } catch (error) {
      console.error('❌ Failed to fetch versions:', error)
      throw error
    }
  }

  // Update current version
  async updateGameVersion(): Promise<string> {
    try {
      const versions = await this.getVersions()
      if (versions.length > 0) {
        this.currentVersion = versions[0]
        
        return this.currentVersion
      }
    } catch (error) {
      console.warn('Failed to update game version, using fallback:', error)
    }
    return this.currentVersion
  }

  /** Check if a skin name indicates a Transcendent skin */
  private isTranscendentSkin(skinName: string): boolean {
    const transcendentKeywords = ['risen legend', 'immortalized legend']
    return transcendentKeywords.some(keyword => skinName.toLowerCase().includes(keyword))
  }

  /** Get the tier number for Transcendent skins */
  private getTranscendentTier(skinName: string): number {
    const name = skinName.toLowerCase()
    if (name.includes('risen legend')) return 1
    if (name.includes('immortalized legend')) return 3
    return 1 // Default to tier 1
  }

  /** Get the correct skin number for Transcendent skins */
  private getTranscendentSkinNumber(skinName: string, originalSkinNum: number): number {
    return originalSkinNum
  }
}

// Export browser-compatible instance
export const leagueApiBrowser = new LeagueApiClientBrowser()
export default LeagueApiClientBrowser
