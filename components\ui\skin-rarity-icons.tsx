"use client"

import Image from "next/image"
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { SkinRarityService } from "@/lib/api/skin-rarity-service"

interface SkinRarityIconsProps {
  rarity: string
  isLegacy: boolean
  isBase: boolean
  className?: string
  size?: number
  showTooltip?: boolean
}

/**
 * Component for displaying skin rarity and legacy icons
 * Handles the logic for base skins, regular skins, and legacy combinations
 */
export function SkinRarityIcons({
  rarity,
  isLegacy,
  isBase,
  className = "",
  size = 16,
  showTooltip = true
}: SkinRarityIconsProps) {
  // Don't show any icons for base skins
  if (isBase) {
    return null
  }

  const rarityIconUrl = SkinRarityService.getRarityIconUrl(rarity)
  const legacyIconUrl = SkinRarityService.getLegacyIconUrl()

  // Get rarity description for tooltip
  const getRarityDescription = (rarity: string): string => {
    const descriptions: Record<string, string> = {
      'Regular': 'Standard skin quality with basic visual changes',
      'Epic': 'High-quality skin with new model, textures, and effects',
      'Legendary': 'Premium skin with new voice lines and animations',
      'Ultimate': 'Evolving skin that changes throughout the game',
      'Mythic': 'Exclusive skin with unique prestige elements',
      'Transcendent': 'Highest tier skin with transformative features',
      'Exalted': 'Ultra-rare skin with exceptional quality'
    }
    return descriptions[rarity] || `${rarity} rarity skin`
  }

  const getLegacyDescription = (): string => {
    return 'Limited-time skin no longer available for purchase'
  }

  // Get rarity-specific color for tooltip (using exact hex colors)
  const getRarityColor = (rarity: string): string => {
    const colorMap: Record<string, string> = {
      'Regular': '#299645',
      'Legacy': '#c1a26a',
      'Epic': '#27d7e5',
      'Legendary': '#e81b23',
      'Mythic': '#b47bdf',
      'Ultimate': '#d7861f',
      'Exalted': 'text-emerald-300', // Keep as is
      'Transcendent': '#cda4de'
    }
    return colorMap[rarity] || '#299645'
  }

  // If no icons to show, return null
  if (!rarityIconUrl && !isLegacy) {
    return null
  }

  const iconElements = []

  // Add rarity icon if available
  if (rarityIconUrl) {
    const rarityIcon = (
      <div
        key="rarity"
        className="flex items-center justify-center"
        style={{ width: size, height: size }}
      >
        <Image
          src={rarityIconUrl}
          alt={rarity}
          width={size}
          height={size}
          className="object-contain"
          style={{ userSelect: 'none', pointerEvents: 'none' }}
          draggable={false}
        />
      </div>
    )

    if (showTooltip) {
      iconElements.push(
        <Tooltip key="rarity-tooltip">
          <TooltipTrigger asChild>
            {rarityIcon}
          </TooltipTrigger>
          <TooltipContent>
            <div className="text-center">
              <div
                className="font-semibold"
                style={{
                  color: getRarityColor(rarity).startsWith('#')
                    ? getRarityColor(rarity)
                    : undefined
                }}
                {...(getRarityColor(rarity).startsWith('text-') && {
                  className: `font-semibold ${getRarityColor(rarity)}`
                })}
              >
                {rarity}
              </div>
              <div className="text-sm text-gray-300 mt-1">
                {getRarityDescription(rarity)}
              </div>
            </div>
          </TooltipContent>
        </Tooltip>
      )
    } else {
      iconElements.push(rarityIcon)
    }
  }

  // Add legacy icon if legacy
  if (isLegacy) {
    const legacyIcon = (
      <div
        key="legacy"
        className="flex items-center justify-center"
        style={{ width: size, height: size }}
      >
        <Image
          src={legacyIconUrl}
          alt="Legacy"
          width={size}
          height={size}
          className="object-contain"
          style={{ userSelect: 'none', pointerEvents: 'none' }}
          draggable={false}
        />
      </div>
    )

    if (showTooltip) {
      iconElements.push(
        <Tooltip key="legacy-tooltip">
          <TooltipTrigger asChild>
            {legacyIcon}
          </TooltipTrigger>
          <TooltipContent>
            <div className="text-center">
              <div
                className="font-semibold"
                style={{ color: '#c1a26a' }}
              >
                Legacy
              </div>
              <div className="text-sm text-gray-300 mt-1">
                {getLegacyDescription()}
              </div>
            </div>
          </TooltipContent>
        </Tooltip>
      )
    } else {
      iconElements.push(legacyIcon)
    }
  }

  return (
    <div className={`flex items-center space-x-1 ${className}`}>
      {iconElements}
    </div>
  )
}

/**
 * Simplified version for cases where you just need the icons without tooltips
 */
export function SkinRarityIconsSimple({
  rarity,
  isLegacy,
  isBase,
  className = "",
  size = 16
}: Omit<SkinRarityIconsProps, 'showTooltip'>) {
  return (
    <SkinRarityIcons
      rarity={rarity}
      isLegacy={isLegacy}
      isBase={isBase}
      className={className}
      size={size}
      showTooltip={false}
    />
  )
}
