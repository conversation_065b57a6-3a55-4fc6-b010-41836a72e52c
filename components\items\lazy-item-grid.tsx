"use client"

import { useState, useEffect, useMemo, useRef, useCallback } from 'react'
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import Image from "next/image"
import Link from "next/link"

interface ItemData {
  id: string
  name: string
  description: string
  plaintext: string
  image: string
  category: string
  formattedPrice: string
  sellPrice: string
  components: string[]
  buildsInto: string[]
  isPurchasable: boolean
  tags: string[]
  gold: {
    base: number
    total: number
    sell: number
    purchasable: boolean
  }
  stats: Record<string, number>
  mainStats: string[]
  isFullItem: boolean
  maps: Record<string, boolean>
}

interface LazyItemGridProps {
  items: ItemData[]
  isClient: boolean
}

const INITIAL_LOAD = 30
const LOAD_INCREMENT = 20

// Function to format tags (add spaces between words)
function formatTag(tag: string): string {
  return tag.replace(/([a-z])([A-Z])/g, '$1 $2')
}

// Function to truncate long tags for display
function truncateTag(tag: string, maxLength: number = 12): string {
  const formatted = formatTag(tag)
  if (formatted.length <= maxLength) {
    return formatted
  }
  return formatted.substring(0, maxLength) + '...'
}

// Function to create URL-friendly item names
function createItemSlug(itemName: string): string {
  return itemName
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters except spaces and hyphens
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .replace(/^-|-$/g, '') // Remove leading/trailing hyphens
}

export default function LazyItemGrid({ items, isClient }: LazyItemGridProps) {
  const [displayCount, setDisplayCount] = useState(INITIAL_LOAD)
  const [isLoading, setIsLoading] = useState(false)
  const loadingTriggerRef = useRef<HTMLDivElement>(null)

  // Get items to display
  const displayedItems = useMemo(() => {
    return items.slice(0, displayCount)
  }, [items, displayCount])

  const hasMore = displayCount < items.length

  // Load more items
  const loadMore = useCallback(() => {
    if (!isLoading && hasMore) {
      setIsLoading(true)
      // Reduced loading delay for smoother experience
      setTimeout(() => {
        setDisplayCount(prev => Math.min(prev + LOAD_INCREMENT, items.length))
        setIsLoading(false)
      }, 100)
    }
  }, [isLoading, hasMore, items.length])

  // Intersection Observer for lazy loading
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries
        if (entry.isIntersecting) {
          loadMore()
        }
      },
      {
        rootMargin: '200px', // Start loading 200px before the trigger comes into view
        threshold: 0.1
      }
    )

    const currentTrigger = loadingTriggerRef.current
    if (currentTrigger && hasMore) {
      observer.observe(currentTrigger)
    }

    return () => {
      if (currentTrigger) {
        observer.unobserve(currentTrigger)
      }
    }
  }, [hasMore, loadMore])

  // Reset display count when items change (due to filtering)
  useEffect(() => {
    setDisplayCount(INITIAL_LOAD)
  }, [items])

  if (!isClient) {
    return null
  }

  return (
    <>
      {/* Items Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
        {displayedItems.map((item, index) => (
          <Link
            key={index}
            href={`/items/${createItemSlug(item.name)}`}
            className="block"
          >
            <Card className="bg-gray-900/40 border-purple-700/20 hover:border-purple-400/50 transition-all duration-300 cursor-pointer">
            <CardContent className="p-3">
              <div className="flex items-center gap-3">
                {/* Item Icon */}
                <div className="flex-shrink-0">
                  <div
                    className="rounded-lg overflow-hidden bg-gray-800/50"
                    style={{
                      width: "48px",
                      height: "48px"
                    }}
                  >
                    <Image
                      src={item.image || "/placeholder.svg"}
                      alt={item.name}
                      width={48}
                      height={48}
                      className="w-full h-full object-contain"
                      loading="lazy"
                    />
                  </div>
                </div>

                {/* Item Details */}
                <div className="flex-1 min-w-0">
                  {/* Item Name */}
                  <h3 className="font-semibold text-white text-sm mb-1 truncate">
                    {item.name}
                  </h3>

                  {/* Tags */}
                  <div className="flex flex-wrap gap-1">
                    {item.tags.length > 0 && (
                      <>
                        {/* First Tag */}
                        <Badge
                          variant="secondary"
                          className="border-blue-600/30 text-blue-400 bg-blue-600/20 text-xs"
                        >
                          {formatTag(item.tags[0])}
                        </Badge>

                        {/* Second Tag (if exists) */}
                        {item.tags.length > 1 && (
                          <Badge
                            variant="secondary"
                            className="border-blue-600/30 text-blue-400 bg-blue-600/20 text-xs"
                          >
                            {truncateTag(item.tags[1])}
                          </Badge>
                        )}

                        {/* Additional Tags Indicator */}
                        {item.tags.length > 2 && (
                          <div className="relative group">
                            <Badge
                              variant="secondary"
                              className="border-orange-600/30 text-orange-400 bg-orange-600/20 text-xs cursor-pointer hover:border-orange-500/50 transition-colors"
                            >
                              +{item.tags.length - 2}
                            </Badge>
                            {/* Custom Tooltip */}
                            <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 hidden group-hover:block z-50">
                              <div className="bg-slate-800/80 text-slate-100 border border-slate-700/50 backdrop-blur-sm rounded-md px-3 py-1.5 shadow-lg text-xs">
                                <div className="flex flex-wrap gap-1 min-w-max">
                                  {item.tags.slice(2).map((tag, tagIndex) => (
                                    <Badge
                                      key={tagIndex}
                                      variant="secondary"
                                      className="border-blue-600/30 text-blue-400 bg-blue-600/20 text-xs whitespace-nowrap"
                                    >
                                      {formatTag(tag)}
                                    </Badge>
                                  ))}
                                </div>
                                {/* Arrow pointing down */}
                                <div className="absolute top-full left-1/2 transform -translate-x-1/2">
                                  <div className="w-0 h-0 border-l-[6px] border-r-[6px] border-t-[6px] border-l-transparent border-r-transparent border-t-slate-800/80"></div>
                                </div>
                              </div>
                            </div>
                          </div>
                        )}
                      </>
                    )}
                  </div>
                </div>

                {/* Price */}
                <div className="flex-shrink-0">
                  {item.gold.total === 0 ? (
                    <span className="font-medium text-sm text-gray-400">
                      N/A
                    </span>
                  ) : (
                    <div className="flex items-center gap-1">
                      <span
                        className="font-medium text-sm"
                        style={{ color: '#fb923c' }}
                      >
                        {item.gold.total}
                      </span>
                      <Image
                        src="https://raw.communitydragon.org/latest/game/assets/ux/tft/regionportals/icon/gold.png"
                        alt="Gold"
                        width={12}
                        height={12}
                        className="w-3 h-3 flex-shrink-0"
                        style={{
                          userSelect: 'none',
                          pointerEvents: 'none',
                          filter: 'brightness(0) saturate(100%) invert(73%) sepia(85%) saturate(1234%) hue-rotate(359deg) brightness(95%) contrast(95%)'
                        }}
                        draggable={false}
                      />
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
            </Card>
          </Link>
        ))}
      </div>

      {/* Intersection Observer Trigger */}
      {hasMore && (
        <div
          ref={loadingTriggerRef}
          className="w-full h-20 flex items-center justify-center mt-8"
        >
          {isLoading && (
            <div className="inline-flex items-center space-x-2">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-purple-400"></div>
              <span className="text-gray-300">Loading more items...</span>
            </div>
          )}
        </div>
      )}

      {/* End message */}
      {!hasMore && displayedItems.length > INITIAL_LOAD && (
        <div className="text-center py-8">
          <p className="text-gray-400">
            You've reached the end! Showing all {displayedItems.length} items.
          </p>
        </div>
      )}
    </>
  )
}
