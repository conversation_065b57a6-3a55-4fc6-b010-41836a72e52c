"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import {
  Tooltip as ShadcnTooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { leagueApiBrowser } from "@/lib/api/league-client-browser"
import { SkinService } from "@/lib/services/skin-service"
import { getClassTagColor, getRoleTagColor } from "@/lib/utils/tag-colors"
import { Calendar, ChevronLeft, ChevronRight, X } from "lucide-react"
import Image from "next/image"
import { useRouter } from "next/navigation"
import { useEffect, useRef, useState } from "react"

// Utility function to get position icon URLs
function getPositionIconUrl(role: string): string {
  const positionIcons: Record<string, string> = {
    'Top': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-social/global/default/positionicon-top.png',
    'Middle': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-social/global/default/positionicon-mid.png',
    'Jungle': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-social/global/default/positionicon-jungle.png',
    'Bottom': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-social/global/default/positionicon-bottom.png',
    'Support': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-social/global/default/positionicon-support.png'
  }
  return positionIcons[role] || positionIcons['Middle'] // fallback to Middle if role not found
}

// Function to get champion's primary class from tags
function getChampionClass(tags: string[]): string {
  if (!tags || tags.length === 0) return 'Fighter'

  // Return the first tag as the primary class for display
  return tags[0] || 'Fighter'
}

interface ChampionPageProps {
  champion: any
}

// Transform API data to match Jinx page structure
async function transformChampionData(champion: any) {
  
  
  

  // Fetch real chroma data using the rebuilt system
  
  

  const skinsWithChromas = await fetchChromaData(champion.name, champion.key, champion.skins)
  

  // Fetch skin pricing data from the SkinService
  
  const skinService = SkinService.getInstance()
  const allSkinsData = await skinService.getAllSkins()

  // Create a map of skin names to pricing data for quick lookup
  const skinPriceMap = new Map()
  allSkinsData.forEach(skin => {
    skinPriceMap.set(skin.name, {
      price: skin.price,
      releaseDate: skin.releaseDate
    })
  })
  

  


  // Helper function to check if an image URL exists
  const checkImageExists = async (url: string): Promise<boolean> => {
    try {
      const response = await fetch(url, { method: 'HEAD' })
      return response.ok
    } catch {
      return false
    }
  }

  // Helper function to generate circular portrait URL with fallback for default skin
  const getCircularPortraitUrl = async (championName: string, skinNum: number, skinName: string): Promise<string> => {
    // Use the LeagueApiClient method which now handles prestige year variants and all other special cases
    return await leagueApiBrowser.getChampionCircleUrl(championName, skinNum, skinName, champion.skins, champion.key)
  }

  // Transform skins data with chroma integration - use Promise.all for async operations
  const transformedSkins = await Promise.all(champion.skins.map(async (skin: any, index: number) => {
    const isBase = skin.num === 0
    const tier = determineSkinTier(skin.name)
    const skinWithChromas = skinsWithChromas[index]

    // Generate in-game image URL if skin has chromas
    let inGameImageUrl = ''
    if (skinWithChromas.chromas && skinWithChromas.chromas.length > 0) {
      // Try to use chromaPath from the first chroma (base skin chroma)
      const baseChroma = skinWithChromas.chromas.find((c: any) => c.isDefault) || skinWithChromas.chromas[0]
      if (baseChroma && baseChroma.chromaPath) {
        // Extract the path after "/v1/" and convert to lowercase
        const v1Index = baseChroma.chromaPath.indexOf('/v1/')
        if (v1Index !== -1) {
          const pathAfterV1 = baseChroma.chromaPath.substring(v1Index + 1)
          const lowercasePath = pathAfterV1.toLowerCase()
          const baseUrl = 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default'
          inGameImageUrl = `${baseUrl}/${lowercasePath}`
        }
      }

      // Fallback to old method if chromaPath is not available
      if (!inGameImageUrl) {
        const championKey = champion.key
        const skinId = parseInt(championKey) * 1000 + skin.num
        inGameImageUrl = `https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/champion-chroma-images/${championKey}/${skinId}.png`
      }
    }

    // Generate circular portrait URL (will be resolved async)
    const skinName = skin.name === 'default' ? `Classic ${champion.name}` : skin.name

    // Use the LeagueApiClient method which handles all the special cases
    const circularPortraitUrl = await leagueApiBrowser.getChampionCircleUrl(champion.id, skin.num, skinName, champion.skins, champion.key)
    

    // Determine price using only real API data - no fallbacks
    let skinPrice = 'Free'
    if (!isBase) {
      // Try to get real pricing from the skin service data
      const skinPriceData = skinPriceMap.get(skinName)
      if (skinPriceData && skinPriceData.price && skinPriceData.price !== 'N/A') {
        skinPrice = skinPriceData.price
      } else {
        // If not found in API, always show N/A
        skinPrice = 'N/A'
      }
    }

    // Generate splash art URL using the new Community Dragon method
    const splashArtUrl = await leagueApiBrowser.getChampionSplashUrlNew(champion.id, skin.num, champion.key)

    return {
      name: skinName,
      price: skinPrice,
      rarity: tier,
      image: circularPortraitUrl, // Circular skin portrait from Community Dragon
      splashArt: splashArtUrl, // Community Dragon uncentered splash art
      inGameImage: inGameImageUrl, // Community Dragon in-game image (only if has chromas)
      isBase,
      hasInGameImage: skinWithChromas.chromas && skinWithChromas.chromas.length > 0, // Track if this skin has in-game image
      chromas: skinWithChromas.chromas || [], // Real chroma data from Community Dragon
      skinNum: skin.num // Store original skin number for fallback logic
    }
  }))

  // Get champion resource type for cleaning ability text
  const resourceType = getChampionResourceType(champion)

  // Transform abilities
  const transformedAbilities = [
    {
      name: champion.passive.name,
      icon: null,
      iconImage: leagueApiBrowser.getChampionPassiveUrl(champion.passive.image.full),
      description: cleanAbilityText(champion.passive.description, resourceType),
      cooldown: null,
      cost: null,
      isPassive: true
    },
    ...champion.spells.map((spell: any) => ({
      key: ['Q', 'W', 'E', 'R'][champion.spells.indexOf(spell)],
      name: spell.name,
      icon: null,
      iconImage: leagueApiBrowser.getChampionSpellUrl(spell.image.full),
      description: cleanAbilityText(spell.description, resourceType),
      cooldown: spell.cooldownBurn,
      cost: cleanCostText(spell.costBurn, spell.costType, resourceType),
      isPassive: false
    }))
  ]

  return {
    championInfo: {
      name: champion.name,
      title: champion.title,
      role: role,
      releaseDate: 'Unknown', // API doesn't provide release date
      costBE: 6300, // Default cost, API doesn't provide this
      costRP: 975   // Default cost, API doesn't provide this
    },
    championStats: {
      difficulty: champion.info.difficulty,
      attack: champion.info.attack,
      defense: champion.info.defense,
      magic: champion.info.magic,
      health: Math.round(champion.stats.hp),
      mana: Math.round(champion.stats.mp),
      attackDamage: Math.round(champion.stats.attackdamage),
      attackSpeed: champion.stats.attackspeed,
      armor: Math.round(champion.stats.armor),
      magicResist: Math.round(champion.stats.spellblock),
      movementSpeed: Math.round(champion.stats.movespeed)
    },
    abilities: transformedAbilities,
    skins: transformedSkins,
    lore: champion.lore || champion.blurb || 'No lore available.',
    tags: champion.tags
  }
}

// Determine skin tier based on name patterns (used for rarity display only)
function determineSkinTier(skinName: string): string {
  const name = skinName.toLowerCase()

  if (name === 'default' || name.includes('classic')) return 'Base'
  if (name.includes('legendary') || name.includes('god') || name.includes('project')) return 'Legendary'
  if (name.includes('epic') || name.includes('star guardian') || name.includes('arcade')) return 'Epic'
  if (name.includes('ultimate') || name.includes('pulsefire')) return 'Ultimate'
  if (name.includes('mythic') || name.includes('prestige')) return 'Mythic'
  if (name.includes('victorious') || name.includes('championship')) return 'Victorious'

  return 'Regular'
}

// Interface for chroma data
interface ChromaData {
  id: string
  name: string
  color: string // Primary color (for backward compatibility)
  colors?: string[] // Array of all colors for multi-color chromas
  chromaPath?: string
  skinId?: number // Base skin ID for image fetching
  isDefault?: boolean // Whether this is the default skin option
}

// Interface for skin with chromas
interface SkinWithChromas {
  id: number
  name: string
  chromas?: ChromaData[]
}

// Completely rebuilt chroma system using server-side API to avoid CORS issues
async function fetchChromaData(championName: string, championKey: string, skinData: any[]): Promise<SkinWithChromas[]> {
  

  try {
    // Use our server-side API route to fetch chroma data
    const response = await fetch(`/api/champions/${championName}/chromas`)

    if (!response.ok) {
      console.warn(`⚠️ CLIENT-SIDE: Failed to fetch chroma data for ${championName}`)
      return skinData.map(skin => ({ id: skin.id || skin.num, name: skin.name, chromas: [] }))
    }

    const result = await response.json()

    if (!result.success) {
      console.warn(`⚠️ CLIENT-SIDE: API returned error for ${championName}:`, result.error)
      return skinData.map(skin => ({ id: skin.id || skin.num, name: skin.name, chromas: [] }))
    }

    
    

    // The API already processed the chroma data, so we can use it directly
    const processedSkins = result.data.skins

    

    // Convert to the expected format
    return processedSkins.map((skin: any) => ({
      id: skin.id,
      name: skin.name,
      chromas: skin.chromas || []
    }))

  } catch (error) {
    console.error(`❌ Error fetching chroma data for champion ${championName}:`, error)
    return skinData.map(skin => ({ id: skin.id || skin.num, name: skin.name, chromas: [] }))
  }
}

// Function to determine champion resource type and clean up ability text
function getChampionResourceType(champion: any): string {
  const championId = champion.id.toLowerCase()

  // Energy champions (mostly ninjas and some assassins)
  const energyChampions = ['akali', 'kennen', 'shen', 'zed', 'leesin']
  if (energyChampions.includes(championId)) {
    return 'Energy'
  }

  // Flow champions
  if (championId === 'yasuo' || championId === 'yone') {
    return 'Flow'
  }

  // Heat champions
  if (championId === 'rumble') {
    return 'Heat'
  }

  // Fury/Rage champions
  const furyChampions = ['gnar', 'shyvana', 'rengar', 'renekton', 'tryndamere']
  if (furyChampions.includes(championId)) {
    return 'Fury'
  }

  // Ferocity champions
  if (championId === 'rengar') {
    return 'Ferocity'
  }

  // Bloodthirst/Health champions
  if (championId === 'vladimir' || championId === 'mordekaiser') {
    return 'Health'
  }

  // Shield champions
  if (championId === 'mordekaiser') {
    return 'Shield'
  }

  // No cost champions (mostly resourceless)
  const noCostChampions = ['garen', 'katarina', 'riven', 'yasuo', 'yone']
  if (noCostChampions.includes(championId) || champion.stats.mp === 0) {
    return 'No Cost'
  }

  // Default to Mana for most champions
  return 'Mana'
}

// Function to clean up ability descriptions and costs
function cleanAbilityText(text: string, resourceType: string): string {
  if (!text) return text

  // Replace common placeholders with proper resource names
  let cleanedText = text
    .replace(/\{\{\s*abilityresourcename\s*\}\}/gi, resourceType)
    .replace(/\{\{\s*cost\s*\}\}/gi, 'Cost')
    .replace(/\{\{\s*cooldown\s*\}\}/gi, 'Cooldown')
    .replace(/\{\{\s*range\s*\}\}/gi, 'Range')
    .replace(/\{\{\s*damage\s*\}\}/gi, 'Damage')
    .replace(/\{\{\s*effect\d*amount\d*\s*\}\}/gi, 'Effect')
    .replace(/\{\{\s*spell\.\w+\s*\}\}/gi, '')
    .replace(/\{\{\s*[^}]+\s*\}\}/gi, '') // Remove any remaining placeholders (improved pattern)
    .replace(/<br\s*\/?>/gi, ' ') // Remove <br> tags and replace with space
    .replace(/<\/?[^>]+(>|$)/g, '') // Remove any other HTML tags
    .replace(/\s+/g, ' ') // Replace multiple spaces with single space
    .trim() // Remove leading/trailing whitespace

  return cleanedText
}

// Function to clean up cost text specifically
function cleanCostText(costBurn: string, costType: string, resourceType: string): string {
  if (!costBurn && !costType) return 'No Cost'

  // Handle "No Cost" champions
  if (resourceType === 'No Cost' || costType === 'No Cost') {
    return 'No Cost'
  }

  // Handle zero cost
  if (costBurn === '0' || !costBurn) {
    return 'No Cost'
  }

  // Build cost string
  let costText = costBurn
  if (costType && costType !== 'No Cost') {
    costText += ` ${costType}`
  }

  return cleanAbilityText(costText, resourceType)
}

// Get rarity icon URL
function getRarityIcon(rarity: string) {
  const iconMap: Record<string, string> = {
    'Legacy': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/summoner-icon/icon-legacy.png',
    'Rare': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-parties/global/default/summoner-icon-rare.png',
    'Transcendent': 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/cn-gem-11.png',
    'Epic': 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/cn-gem-3.png',
    'Mythic': 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/cn-gem-8.png',
    'Legendary': 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/legendary.png',
    'Ultimate': 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/ultimate.png',
    'Exalted': 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/cn-gem-10.png'
  }
  return iconMap[rarity] || null
}

// Get rarity description
function getRarityDescription(rarity: string) {
  const descriptions: Record<string, string> = {
    'Base': 'Base Skin - Included with champion purchase',
    'Legacy': 'Legacy Skin - No longer available for purchase',
    'Rare': 'Rare Skin - Basic skin with slight visual changes',
    'Epic': 'Epic Skin - New model, textures, splash art, and sometimes new animations',
    'Legendary': 'Legendary Skin - Completely new model, textures, splash art, animations, and voice-over',
    'Ultimate': 'Ultimate Skin - Highest tier with multiple forms or evolving features',
    'Mythic': 'Mythic Skin - Exclusive high-tier skin with unique features',
    'Transcendent': 'Transcendent Skin - Ultra-rare skin with exceptional quality',
    'Exalted': 'Exalted Skin - Premium exclusive skin with unique features'
  }
  return descriptions[rarity] || 'Unknown rarity'
}

// Fallback function to create basic champion data without async operations
async function createFallbackChampionData(champion: any) {
  

  // Use the new chroma system in fallback as well
  const skinsWithChromas = await fetchChromaData(champion.name, champion.key, champion.skins)

  // Fetch skin pricing data from the SkinService for fallback as well
  
  const skinService = SkinService.getInstance()
  const allSkinsData = await skinService.getAllSkins()

  // Create a map of skin names to pricing data for quick lookup
  const skinPriceMap = new Map()
  allSkinsData.forEach(skin => {
    skinPriceMap.set(skin.name, {
      price: skin.price,
      releaseDate: skin.releaseDate
    })
  })
  


  const resourceType = getChampionResourceType(champion)

  // Transform skins data using the new chroma system - use Promise.all for async operations
  const transformedSkins = await Promise.all(skinsWithChromas.map(async (skinWithChromas: SkinWithChromas) => {
    // Find the original skin data
    const originalSkin = champion.skins.find((s: any) => (s.id || s.num) === skinWithChromas.id)
    if (!originalSkin) {
      console.warn(`Could not find original skin data for skin ID ${skinWithChromas.id}`)
      return null
    }

    const isBase = originalSkin.num === 0
    const tier = determineSkinTier(originalSkin.name)
    const skinName = originalSkin.name === 'default' ? `Classic ${champion.name}` : originalSkin.name

    // Use the LeagueApiClient method which handles all the special cases
    const circularPortraitUrl = await leagueApiBrowser.getChampionCircleUrl(champion.id, originalSkin.num, skinName, champion.skins, champion.key)

    // Use the chromas from the new chroma system
    const chromas = skinWithChromas.chromas || []
    

    // Determine price using only real API data - no fallbacks
    let skinPrice = 'Free'
    if (!isBase) {
      // Try to get real pricing from the skin service data
      const skinPriceData = skinPriceMap.get(skinName)
      if (skinPriceData && skinPriceData.price && skinPriceData.price !== 'N/A') {
        skinPrice = skinPriceData.price
      } else {
        // If not found in API, always show N/A
        skinPrice = 'N/A'
      }
    }

    // Generate splash art URL using the new Community Dragon method
    const splashArtUrl = await leagueApiBrowser.getChampionSplashUrlNew(champion.id, originalSkin.num, champion.key)

    return {
      name: skinName,
      price: skinPrice,
      rarity: tier,
      image: circularPortraitUrl,
      splashArt: splashArtUrl, // Community Dragon uncentered splash art
      inGameImage: chromas.length > 0 ? leagueApiBrowser.getChampionInGameImageUrl(champion.key, leagueApiBrowser.generateSkinId(champion.key, originalSkin.num)) : '',
      isBase,
      hasInGameImage: chromas.length > 0,
      chromas,
      skinNum: originalSkin.num
    }
  })).then(results => results.filter(Boolean)) // Remove any null entries

  // Transform abilities
  const transformedAbilities = [
    {
      name: champion.passive.name,
      icon: null,
      iconImage: leagueApiBrowser.getChampionPassiveUrl(champion.passive.image.full),
      description: cleanAbilityText(champion.passive.description, resourceType),
      cooldown: null,
      cost: null,
      isPassive: true
    },
    ...champion.spells.map((spell: any) => ({
      key: ['Q', 'W', 'E', 'R'][champion.spells.indexOf(spell)],
      name: spell.name,
      icon: null,
      iconImage: leagueApiBrowser.getChampionSpellUrl(spell.image.full),
      description: cleanAbilityText(spell.description, resourceType),
      cooldown: spell.cooldownBurn,
      cost: cleanCostText(spell.costBurn, spell.costType, resourceType),
      isPassive: false
    }))
  ]

  return {
    championInfo: {
      name: champion.name,
      title: champion.title,
      role: role,
      releaseDate: 'Unknown',
      costBE: 6300,
      costRP: 975
    },
    championStats: {
      difficulty: champion.info.difficulty,
      attack: champion.info.attack,
      defense: champion.info.defense,
      magic: champion.info.magic,
      health: Math.round(champion.stats.hp),
      mana: Math.round(champion.stats.mp),
      attackDamage: Math.round(champion.stats.attackdamage),
      attackSpeed: champion.stats.attackspeed,
      armor: Math.round(champion.stats.armor),
      magicResist: Math.round(champion.stats.spellblock),
      movementSpeed: Math.round(champion.stats.movespeed)
    },
    abilities: transformedAbilities,
    skins: transformedSkins,
    lore: champion.lore || champion.blurb || 'No lore available.',
    tags: champion.tags
  }
}

export default function DynamicChampionPage({ champion }: ChampionPageProps) {
  const router = useRouter()
  const [currentSkinIndex, setCurrentSkinIndex] = useState(0)
  const [showSplashModal, setShowSplashModal] = useState(false)
  const [isAnimating, setIsAnimating] = useState(false)
  const [showSplashArt, setShowSplashArt] = useState(true) // true = splash art, false = in-game image
  const [userPrefersInGame, setUserPrefersInGame] = useState(false) // Remember user preference

  const [resolvedSkinImages, setResolvedSkinImages] = useState<{[key: number]: string}>({})
  const [championData, setChampionData] = useState<any>(null)
  const [isLoadingChromas, setIsLoadingChromas] = useState(true)
  const [showChromas, setShowChromas] = useState(false)
  const [chromasAnimating, setChromasAnimating] = useState(false)
  const [selectedChromaId, setSelectedChromaId] = useState<string | null>(null)
  const chromasRef = useRef<HTMLDivElement>(null)

  const VISIBLE_SKINS = 9

  // Load champion data with chromas
  useEffect(() => {
    const loadChampionData = async () => {
      setIsLoadingChromas(true)

      try {
        // Add a timeout to prevent hanging
        const dataPromise = transformChampionData(champion)
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Transform timeout after 10 seconds')), 10000)
        )

        const data = await Promise.race([dataPromise, timeoutPromise])
        setChampionData(data)
      } catch (error) {
        console.error(`❌ Error loading champion data for ${champion.name}:`, error)
        console.error(`❌ Error details:`, {
          message: error instanceof Error ? error.message : 'Unknown error',
          stack: error instanceof Error ? error.stack : 'No stack trace',
          championName: champion.name,
          championKey: champion.key
        })
        // Use the fallback function for immediate data
        
        try {
          const fallbackData = await createFallbackChampionData(champion)
          setChampionData(fallbackData)
        } catch (fallbackError) {
          console.error(`❌ Fallback also failed for ${champion.name}:`, fallbackError)
          // Create minimal data structure as last resort
          setChampionData({
            name: champion.name,
            title: champion.title,
            skins: champion.skins.map((skin: any) => ({
              id: skin.id || skin.num,
              name: skin.name,
              chromas: []
            })),
            roles: champion.tags || [],
            stats: champion.stats || {}
          })
        }
      } finally {
        
        setIsLoadingChromas(false)
      }
    }

    loadChampionData()
  }, [champion])

  // Check for fallback URLs for default skins and preload all images
  useEffect(() => {
    if (typeof window !== 'undefined' && championData) {
      const checkDefaultSkinFallbacks = async () => {
        const newResolvedImages: {[key: number]: string} = {}
        const skins = championData.skins

        for (const skin of skins) {
          // Use the new LeagueApiClient method for all skins, especially default skins
          if (skin.isBase && skin.skinNum === 0) {
            try {
              const resolvedUrl = await leagueApiBrowser.getChampionCircleUrl(champion.id, skin.skinNum, undefined, champion.skins, champion.key)
              newResolvedImages[skin.skinNum] = resolvedUrl
            } catch (error) {
              console.error(`Failed to resolve circle URL for ${champion.id} skin ${skin.skinNum}:`, error)
              // Fallback to old pattern if new method fails
              const fallbackUrl = `https://raw.communitydragon.org/latest/game/assets/characters/${champion.id.toLowerCase()}/hud/${champion.id.toLowerCase()}_circle_0.png`
              newResolvedImages[skin.skinNum] = fallbackUrl
            }
          }

          // Enhanced preloading with loading state tracking
          const preloadPromises = []

          if (skin.image) {
            const imgPromise = new Promise((resolve, reject) => {
              const img = document.createElement('img')
              img.onload = resolve
              img.onerror = reject
              img.src = skin.image
            })
            preloadPromises.push(imgPromise)
          }

          if (skin.splashArt) {
            const splashPromise = new Promise((resolve, reject) => {
              const splashImg = document.createElement('img')
              splashImg.onload = resolve
              splashImg.onerror = reject
              splashImg.src = skin.splashArt
            })
            preloadPromises.push(splashPromise)
          }

          if (skin.inGameImage) {
            const inGamePromise = new Promise((resolve, reject) => {
              const inGameImg = document.createElement('img')
              inGameImg.onload = resolve
              inGameImg.onerror = reject
              inGameImg.src = skin.inGameImage
            })
            preloadPromises.push(inGamePromise)
          }

          // Wait for all images to load for this skin
          Promise.allSettled(preloadPromises).then(() => {
            // Individual skin images loaded
          })
        }

        setResolvedSkinImages(newResolvedImages)
      }

      checkDefaultSkinFallbacks()
    }
  }, [championData, champion.id])

  // Smart mode switching with user preference memory
  useEffect(() => {
    if (championData) {
      const currentSkin = championData.skins[currentSkinIndex]
      // Rule 3: In-Game toggle is only clickable if skin has chromas
      const canShowInGame = currentSkin?.chromas && currentSkin.chromas.length > 0

      if (!canShowInGame) {
        // Force splash art when in-game not available (no chromas)
        setShowSplashArt(true)
      } else if (userPrefersInGame) {
        // Auto-switch back to in-game when available and user prefers it
        setShowSplashArt(false)
      }
    }
  }, [currentSkinIndex, championData, userPrefersInGame])

  // Reset chroma selection when switching skins
  useEffect(() => {
    setSelectedChromaId(null)
    setShowChromas(false)
  }, [currentSkinIndex])

  // Handle ESC key to close modal
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && showSplashModal) {
        setShowSplashModal(false)
      }
    }

    if (showSplashModal) {
      document.addEventListener('keydown', handleKeyDown)
      return () => document.removeEventListener('keydown', handleKeyDown)
    }
  }, [showSplashModal])

  // Smooth chroma dropdown animations
  const toggleChromas = () => {
    if (chromasAnimating) return

    setChromasAnimating(true)
    setShowChromas(!showChromas)

    // Reset animation state after duration
    setTimeout(() => {
      setChromasAnimating(false)
    }, 300)
  }

  // Set initial skin to default (base) skin when champion data loads
  useEffect(() => {
    if (championData && championData.skins) {
      const defaultSkinIndex = championData.skins.findIndex((skin: any) => skin.isBase)
      if (defaultSkinIndex !== -1 && currentSkinIndex !== defaultSkinIndex) {
        setCurrentSkinIndex(defaultSkinIndex)
      }
    }
  }, [championData])

  // Close chromas panel when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (chromasRef.current && !chromasRef.current.contains(event.target as Node)) {
        setShowChromas(false)
      }
    }

    if (showChromas) {
      document.addEventListener('mousedown', handleClickOutside)
      document.addEventListener('click', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
      document.removeEventListener('click', handleClickOutside)
    }
  }, [showChromas])

  // Close modal on escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        setShowSplashModal(false)
      }
    }

    if (showSplashModal) {
      document.addEventListener('keydown', handleEscape)
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }

    return () => {
      document.removeEventListener('keydown', handleEscape)
      document.body.style.overflow = 'unset'
    }
  }, [showSplashModal])

  // Show loading state while champion data is being fetched
  if (isLoadingChromas || !championData) {
    return (
      <div className="container mx-auto px-8 py-8">
        {/* Champion Info Skeleton */}
        <div className="mb-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Champion Info */}
            <div>
              <div className="flex items-center space-x-4 mb-4">
                <div className="w-20 h-20 rounded-full bg-gray-700/50 animate-pulse" />
                <div>
                  <div className="flex items-center space-x-3 mb-2">
                    <div className="h-8 w-8 bg-gray-700/50 animate-pulse rounded" />
                    <div className="h-10 w-48 bg-gray-700/50 animate-pulse rounded" />
                    <div className="w-8 h-8 bg-gray-700/50 animate-pulse rounded" />
                  </div>
                  <div className="h-6 w-64 mb-3 bg-gray-700/50 animate-pulse rounded" />
                  <div className="flex items-center space-x-2 mb-3">
                    <div className="h-6 w-20 bg-gray-700/50 animate-pulse rounded" />
                    <div className="h-6 w-16 bg-gray-700/50 animate-pulse rounded" />
                  </div>
                </div>
              </div>

              {/* Win Rate Graph Skeleton */}
              <div className="mt-6">
                <div className="bg-black/70 backdrop-blur-md rounded-lg border border-orange-700/40 p-6 shadow-xl h-80 flex flex-col">
                  {/* Header */}
                  <div className="flex items-center justify-between mb-3">
                    <div>
                      <div className="h-6 w-32 mb-1 bg-gray-700/50 animate-pulse rounded" />
                      <div className="h-4 w-48 bg-gray-700/50 animate-pulse rounded" />
                    </div>
                    <div className="h-9 w-40 bg-gray-700/50 animate-pulse rounded" />
                  </div>

                  {/* Average Win Rate */}
                  <div className="mb-2">
                    <div className="flex items-center space-x-2">
                      <div className="h-4 w-24 bg-gray-700/50 animate-pulse rounded" />
                      <div className="h-5 w-12 bg-gray-700/50 animate-pulse rounded" />
                    </div>
                  </div>

                  {/* Chart Area */}
                  <div className="flex-1 relative">
                    <div className="w-full h-40 relative">
                      {/* Grid lines skeleton */}
                      <div className="absolute inset-0">
                        {Array.from({ length: 5 }).map((_, i) => (
                          <div
                            key={i}
                            className="absolute w-full border-t border-gray-700/30"
                            style={{ top: `${(i * 100) / 4}%` }}
                          />
                        ))}
                      </div>

                      {/* Y-axis labels skeleton */}
                      <div className="absolute left-0 top-0 h-full flex flex-col justify-between py-1">
                        {Array.from({ length: 5 }).map((_, i) => (
                          <div key={i} className="h-3 w-8 bg-gray-700/50 animate-pulse rounded" />
                        ))}
                      </div>

                      {/* Chart line skeleton */}
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="h-20 w-full rounded-lg bg-gray-700/50 animate-pulse" />
                      </div>

                      {/* X-axis labels skeleton */}
                      <div className="absolute bottom-0 left-0 right-0 flex justify-between px-10">
                        {Array.from({ length: 7 }).map((_, i) => (
                          <div key={i} className="h-3 w-6 bg-gray-700/50 animate-pulse rounded" />
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Skin Display Skeleton */}
            <div className="relative">
              {/* Toggle buttons skeleton */}
              <div className="flex items-center justify-between mb-4">
                <div className="h-9 w-9 bg-gray-700/50 animate-pulse rounded-lg" />
                <div className="flex items-center space-x-3">
                  <div className="h-4 w-16 bg-gray-700/50 animate-pulse rounded" />
                  <div className="h-6 w-12 bg-gray-700/50 animate-pulse rounded-full" />
                  <div className="h-4 w-16 bg-gray-700/50 animate-pulse rounded" />
                </div>
              </div>

              {/* Main display skeleton */}
              <div className="w-full h-96 rounded-lg bg-gray-800/40 border border-gray-700/20 animate-pulse" />

              {/* Skin carousel skeleton - Updated positioning */}
              <div className="relative mt-3" style={{ minHeight: '100px', paddingTop: '5px', paddingBottom: '0px' }}>
                <div className="flex items-center justify-center h-full" style={{ gap: '12px' }}>
                  {/* Left arrow skeleton */}
                  <div className="w-6 h-6 rounded-full bg-gray-700/50 animate-pulse" />

                  {/* Carousel container skeleton */}
                  <div className="relative overflow-visible" style={{ width: '660px', height: '140px' }}>
                    <div
                      className="relative"
                      style={{
                        height: '100px',
                        padding: '40px 20px',
                        width: '660px'
                      }}
                    >
                      <div className="flex space-x-1 justify-center items-center" style={{ height: '64px' }}>
                        {Array.from({ length: 9 }).map((_, index) => (
                          <div key={index} className="flex justify-center items-center flex-shrink-0" style={{ width: '72px' }}>
                            <div className="relative">
                              <div className="w-16 h-16 rounded-lg bg-gray-700/50 animate-pulse" />
                              {/* Rarity icon skeleton */}
                              <div className="absolute bottom-1 left-1/2 transform -translate-x-1/2">
                                <div className="w-4 h-4 rounded-full bg-gray-600/50 animate-pulse" />
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* Right arrow skeleton */}
                  <div className="w-6 h-6 rounded-full bg-gray-700/50 animate-pulse" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  const { championInfo, championStats, abilities, skins, lore, tags } = championData

  const currentSkin = skins[currentSkinIndex]
  const skinsCount = skins.filter((skin: any) => !skin.isBase).length
  // Rule 3: In-Game toggle is only clickable if skin has chromas
  const canShowInGame = currentSkin?.chromas && currentSkin.chromas.length > 0







  const scrollLeft = () => {
    if (isAnimating) return
    setIsAnimating(true)

    // Move to previous skin in circular fashion
    setCurrentSkinIndex((prev) => (prev - 1 + skins.length) % skins.length)

    setTimeout(() => {
      setIsAnimating(false)
    }, 500)
  }

  const scrollRight = () => {
    if (isAnimating) return
    setIsAnimating(true)

    // Move to next skin in circular fashion
    setCurrentSkinIndex((prev) => (prev + 1) % skins.length)

    setTimeout(() => {
      setIsAnimating(false)
    }, 500)
  }

  // With circular scrolling, we can always scroll
  const canScrollLeft = !isAnimating
  const canScrollRight = !isAnimating

  const handleSkinClick = (index: number) => {
    if (isAnimating || index === currentSkinIndex) return

    setIsAnimating(true)

    // Simply set the clicked skin as the new current skin
    setCurrentSkinIndex(index)

    // Reset animation state after animation completes
    setTimeout(() => {
      setIsAnimating(false)
    }, 500)
  }

  const handleSplashClick = () => {
    setShowSplashModal(true)
  }

  const handleToggleImageMode = () => {
    const newShowSplashArt = !showSplashArt
    setShowSplashArt(newShowSplashArt)

    // Remember user preference for in-game mode
    setUserPrefersInGame(!newShowSplashArt)
  }

  // Rule 7: When chroma is selected, auto-switch to In-Game mode
  const handleChromaSelect = (chromaId: string) => {
    setSelectedChromaId(selectedChromaId === chromaId ? null : chromaId)

    // If selecting a chroma (not deselecting), switch to In-Game mode
    if (selectedChromaId !== chromaId) {
      setShowSplashArt(false)
      setUserPrefersInGame(true)
    }
  }

  // Get current image URL based on mode and selected chroma
  const getCurrentImageUrl = () => {
    if (showSplashArt || !canShowInGame) {
      return currentSkin.splashArt
    }

    // In-Game mode
    if (selectedChromaId && championData) {
      const selectedChroma = currentSkin.chromas?.find((c: ChromaData) => c.id === selectedChromaId)
      if (selectedChroma) {
        if (selectedChroma.isDefault) {
          // Show default skin image
          return `https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/champion-chroma-images/${champion.key}/${selectedChroma.skinId}.png`
        } else {
          // Show selected chroma image
          return `https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/champion-chroma-images/${champion.key}/${selectedChroma.id}.png`
        }
      }
    }

    // Rule 5: Show main skin by default in In-Game mode (when no chroma is selected)
    if (currentSkin.chromas && currentSkin.chromas.length > 0) {
      // Use the default chroma (first one which should be the default skin)
      const defaultChroma = currentSkin.chromas.find((c: ChromaData) => c.isDefault)
      if (defaultChroma && defaultChroma.skinId) {
        return `https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/champion-chroma-images/${champion.key}/${defaultChroma.skinId}.png`
      }
    }

    return currentSkin.inGameImage
  }

  // Helper function to get sequential array of visible skins
  const getCircularVisibleSkins = () => {
    const totalSkins = skins.length
    const centerIndex = Math.floor(VISIBLE_SKINS / 2) // Index 4 for 9 skins (0-8)
    const visibleSkins = []

    // Find the default skin (base skin) and create sequential order
    const defaultSkinIndex = skins.findIndex((skin: any) => skin.isBase)

    // Create ordered skin list: [Default, Skin1, Skin2, Skin3, ...]
    const orderedSkins = []
    if (defaultSkinIndex !== -1) {
      orderedSkins.push(defaultSkinIndex) // Default skin first
      // Add other skins in their original order (excluding default)
      for (let i = 0; i < totalSkins; i++) {
        if (i !== defaultSkinIndex) {
          orderedSkins.push(i)
        }
      }
    } else {
      // Fallback if no base skin found
      for (let i = 0; i < totalSkins; i++) {
        orderedSkins.push(i)
      }
    }

    // Find position of current skin in the sequential ordered list
    const currentPositionInOrdered = orderedSkins.indexOf(currentSkinIndex)

    if (totalSkins >= VISIBLE_SKINS) {
      // For champions with 9+ skins, show 9 skins with circular wrapping around current
      for (let i = 0; i < VISIBLE_SKINS; i++) {
        const offset = i - centerIndex
        let targetPosition = currentPositionInOrdered + offset

        // Handle circular wrapping
        if (targetPosition < 0) {
          targetPosition = totalSkins + targetPosition
        } else if (targetPosition >= totalSkins) {
          targetPosition = targetPosition - totalSkins
        }

        const skinIndex = orderedSkins[targetPosition]
        visibleSkins.push({
          ...skins[skinIndex],
          originalIndex: skinIndex,
          displayIndex: i,
          isCenter: i === centerIndex
        })
      }
    } else {
      // For champions with fewer skins than display slots
      // Show all skins in sequential order with empty slots on sides

      for (let i = 0; i < VISIBLE_SKINS; i++) {
        const offset = i - centerIndex
        const targetPosition = currentPositionInOrdered + offset

        if (targetPosition < 0 || targetPosition >= totalSkins) {
          // This position should be empty
          visibleSkins.push(null)
        } else {
          // Show the skin at this sequential position
          const skinIndex = orderedSkins[targetPosition]
          visibleSkins.push({
            ...skins[skinIndex],
            originalIndex: skinIndex,
            displayIndex: i,
            isCenter: i === centerIndex
          })
        }
      }
    }

    return visibleSkins
  }

  return (
    <div className="container mx-auto px-8 py-8">

      {/* CSS for animations */}
      <style jsx>{`
        @keyframes slideInRight {
          from {
            opacity: 0;
            transform: translateX(-20px) translateY(-50%);
          }
          to {
            opacity: 1;
            transform: translateX(0) translateY(-50%);
          }
        }
      `}</style>

      {/* Header with Skin Navigation */}
      <div className="mb-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Champion Info */}
          <div>
            <div className="flex items-center space-x-4 mb-4">
              <div className="w-20 h-20 rounded-full overflow-hidden bg-black/50 border-2 border-orange-400/60 backdrop-blur-sm shadow-xl">
                <Image
                  src={leagueApiBrowser.getChampionSquareUrl(champion.id)}
                  alt={champion.name}
                  width={80}
                  height={80}
                  className="w-full h-full object-cover"
                />
              </div>
              <div>
                <div className="flex items-center space-x-3 mb-2">
                  <Image
                    src="https://i.ibb.co/PvXPxJMm/Champion-Gold.png"
                    alt="Champion"
                    width={32}
                    height={32}
                    className="h-8 w-8 drop-shadow-lg"
                    style={{ userSelect: 'none', pointerEvents: 'none' }}
                    draggable={false}
                  />
                  <h1 className="text-4xl font-bold text-white drop-shadow-lg">{championInfo.name}</h1>
                  <Image
                    src={getPositionIconUrl(championInfo.role)}
                    alt={championInfo.role}
                    width={32}
                    height={32}
                    className="w-8 h-8 drop-shadow-lg"
                    style={{ userSelect: 'none', pointerEvents: 'none' }}
                    draggable={false}
                  />
                </div>
                <p className="text-xl text-orange-300 mb-3 drop-shadow-md">{championInfo.title}</p>
                <div className="flex items-center space-x-2 mb-3">
                  <span
                    className={`px-3 py-1.5 text-sm rounded-full cursor-pointer hover:scale-105 transition-transform backdrop-blur-sm shadow-lg ${getClassTagColor(getChampionClass(tags))}`}
                    onClick={() => router.push(`/champions?class=${getChampionClass(tags)}`)}
                  >
                    {getChampionClass(tags)}
                  </span>
                  <span
                    className={`px-3 py-1.5 text-sm rounded-full cursor-pointer hover:scale-105 transition-transform backdrop-blur-sm shadow-lg ${getRoleTagColor(championInfo.role)}`}
                    onClick={() => router.push(`/champions?role=${championInfo.role}`)}
                  >
                    {championInfo.role}
                  </span>
                </div>

                {/* Cost, Skins Count, and Release Date */}
                <div className="space-y-2">
                  <div className="flex items-center space-x-4">
                    {/* Blue Essence */}
                    <ShadcnTooltip>
                      <TooltipTrigger asChild>
                        <div className="flex items-center space-x-2 bg-black/70 backdrop-blur-md rounded-lg px-3 py-2 border border-gray-700/50 shadow-lg cursor-help">
                          <Image
                            src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-navigation/global/default/rewardicons_blueessence.png"
                            alt="Blue Essence"
                            width={20}
                            height={20}
                            className="w-5 h-5"
                            style={{ userSelect: 'none', pointerEvents: 'none' }}
                            draggable={false}
                          />
                          <span className="text-cyan-400 font-medium drop-shadow-md">
                            {championInfo.costBE.toLocaleString()}
                          </span>
                        </div>
                      </TooltipTrigger>
                      <TooltipContent>
                        <div className="text-center">
                          <div className="font-semibold text-cyan-300">Blue Essence</div>
                          <div className="text-sm text-gray-300 mt-1">
                            Primary in-game currency earned from battle pass
                          </div>
                        </div>
                      </TooltipContent>
                    </ShadcnTooltip>

                    {/* RP */}
                    <ShadcnTooltip>
                      <TooltipTrigger asChild>
                        <div className="flex items-center space-x-2 bg-black/70 backdrop-blur-md rounded-lg px-3 py-2 border border-gray-700/50 shadow-lg cursor-help">
                          <Image
                            src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg"
                            alt="Riot Points"
                            width={20}
                            height={20}
                            className="w-5 h-5"
                            style={{ userSelect: 'none', pointerEvents: 'none' }}
                            draggable={false}
                          />
                          <span className="text-yellow-400 font-medium drop-shadow-md">{championInfo.costRP}</span>
                        </div>
                      </TooltipTrigger>
                      <TooltipContent>
                        <div className="text-center">
                          <div className="font-semibold text-yellow-300">Riot Points (RP)</div>
                          <div className="text-sm text-gray-300 mt-1">
                            Premium currency for instant unlocks
                          </div>
                        </div>
                      </TooltipContent>
                    </ShadcnTooltip>

                    {/* Skin Count */}
                    <ShadcnTooltip>
                      <TooltipTrigger asChild>
                        <div className="flex items-center space-x-2 bg-black/70 backdrop-blur-md rounded-lg px-3 py-2 border border-gray-700/50 shadow-lg cursor-help">
                          <Image
                            src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/skins_rewards.svg"
                            alt="Skins"
                            width={20}
                            height={20}
                            className="w-5 h-5"
                            style={{ userSelect: 'none', pointerEvents: 'none' }}
                            draggable={false}
                          />
                          <span style={{ color: '#dfcda8' }} className="font-medium drop-shadow-md">{skinsCount}</span>
                        </div>
                      </TooltipTrigger>
                      <TooltipContent>
                        <div className="text-center">
                          <div className="font-semibold" style={{ color: '#dfcda8' }}>Champion Skins</div>
                          <div className="text-sm text-gray-300 mt-1">
                            Total skins available (excluding base skin)
                          </div>
                        </div>
                      </TooltipContent>
                    </ShadcnTooltip>
                  </div>
                  <div className="flex items-center space-x-2 bg-black/70 backdrop-blur-md rounded-lg px-3 py-2 w-fit border border-gray-700/50 shadow-lg">
                    <Calendar className="h-4 w-4 text-gray-400" />
                    <span className="text-gray-300 text-sm drop-shadow-md">
                      Released: {championInfo.releaseDate}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Skin Display */}
          <div className="relative">
            {/* Chromas Button and View Mode Toggle - Same Line */}
            <div className="flex items-center justify-between mb-4">
              {/* Chromas Button - Left Side - Always visible */}
              <div className="relative flex items-center" ref={chromasRef}>
                {/* Chroma Icon Button - Always visible, disabled when no chromas */}
                <button
                  onClick={currentSkin.chromas && currentSkin.chromas.length > 0 ? toggleChromas : undefined}
                  disabled={!currentSkin.chromas || currentSkin.chromas.length === 0 || chromasAnimating}
                  className={`backdrop-blur-md rounded-lg p-2 border shadow-lg transition-all duration-300 ${
                    currentSkin.chromas && currentSkin.chromas.length > 0
                      ? `bg-black/80 hover:bg-black/90 border-orange-500/40 cursor-pointer ${showChromas ? 'ring-2 ring-orange-400/50 scale-105' : ''}`
                      : 'bg-gray-800/50 border-gray-600/30 cursor-not-allowed opacity-50'
                  } ${chromasAnimating ? 'pointer-events-none' : ''}`}
                  title={currentSkin.chromas && currentSkin.chromas.length > 0 ? 'View Chromas' : 'No Chromas Available'}
                >
                  <Image
                    src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/icons/chroma-icon.png"
                    alt="Chromas"
                    width={20}
                    height={20}
                    className="w-5 h-5"
                  />
                </button>

                {/* Horizontal Chromas Dropdown - Show/hide with animation */}
                {showChromas && currentSkin.chromas && currentSkin.chromas.length > 0 && (
                  <div className="absolute left-full top-0 ml-2 bg-black/90 backdrop-blur-md rounded-lg border border-orange-500/40 shadow-xl px-3 py-2 z-50 h-9 flex items-center animate-in slide-in-from-left-2 duration-300">
                    <div className="flex items-center gap-2">
                      {currentSkin.chromas.map((chroma: ChromaData) => {
                        // Check if chroma has truly different colors (not just duplicates)
                        const hasMultipleUniqueColors = chroma.colors && chroma.colors.length > 1 &&
                          chroma.colors[0] !== chroma.colors[1]

                        return (
                          <button
                            key={chroma.id}
                            onClick={() => handleChromaSelect(chroma.id)}
                            className={`w-5 h-5 rounded-full border-2 transition-all duration-300 relative flex items-center justify-center overflow-hidden transform hover:rotate-12 ${
                              selectedChromaId === chroma.id
                                ? 'border-orange-400 scale-110 shadow-lg shadow-orange-400/50 rotate-12'
                                : 'border-gray-500 hover:border-orange-400/60 hover:scale-105'
                            }`}
                            style={!hasMultipleUniqueColors ? { backgroundColor: chroma.color } : {}}
                            title={chroma.name}
                          >
                            {hasMultipleUniqueColors ? (
                              // Multi-color chroma: split circle
                              <div className="w-full h-full relative">
                                {chroma.colors!.map((color, index) => (
                                  <div
                                    key={index}
                                    className="absolute inset-0"
                                    style={{
                                      backgroundColor: color,
                                      clipPath: index === 0
                                        ? 'polygon(0% 0%, 100% 0%, 0% 100%)' // Left triangle
                                        : 'polygon(100% 0%, 100% 100%, 0% 100%)' // Right triangle
                                    }}
                                  />
                                ))}
                              </div>
                            ) : null}

                            {chroma.isDefault && (
                              // Default chroma: thick red diagonal line instead of "/"
                              <div className="absolute inset-0 pointer-events-none">
                                <div
                                  className="absolute bg-red-500 transform rotate-45"
                                  style={{
                                    width: '2px',
                                    height: '120%',
                                    left: '50%',
                                    top: '50%',
                                    transformOrigin: 'center',
                                    transform: 'translate(-50%, -50%) rotate(45deg)'
                                  }}
                                />
                              </div>
                            )}
                          </button>
                        )
                      })}
                    </div>
                  </div>
                )}
              </div>

              {/* View Mode Toggle - Right Side */}
              <div className="flex items-center space-x-3">
                <span className={`text-sm font-medium transition-colors ${
                  !canShowInGame ? 'text-gray-400' : (!showSplashArt ? 'text-orange-400' : 'text-gray-500')
                }`}>
                  In-Game {!canShowInGame && '(N/A)'}
                </span>
                <button
                  onClick={canShowInGame ? handleToggleImageMode : undefined}
                  disabled={!canShowInGame}
                  className={`relative w-12 h-6 rounded-full transition-colors ${
                    canShowInGame
                      ? (showSplashArt ? 'bg-orange-500/80 hover:bg-orange-400/80 cursor-pointer' : 'bg-gray-600/50 hover:bg-gray-500/50 cursor-pointer')
                      : 'bg-gray-500/30 cursor-not-allowed opacity-50'
                  }`}
                >
                  <div
                    className={`absolute w-5 h-5 bg-white rounded-full top-0.5 transition-all duration-300 ${
                      showSplashArt || !canShowInGame ? 'left-6' : 'left-0.5'
                    } ${!canShowInGame ? 'opacity-70' : ''}`}
                  />
                </button>
                <span className={`text-sm font-medium transition-colors ${
                  showSplashArt || !canShowInGame ? 'text-orange-400' : 'text-gray-500'
                }`}>
                  Splash Art
                </span>
              </div>
            </div>

            {/* Main Display */}
            <div
              className="w-full h-96 rounded-lg overflow-hidden bg-black/30 backdrop-blur-sm relative border border-orange-500/40 shadow-2xl cursor-pointer group"
              onClick={handleSplashClick}
            >
              {(showSplashArt || !canShowInGame) ? (
                // Splash Art Mode
                <Image
                  src={getCurrentImageUrl() || "/placeholder.svg"}
                  alt={currentSkin.name}
                  width={600}
                  height={320}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
              ) : (
                // In-Game Mode with Background
                <div className="relative w-full h-full">
                  {/* Background Image - Larger to match splash art zoom */}
                  <Image
                    src="https://i.ibb.co/z91Cdg3/image.png"
                    alt="In-game background"
                    width={800}
                    height={450}
                    className="w-full h-full object-cover"
                  />
                  {/* In-Game Model Centered Over Background - 75% smaller */}
                  <div className="absolute inset-0 flex items-center justify-center">
                    <Image
                      src={getCurrentImageUrl() || "/placeholder.svg"}
                      alt={
                        selectedChromaId && currentSkin.chromas?.length > 0
                          ? `${currentSkin.name} - ${currentSkin.chromas.find((c: ChromaData) => c.id === selectedChromaId)?.name || 'Chroma'}`
                          : currentSkin.name
                      }
                      width={300}
                      height={160}
                      className="max-w-[75%] max-h-[75%] object-contain group-hover:scale-105 transition-transform duration-300"
                    />
                  </div>
                </div>
              )}

              {/* Hover overlay */}
              <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
              </div>

              {/* Skin Info Overlay */}
              <div className="absolute bottom-4 left-4 right-4 bg-black/90 backdrop-blur-md rounded-lg p-4 border border-orange-500/40 shadow-xl">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-semibold text-white drop-shadow-md">{currentSkin.name}</h3>
                    <div className="flex items-center space-x-1">
                      {currentSkin.price === "Free" ? (
                        <p className="text-orange-400 font-medium drop-shadow-md">Free</p>
                      ) : (
                        <div className="flex items-center space-x-1">
                          <span className="text-orange-400 font-medium drop-shadow-md">{currentSkin.price}</span>
                          <Image
                            src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg"
                            alt="RP"
                            width={16}
                            height={16}
                            className="w-4 h-4"
                            style={{ userSelect: 'none', pointerEvents: 'none' }}
                            draggable={false}
                          />
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-xs px-2 py-1 rounded bg-orange-600/80 text-orange-100">
                      {(showSplashArt || !canShowInGame) ? 'Splash Art' : 'In-Game'}
                    </span>
                    <div className="text-sm text-gray-300 drop-shadow-md">
                      {currentSkinIndex + 1} / {skins.length}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Skin Carousel */}
            <div className="relative mt-3" style={{ minHeight: '100px', paddingTop: '5px', paddingBottom: '0px' }}>
              <div className="flex items-center justify-center h-full" style={{ gap: '12px' }}>
                {/* Left Arrow - Circular and aligned with splash art */}
                <button
                  onClick={scrollLeft}
                  disabled={!canScrollLeft}
                  className={`flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center transition-all duration-200 relative border-2 ${
                    canScrollLeft
                      ? "bg-orange-600/80 hover:bg-orange-500/90 text-white shadow-lg backdrop-blur-sm border-orange-500/60 hover:border-orange-400"
                      : "bg-gray-700/50 text-gray-500 cursor-not-allowed border-gray-600/50"
                  }`}
                  style={{ zIndex: 150 }}
                >
                  <ChevronLeft className="h-5 w-5" />
                </button>

                {/* Skin carousel container - Optimized for 9 skins with reduced spacing */}
                <div className="relative overflow-visible" style={{ width: '720px', height: '120px' }}>
                  <div
                    className="relative cursor-grab"
                    style={{
                      height: '100px',
                      padding: '30px 20px',
                      width: '720px',
                      userSelect: 'none',
                      WebkitUserSelect: 'none'
                    }}
                  >
                    {/* Smooth carousel - clicked skin animates to middle position */}
                    <div
                      className="flex space-x-1 justify-center items-center transition-all duration-500 ease-out"
                      style={{ height: '64px' }}
                    >
                      {Array.from({ length: VISIBLE_SKINS }, (_, slotIndex) => {
                        const visibleSkins = getCircularVisibleSkins()
                        const skin = visibleSkins[slotIndex]
                        const centerIndex = Math.floor(VISIBLE_SKINS / 2)
                        const isCenter = slotIndex === centerIndex

                        // If this slot is empty (null), show empty slot
                        if (skin === null) {
                          return (
                            <div
                              key={`empty-slot-${slotIndex}`}
                              className="flex justify-center items-center flex-shrink-0"
                              style={{ width: '72px' }}
                            >
                              {/* Empty slot with dotted border */}
                              <div className="w-16 h-16 rounded-lg border-2 border-dashed border-gray-500/80 bg-gray-800/40 backdrop-blur-sm flex items-center justify-center">
                                <div className="w-2 h-2 rounded-full bg-gray-500/60"></div>
                              </div>
                            </div>
                          )
                        }

                        const rarityIcon = getRarityIcon(skin.rarity)

                        return (
                          <div
                            key={`skin-${skin.originalIndex}-slot-${slotIndex}`}
                            className="flex justify-center items-center flex-shrink-0"
                            style={{ width: '72px' }}
                          >
                            <button
                              onClick={() => handleSkinClick(skin.originalIndex)}
                              className={`w-16 h-16 rounded-lg overflow-hidden border-2 backdrop-blur-sm shadow-lg relative transition-all duration-500 ease-out ${
                                isCenter
                                  ? "border-orange-400 scale-110 shadow-orange-400/50"
                                  : "border-gray-600 hover:border-orange-400/60 hover:scale-105"
                              }`}
                              style={{
                                zIndex: isCenter ? 100 : 50,
                                userSelect: 'none',
                                WebkitUserSelect: 'none'
                              }}
                            >
                              {/* Skin image with smooth transition */}
                              <div className={`w-full h-full transition-all duration-500 ease-out ${isAnimating ? 'opacity-80' : 'opacity-100'}`}>
                                <Image
                                  src={
                                    (skin.isBase && skin.skinNum === 0 && resolvedSkinImages[skin.skinNum])
                                      ? resolvedSkinImages[skin.skinNum]
                                      : skin.image || "/placeholder.svg"
                                  }
                                  alt={skin.name}
                                  width={64}
                                  height={64}
                                  className="w-full h-full object-cover pointer-events-none transition-all duration-500 ease-out rounded-full"
                                  draggable={false}
                                  style={{
                                    userSelect: 'none',
                                    WebkitUserSelect: 'none'
                                  }}
                                />
                              </div>

                              {/* Rarity Icon Overlay - Centered at bottom edge */}
                              {rarityIcon && (
                                <ShadcnTooltip>
                                  <TooltipTrigger asChild>
                                    <div
                                      className={`absolute left-1/2 pointer-events-auto transition-opacity duration-400 cursor-help ${
                                        isAnimating ? 'opacity-80' : 'opacity-100'
                                      }`}
                                      style={{
                                        zIndex: 10,
                                        bottom: '-2px', // Position the center of the icon at the bottom edge
                                        transform: 'translateX(-50%)'
                                      }}
                                    >
                                      <Image
                                        src={rarityIcon}
                                        alt={`${skin.rarity} rarity`}
                                        width={16}
                                        height={16}
                                        className="w-4 h-4 drop-shadow-lg"
                                        draggable={false}
                                      />
                                    </div>
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <p>{getRarityDescription(skin.rarity)}</p>
                                  </TooltipContent>
                                </ShadcnTooltip>
                              )}
                            </button>
                          </div>
                        )
                      })}
                    </div>
                  </div>
                </div>

                {/* Right Arrow - Circular and aligned with splash art */}
                <button
                  onClick={scrollRight}
                  disabled={!canScrollRight}
                  className={`flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center transition-all duration-200 relative border-2 ${
                    canScrollRight
                      ? "bg-orange-600/80 hover:bg-orange-500/90 text-white shadow-lg backdrop-blur-sm border-orange-500/60 hover:border-orange-400"
                      : "bg-gray-700/50 text-gray-500 cursor-not-allowed border-gray-600/50"
                  }`}
                  style={{ zIndex: 150 }}
                >
                  <ChevronRight className="h-5 w-5" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Splash Art Modal */}
      {showSplashModal && (
        <div
          className="fixed inset-0 z-[9999] flex items-center justify-center p-8"
          style={{ backdropFilter: 'blur(8px)', backgroundColor: 'rgba(0, 0, 0, 0.8)' }}
          onClick={() => setShowSplashModal(false)}
        >
          <div
            className="relative max-w-6xl max-h-full"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Close Button - Enhanced visibility */}
            <button
              onClick={(e) => {
                e.stopPropagation()
                setShowSplashModal(false)
              }}
              className="absolute -top-4 -right-4 z-[10000] w-12 h-12 bg-black/90 hover:bg-red-600/80 text-white rounded-full flex items-center justify-center transition-all duration-200 shadow-2xl border-2 border-white/20 hover:border-red-400/50 backdrop-blur-sm"
              title="Close (ESC)"
            >
              <X className="h-6 w-6" strokeWidth={2.5} />
            </button>

            {/* Full Size Image */}
            <div className="rounded-lg overflow-hidden shadow-2xl animate-in zoom-in-95 duration-300">
              {(showSplashArt || !canShowInGame) ? (
                // Splash Art Mode
                <Image
                  src={getCurrentImageUrl() || "/placeholder.svg"}
                  alt={currentSkin.name}
                  width={1200}
                  height={700}
                  className="w-full h-auto object-contain max-h-[80vh]"
                  priority
                />
              ) : (
                // In-Game Mode with Background
                <div className="relative w-full max-h-[80vh]">
                  {/* Background Image - Larger to match splash art zoom */}
                  <Image
                    src="https://i.ibb.co/z91Cdg3/image.png"
                    alt="In-game background"
                    width={1200}
                    height={700}
                    priority
                  />
                  {/* In-Game Model Centered Over Background - 75% smaller */}
                  <div className="absolute inset-0 flex items-center justify-center">
                    <Image
                      src={getCurrentImageUrl() || "/placeholder.svg"}
                      alt={
                        selectedChromaId && currentSkin.chromas?.length > 0
                          ? `${currentSkin.name} - ${currentSkin.chromas.find((c: ChromaData) => c.id === selectedChromaId)?.name || 'Chroma'}`
                          : currentSkin.name
                      }
                      width={600}
                      height={350}
                      className="max-w-[75%] max-h-[75%] object-contain"
                    />
                  </div>
                </div>
              )}
            </div>

            {/* Image Info */}
            <div className="absolute bottom-4 left-4 right-4 bg-black/90 backdrop-blur-md rounded-lg p-4 border border-orange-500/40 shadow-xl">
              <h3 className="text-xl font-semibold text-white drop-shadow-md">{currentSkin.name}</h3>
              <div className="flex items-center justify-between">
                <p className="text-gray-300 text-sm">Click outside or press ESC to close</p>
                <span className="text-xs px-2 py-1 rounded bg-orange-600/80 text-orange-100">
                  {(showSplashArt || !canShowInGame) ? 'Splash Art' : 'In-Game'}
                </span>
              </div>
            </div>
          </div>
        </div>
      )}

      <h2 className="text-2xl font-bold text-white mb-8 drop-shadow-md text-center">
        Champion Overview
      </h2>

      {/* Champion Stats */}
      <div className="mb-8 grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Base Stats */}
        <Card className="bg-black/70 backdrop-blur-md border-orange-700/40 shadow-xl">
          <CardContent className="p-6">
            <h3 className="text-xl font-semibold text-white mb-4 drop-shadow-md">Base Stats</h3>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <div className="flex items-center space-x-2 text-sm text-gray-400 mb-1">
                    <Image
                      src="https://raw.communitydragon.org/latest/game/assets/perks/statmods/statmodshealthscalingicon.png"
                      alt="Health"
                      width={20}
                      height={20}
                      className="w-5 h-5"
                    />
                    <span>Health</span>
                  </div>
                  <div className="text-lg font-semibold text-white drop-shadow-md">{championStats.health}</div>
                </div>
                <div>
                  <div className="flex items-center space-x-2 text-sm text-gray-400 mb-1">
                    <Image
                      src="https://i.ibb.co/0VFpT40y/Frame-3-1.png"
                      alt="Mana"
                      width={20}
                      height={20}
                      className="w-5 h-5"
                    />
                    <span>Mana</span>
                  </div>
                  <div className="text-lg font-semibold text-white drop-shadow-md">{championStats.mana}</div>
                </div>
                <div>
                  <div className="flex items-center space-x-2 text-sm text-gray-400 mb-1">
                    <Image
                      src="https://raw.communitydragon.org/latest/game/assets/perks/statmods/statmodsattackdamageicon.png"
                      alt="Attack Damage"
                      width={20}
                      height={20}
                      className="w-5 h-5"
                    />
                    <span>Attack Damage</span>
                  </div>
                  <div className="text-lg font-semibold text-white drop-shadow-md">
                    {championStats.attackDamage}
                  </div>
                </div>
                <div>
                  <div className="flex items-center space-x-2 text-sm text-gray-400 mb-1">
                    <Image
                      src="https://raw.communitydragon.org/latest/game/assets/perks/statmods/statmodsattackspeedicon.png"
                      alt="Attack Speed"
                      width={20}
                      height={20}
                      className="w-5 h-5"
                    />
                    <span>Attack Speed</span>
                  </div>
                  <div className="text-lg font-semibold text-white drop-shadow-md">{championStats.attackSpeed}</div>
                </div>
                <div>
                  <div className="flex items-center space-x-2 text-sm text-gray-400 mb-1">
                    <Image
                      src="https://raw.communitydragon.org/latest/game/assets/perks/statmods/statmodsarmoricon.png"
                      alt="Armor"
                      width={20}
                      height={20}
                      className="w-5 h-5"
                    />
                    <span>Armor</span>
                  </div>
                  <div className="text-lg font-semibold text-white drop-shadow-md">{championStats.armor}</div>
                </div>
                <div>
                  <div className="flex items-center space-x-2 text-sm text-gray-400 mb-1">
                    <Image
                      src="https://raw.communitydragon.org/latest/game/assets/perks/statmods/statmodsmagicresicon.png"
                      alt="Magic Resist"
                      width={20}
                      height={20}
                      className="w-5 h-5"
                    />
                    <span>Magic Resist</span>
                  </div>
                  <div className="text-lg font-semibold text-white drop-shadow-md">{championStats.magicResist}</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Champion Ratings */}
        <Card className="bg-black/70 backdrop-blur-md border-orange-700/40 shadow-xl">
          <CardContent className="p-6">
            <h3 className="text-xl font-semibold text-white mb-4 drop-shadow-md">Champion Ratings</h3>
            <div className="space-y-4">

              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span className="text-gray-400">Attack</span>
                  <span className="text-white font-medium">{championStats.attack}/10</span>
                </div>
                <Progress value={championStats.attack * 10} className="h-2" />
              </div>
              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span className="text-gray-400">Defense</span>
                  <span className="text-white font-medium">{championStats.defense}/10</span>
                </div>
                <Progress value={championStats.defense * 10} className="h-2" />
              </div>
              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span className="text-gray-400">Magic</span>
                  <span className="text-white font-medium">{championStats.magic}/10</span>
                </div>
                <Progress value={championStats.magic * 10} className="h-2" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Abilities Section */}
      <Card className="bg-black/70 backdrop-blur-md border-orange-700/40 shadow-xl mb-8">
        <CardContent className="p-6">
          <h3 className="text-xl font-semibold text-white mb-6 drop-shadow-md">Abilities</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {abilities.map((ability: any, index: number) => (
              <div key={index} className="flex flex-col h-full">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="w-12 h-12 rounded-lg overflow-hidden bg-gray-800/50 border border-orange-500/40 flex items-center justify-center">
                    <Image
                      src={ability.iconImage || "/placeholder.svg"}
                      alt={ability.name}
                      width={48}
                      height={48}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div>
                    <div className="flex items-center space-x-2">
                      {ability.key && (
                        <span className="text-xs bg-orange-600/80 text-white px-2 py-1 rounded font-medium">
                          {ability.key}
                        </span>
                      )}
                      {ability.isPassive && (
                        <span className="text-xs bg-blue-600/80 text-white px-2 py-1 rounded font-medium">
                          PASSIVE
                        </span>
                      )}
                    </div>
                    <h4 className="text-lg font-semibold text-white drop-shadow-md">{ability.name}</h4>
                  </div>
                </div>
                <p className="text-gray-300 text-sm leading-relaxed flex-grow mb-3">{ability.description}</p>
                {!ability.isPassive && (ability.cooldown || ability.cost) && (
                  <div className="flex justify-between items-center text-xs mt-auto">
                    <span className="text-blue-300 font-medium">
                      {ability.cooldown ? `Cooldown: ${ability.cooldown}s` : ''}
                    </span>
                    <span className="text-purple-300 font-medium">
                      Cost: {ability.cost || 'No Cost'}
                    </span>
                  </div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Champion Lore */}
      <Card className="bg-black/70 backdrop-blur-md border-orange-700/40 shadow-xl">
        <CardContent className="p-6">
          <h3 className="text-xl font-semibold text-white mb-4 drop-shadow-md">Champion Lore</h3>
          <p className="text-gray-300 leading-relaxed">{lore}</p>
        </CardContent>
      </Card>
    </div>
  )
}
