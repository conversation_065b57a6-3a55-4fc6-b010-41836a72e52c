import { useState, useEffect, useCallback } from 'react'
import { ProcessedChromaData } from '@/lib/types/league-api'

interface UseChromasReturn {
  chromas: ProcessedChromaData[]
  loading: boolean
  error: string | null
  refetch: () => void
  totalCount: number
}

export function useChromas(): UseChromasReturn {
  const [chromas, setChromas] = useState<ProcessedChromaData[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [totalCount, setTotalCount] = useState(0)

  const fetchChromas = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/chromas/all')
      if (!response.ok) {
        throw new Error('Failed to fetch chromas')
      }

      const result = await response.json()
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch chromas')
      }

      setChromas(result.data)
      setTotalCount(result.count)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
      setError(errorMessage)
      console.error('❌ Failed to fetch chromas:', errorMessage)
    } finally {
      setLoading(false)
    }
  }, [])

  useEffect(() => {
    fetchChromas()
  }, [fetchChromas])

  return {
    chromas,
    loading,
    error,
    refetch: fetchChromas,
    totalCount
  }
}

interface UseChromaSearchReturn {
  chromas: ProcessedChromaData[]
  loading: boolean
  error: string | null
  search: (query: string, champion?: string, skinId?: number) => void
  clear: () => void
}

export function useChromaSearch(): UseChromaSearchReturn {
  const [chromas, setChromas] = useState<ProcessedChromaData[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const search = useCallback(async (query: string, champion?: string, skinId?: number) => {
    try {
      setLoading(true)
      setError(null)

      const params = new URLSearchParams()
      if (query) params.append('q', query)
      if (champion) params.append('champion', champion)
      if (skinId) params.append('skinId', skinId.toString())

      const response = await fetch(`/api/chromas/search?${params.toString()}`)
      if (!response.ok) {
        throw new Error('Failed to search chromas')
      }

      const result = await response.json()
      if (!result.success) {
        throw new Error(result.error || 'Failed to search chromas')
      }

      setChromas(result.data)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
      setError(errorMessage)
      console.error('❌ Failed to search chromas:', errorMessage)
    } finally {
      setLoading(false)
    }
  }, [])

  const clear = useCallback(() => {
    setChromas([])
    setError(null)
  }, [])

  return {
    chromas,
    loading,
    error,
    search,
    clear
  }
}

interface UsePaginatedChromasReturn {
  chromas: ProcessedChromaData[]
  loading: boolean
  error: string | null
  loadMore: () => void
  hasMore: boolean
  totalCount: number
  currentPage: number
}

export function usePaginatedChromas(limit: number = 50): UsePaginatedChromasReturn {
  const [chromas, setChromas] = useState<ProcessedChromaData[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [hasMore, setHasMore] = useState(true)
  const [totalCount, setTotalCount] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)

  const fetchPage = useCallback(async (page: number, append: boolean = false) => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`/api/chromas/paginated?page=${page}&limit=${limit}`)
      if (!response.ok) {
        throw new Error('Failed to fetch chromas')
      }

      const result = await response.json()
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch chromas')
      }

      if (append) {
        setChromas(prev => [...prev, ...result.data])
      } else {
        setChromas(result.data)
      }

      setHasMore(result.pagination.hasMore)
      setTotalCount(result.pagination.total)
      setCurrentPage(result.pagination.currentPage)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
      setError(errorMessage)
      console.error('❌ Failed to fetch paginated chromas:', errorMessage)
    } finally {
      setLoading(false)
    }
  }, [limit])

  const loadMore = useCallback(() => {
    if (hasMore && !loading) {
      fetchPage(currentPage + 1, true)
    }
  }, [hasMore, loading, currentPage, fetchPage])

  useEffect(() => {
    fetchPage(1)
  }, [fetchPage])

  return {
    chromas,
    loading,
    error,
    loadMore,
    hasMore,
    totalCount,
    currentPage
  }
}
