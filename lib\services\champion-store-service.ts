interface ChampionStoreData {
  itemId: number
  inventoryType: string
  iconUrl: string
  ownedQuantity: number
  maxQuantity: number
  rp: number
  ip: number // Blue Essence
  releaseDate: number // Timestamp
  name: string
  description: string
}

interface ChampionStoreCache {
  data: ChampionStoreData[]
  timestamp: number
}

class ChampionStoreService {
  private cache: ChampionStoreCache | null = null
  private readonly CACHE_TTL = 1000 * 60 * 60 * 24 // 24 hours
  private readonly API_URL = 'https://api.loldb.info/api/store/champions'

  /**
   * Get champion store data by champion name
   */
  async getChampionStoreData(championName: string): Promise<ChampionStoreData | null> {
    const allData = await this.getAllChampionStoreData()
    return allData.find(champion => 
      champion.name.toLowerCase() === championName.toLowerCase()
    ) || null
  }

  /**
   * Get champion store data by champion ID (itemId)
   */
  async getChampionStoreDataById(championId: number): Promise<ChampionStoreData | null> {
    const allData = await this.getAllChampionStoreData()
    return allData.find(champion => champion.itemId === championId) || null
  }

  /**
   * Get all champion store data
   */
  async getAllChampionStoreData(): Promise<ChampionStoreData[]> {
    // Check cache first
    if (this.cache && this.isCacheValid()) {
      
      return this.cache.data
    }

    try {
      
      const response = await fetch(this.API_URL)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: Failed to fetch champion store data`)
      }

      const rawData = await response.json()
      
      
      

      // Handle different response structures
      let data: ChampionStoreData[]
      if (Array.isArray(rawData)) {
        data = rawData
      } else if (rawData.data && rawData.data.catalog && Array.isArray(rawData.data.catalog)) {
        data = rawData.data.catalog
      } else if (rawData.data && Array.isArray(rawData.data)) {
        data = rawData.data
      } else if (rawData.champions && Array.isArray(rawData.champions)) {
        data = rawData.champions
      } else {
        console.error('❌ Unexpected API response structure:', rawData)
        throw new Error('Unexpected API response structure')
      }

      // Cache the data
      this.cache = {
        data,
        timestamp: Date.now()
      }

      
      return data

    } catch (error) {
      console.error('❌ Failed to fetch champion store data:', error)
      
      // Return cached data if available, even if expired
      if (this.cache) {
        console.warn('⚠️ Using expired cache due to API failure')
        return this.cache.data
      }
      
      // Return empty array as fallback
      return []
    }
  }

  /**
   * Format release date from timestamp to readable format
   */
  formatReleaseDate(timestamp: number): string {
    try {
      const date = new Date(timestamp)
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    } catch (error) {
      console.warn('⚠️ Failed to format release date:', error)
      return 'Unknown'
    }
  }

  /**
   * Get champion pricing information
   */
  async getChampionPricing(championName: string): Promise<{
    costBE: number
    costRP: number
    releaseDate: string
  }> {
    try {
      
      const storeData = await this.getChampionStoreData(championName)

      if (!storeData) {
        console.warn(`⚠️ STORE SERVICE: No store data found for champion: ${championName}`)
        return {
          costBE: 6300, // Default fallback
          costRP: 975,  // Default fallback
          releaseDate: 'Unknown'
        }
      }

      
      return {
        costBE: storeData.ip,
        costRP: storeData.rp,
        releaseDate: this.formatReleaseDate(storeData.releaseDate)
      }
    } catch (error) {
      console.error(`❌ STORE SERVICE: Error getting pricing for ${championName}:`, error)
      return {
        costBE: 6300, // Default fallback
        costRP: 975,  // Default fallback
        releaseDate: 'Unknown'
      }
    }
  }

  /**
   * Check if cache is still valid
   */
  private isCacheValid(): boolean {
    if (!this.cache) return false
    return Date.now() - this.cache.timestamp < this.CACHE_TTL
  }

  /**
   * Clear the cache
   */
  clearCache(): void {
    this.cache = null
    
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): {
    cached: boolean
    cacheAge: number
    championCount: number
  } {
    return {
      cached: !!this.cache,
      cacheAge: this.cache ? Date.now() - this.cache.timestamp : 0,
      championCount: this.cache?.data.length || 0
    }
  }
}

// Export singleton instance
export const championStoreService = new ChampionStoreService()
export type { ChampionStoreData }

