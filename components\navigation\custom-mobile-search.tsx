"use client"

import { useState, useEffect, useRef } from "react"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { DidYouMean } from "@/components/ui/did-you-mean"
import { Search, X, ChevronRight } from "lucide-react"
import { useMobileSearch } from "@/hooks/use-search"

interface CustomMobileSearchProps {
  onSearch?: (query: string) => void
}

export default function CustomMobileSearch({ onSearch }: CustomMobileSearchProps) {
  const search = useMobileSearch()
  const [isAnimating, setIsAnimating] = useState(false)
  const inputRef = useRef<HTMLInputElement>(null)



  const getTagColor = (tag: string) => {
    switch (tag) {
      case "Champions":
        return "bg-blue-600/20 text-blue-300 border-blue-500/30"
      case "Items":
        return "bg-green-600/20 text-green-300 border-green-500/30"
      case "Skins":
        return "bg-purple-600/20 text-purple-300 border-purple-500/30"
      case "Chromas":
        return "bg-pink-600/20 text-pink-300 border-pink-500/30"
      default:
        return "bg-gray-600/20 text-gray-300 border-gray-500/30"
    }
  }

  const handleOpenSearch = () => {
    setIsAnimating(true)
    search.openSearch()
    // Focus input after animation
    setTimeout(() => {
      inputRef.current?.focus()
    }, 300)
  }

  const handleCloseSearch = () => {
    setIsAnimating(false)
    search.closeSearch()
  }

  const handleSearch = () => {
    if (onSearch) {
      onSearch(search.searchQuery)
    }
    handleCloseSearch()
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch()
    }
    if (e.key === 'Escape') {
      handleCloseSearch()
    }
  }

  // Prevent body scroll when search is open
  useEffect(() => {
    if (search.isOpen) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }

    return () => {
      document.body.style.overflow = 'unset'
    }
  }, [search.isOpen])

  return (
    <>
      {/* Search Button - Always visible for testing */}
      <Button
        variant="ghost"
        size="icon"
        className="text-white hover:bg-orange-600/20 transition-all duration-300"
        onClick={handleOpenSearch}
      >
        <Search className="h-5 w-5" />
        <span className="sr-only">Search</span>
      </Button>

      {/* Search Overlay */}
      {(search.isOpen || isAnimating) && (
        <>
          {/* Blur Background */}
          <div
            className={`fixed inset-0 bg-black/60 backdrop-blur-sm z-40 transition-all duration-300 ${
              search.isOpen ? 'opacity-100' : 'opacity-0'
            }`}
            onClick={handleCloseSearch}
          />

          {/* Search Panel - Covers about 42.5% of the screen */}
          <div
            className={`fixed top-0 left-0 right-0 bg-gray-950/95 backdrop-blur-md border-b border-orange-800/20 z-50 transition-all duration-300 ease-out ${
              search.isOpen ? 'translate-y-0 opacity-100' : '-translate-y-full opacity-0'
            }`}
            style={{ height: '42.5vh' }}
          >
            <div className="h-full flex flex-col p-6">
              {/* Header */}
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-white">Search</h2>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleCloseSearch}
                  className="text-gray-400 hover:text-white"
                >
                  <X className="h-5 w-5" />
                </Button>
              </div>

              {/* Search Input */}
              <div className="relative mb-6">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <Input
                  ref={inputRef}
                  type="text"
                  placeholder="Search champions, items, skins..."
                  value={search.searchQuery}
                  onChange={(e) => search.setSearchQuery(e.target.value)}
                  onKeyDown={handleKeyPress}
                  className="pl-12 pr-4 py-4 bg-gray-800/50 border-gray-600 text-white placeholder-gray-400 focus:border-orange-400 text-lg"
                />
              </div>

              {/* Search Results - Scrollable */}
              <div className="flex-1 overflow-hidden">
                {search.currentSuggestions.length > 0 && (
                  <div className="h-full overflow-y-auto space-y-3 pr-2">
                    {search.currentSuggestions.map((suggestion) => (
                      <div
                        key={`${suggestion.type}-${suggestion.id}`}
                        onClick={() => search.handleResultClick(suggestion.href)}
                        className="flex items-center p-4 hover:bg-gray-800/50 rounded-lg transition-all duration-150 group/mobile-item cursor-pointer"
                      >
                        <div className="w-12 h-12 mr-4 rounded-lg overflow-hidden bg-gray-800/50 flex-shrink-0">
                          <Image
                            src={suggestion.image}
                            alt={suggestion.name}
                            width={48}
                            height={48}
                            className="w-full h-full object-cover group-hover/mobile-item:scale-105 transition-transform duration-200"
                          />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="text-white font-medium text-base mb-1 group-hover/mobile-item:text-orange-300 transition-colors">
                            {suggestion.name}
                          </div>
                          <div className="flex items-center">
                            <span
                              className={`inline-flex items-center px-2 py-1 rounded-md text-sm font-medium border ${getTagColor(suggestion.tag)}`}
                            >
                              {suggestion.tag}
                            </span>
                          </div>
                        </div>
                        <ChevronRight className="h-5 w-5 text-gray-500 transition-all duration-150 flex-shrink-0 group-hover/mobile-item:text-orange-400 group-hover/mobile-item:translate-x-1" />
                      </div>
                    ))}
                  </div>
                )}

                {search.loading && (
                  <div className="flex items-center justify-center h-full">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-400"></div>
                  </div>
                )}

                {!search.loading && search.currentSuggestions.length === 0 && search.searchQuery.trim() && (
                  <div className="h-full">
                    {search.showSuggestions ? (
                      <div className="p-4">
                        <div className="flex items-center justify-center mb-6">
                          <p className="text-gray-400 text-sm">No exact matches found</p>
                        </div>
                        <DidYouMean
                          suggestions={search.searchSuggestions}
                          onSuggestionClick={search.handleSuggestionClick}
                          variant="orange"
                        />
                      </div>
                    ) : (
                      <div className="flex items-center justify-center h-full">
                        <p className="text-gray-400 text-lg">No results found</p>
                      </div>
                    )}
                  </div>
                )}

                {!search.loading && search.currentSuggestions.length === 0 && !search.searchQuery.trim() && (
                  <div className="flex items-center justify-center h-full">
                    <p className="text-gray-400 text-lg">Start typing to search...</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </>
      )}
    </>
  )
}
