"use client"

import * as React from "react"
import { Check, ChevronsUpDown } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

interface ChampionsComboboxProps {
  selectedChampions: string[]
  onSelectionChange: (champions: string[]) => void
  availableChampions: string[]
  placeholder?: string
  className?: string
}

export function ChampionsCombobox({
  selectedChampions,
  onSelectionChange,
  availableChampions,
  placeholder = "Select champions...",
  className
}: ChampionsComboboxProps) {
  const [open, setOpen] = React.useState(false)

  const handleSelect = (championName: string) => {
    const isSelected = selectedChampions.includes(championName)
    
    if (isSelected) {
      // Remove champion if already selected
      onSelectionChange(selectedChampions.filter(c => c !== championName))
    } else {
      // Add champion if not selected
      onSelectionChange([...selectedChampions, championName])
    }
  }

  const getDisplayText = () => {
    if (selectedChampions.length === 0) {
      return placeholder
    } else if (selectedChampions.length === 1) {
      return selectedChampions[0]
    } else {
      return `${selectedChampions[0]} +${selectedChampions.length - 1}`
    }
  }

  return (
    <div className={className}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between bg-gray-900/40 border-gray-700/20 text-white hover:bg-gray-800/50 h-[40px]"
          >
            <span className="truncate text-left">
              {getDisplayText()}
            </span>
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0 bg-gray-900 border-gray-700">
          <Command>
            <CommandInput 
              placeholder="Search champions..." 
              className="h-9 bg-gray-900 border-gray-700 text-white placeholder-gray-400"
            />
            <CommandList className="max-h-[300px]">
              <CommandEmpty className="text-gray-400 py-6 text-center text-sm">
                No champion found.
              </CommandEmpty>
              <CommandGroup>
                {availableChampions.map((champion) => (
                  <CommandItem
                    key={champion}
                    value={champion}
                    onSelect={() => handleSelect(champion)}
                    className="text-white hover:bg-purple-400/10 cursor-pointer"
                  >
                    <span className="flex-1">{champion}</span>
                    <Check
                      className={cn(
                        "ml-auto h-4 w-4",
                        selectedChampions.includes(champion) ? "opacity-100" : "opacity-0"
                      )}
                    />
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  )
}
