import { useState, useEffect } from 'react'
import { mythicCostService, MythicCostData } from '@/lib/services/mythic-cost-service'
import { ProcessedSkinData } from '@/lib/services/skin-service'

export interface UseMythicPricesReturn {
  mythicPrices: Map<string, MythicCostData | null>
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
}

/**
 * Hook to fetch mythic prices for skins page
 * Only fetches prices for mythic skins
 */
export function useMythicPrices(skins: ProcessedSkinData[]): UseMythicPricesReturn {
  const [mythicPrices, setMythicPrices] = useState<Map<string, MythicCostData | null>>(new Map())
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchMythicPrices = async () => {
    try {
      setLoading(true)
      setError(null)

      // Filter and prepare mythic skins data
      const mythicSkinsData = skins
        .filter(skin => skin.tier === 'Mythic' && skin.contentId)
        .map(skin => ({
          contentId: skin.contentId,
          tier: skin.tier
        }))


      if (mythicSkinsData.length === 0) {
        setMythicPrices(new Map())
        return
      }

      const prices = await mythicCostService.getBulkMythicPrices(mythicSkinsData)
      setMythicPrices(prices)


    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch mythic prices'
      setError(errorMessage)
      console.error('❌ Failed to fetch mythic prices:', errorMessage)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (skins.length > 0) {
      fetchMythicPrices()
    }
  }, [skins])

  return {
    mythicPrices,
    loading,
    error,
    refetch: fetchMythicPrices
  }
}
