/**
 * Discount Card Component - Uses champion card styling for consistency
 */

"use client"

import Image from "next/image"
import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ProcessedDiscountItem } from "@/lib/services/sales-service"
import { formatPrice } from "@/lib/utils/discount-utils"

interface DiscountCardProps {
  discount: ProcessedDiscountItem
  className?: string
}

// Function to create URL-friendly skin names (same as in skin pages)
function createSkinSlug(skinName: string): string {
  return skinName
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters except spaces and hyphens
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .replace(/^-|-$/g, '') // Remove leading/trailing hyphens
}

export default function DiscountCard({ discount, className = "" }: DiscountCardProps) {

  // Determine the display name and link
  const displayName = discount.skinName || discount.championName
  const linkHref = discount.type === 'CHAMPION_SKIN' && discount.skinName
    ? `/skins/${createSkinSlug(discount.skinName)}`
    : `/champions/${discount.championSlug}`

  return (
    <Link href={linkHref}>
      <Card className={`bg-gray-900/40 border-amber-700/20 hover:border-amber-400/50 hover:-translate-y-1 sm:hover:-translate-y-2 transition-all duration-300 group cursor-pointer w-full min-w-[140px] sm:min-w-[160px] max-w-[160px] sm:max-w-[280px] aspect-[2/3] flex flex-col ${className}`}>
        <CardContent className="p-0 flex flex-col h-full relative overflow-hidden rounded-lg">
          {/* Sale Badge with Community Dragon Image */}
          <div className="absolute top-2 right-2 sm:top-3 sm:right-3 z-20">
            <div className="relative">
              <Image
                src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-store/global/default/storefront/addon/public/img/sprite-source/lcu-sale.png"
                alt="Sale"
                width={40}
                height={20}
              />
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-white font-medium text-xs drop-shadow-lg">
                  -{discount.discount}%
                </span>
              </div>
            </div>
          </div>

          {/* Item Image - Full Card Background */}
          <div className="absolute inset-0 w-full h-full">
            <Image
              src={discount.image}
              alt={displayName}
              width={300}
              height={560}
              className="w-full h-full object-cover object-top scale-110 group-hover:scale-125 transition-transform duration-300"
              onError={(e) => {
                const target = e.target as HTMLImageElement
                target.src = '/placeholder.svg'
              }}
            />
          </div>

          {/* Gradient Overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent z-5" />

          {/* Item Info */}
          <div className="absolute bottom-0 left-0 right-0 py-4 px-2 z-10">
            {/* Item Name */}
            <h3
              className="text-white font-semibold text-sm sm:text-base md:text-lg group-hover:text-amber-400 transition-colors mb-0"
              style={{ textShadow: '3px 3px 6px rgba(0,0,0,1), 1px 1px 3px rgba(0,0,0,0.8)' }}
            >
              {displayName}
            </h3>

            {/* Pricing - Sale Price Centered, Original Price Adjacent */}
            <div className="relative flex items-center justify-center mt-2">
              {/* Sale Price with Currency Icon - Perfectly Centered */}
              <div className="flex items-center space-x-1">
                {/* Currency Icon */}
                {discount.currency === 'RP' && (
                  <Image
                    src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg"
                    alt="RP"
                    width={16}
                    height={16}
                    className="w-4 h-4 sm:w-5 sm:h-5"
                  />
                )}
                {discount.currency === 'ME' && (
                  <Image
                    src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/mythic.svg"
                    alt="ME"
                    width={16}
                    height={16}
                    className="w-4 h-4 sm:w-5 sm:h-5"
                  />
                )}
                {discount.currency === 'AS' && (
                  <Image
                    src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/currency/icons/ancient_spark.svg"
                    alt="AS"
                    width={16}
                    height={16}
                    className="w-4 h-4 sm:w-5 sm:h-5"
                  />
                )}
                {discount.currency === 'BE' && (
                  <Image
                    src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-navigation/global/default/rewardicons_blueessence.png"
                    alt="BE"
                    width={16}
                    height={16}
                    className="w-4 h-4 sm:w-5 sm:h-5"
                  />
                )}
                <span
                  className={`font-bold text-sm sm:text-base ${
                    discount.currency === 'AS' || discount.currency === 'BE' ? '' : 'text-amber-400'
                  }`}
                  style={{
                    textShadow: '2px 2px 4px rgba(0,0,0,1)',
                    color: discount.currency === 'AS' ? '#2babf0' :
                           discount.currency === 'BE' ? '#38c4dc' : undefined
                  }}
                >
                  {discount.salePrice}
                </span>
              </div>

              {/* Original Price - Strikethrough - Fixed position from right with padding */}
              <span
                className="absolute right-2 text-gray-400 line-through text-[12px]"
                style={{ textShadow: '2px 2px 4px rgba(0,0,0,1)' }}
              >
                {discount.originalPrice}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>
    </Link>
  )
}

/**
 * Discount Card Skeleton for loading states
 */
export function DiscountCardSkeleton({ className = "" }: { className?: string }) {
  return (
    <Card className={`bg-gray-900/40 border-amber-700/20 w-full min-w-[140px] sm:min-w-[160px] max-w-[160px] sm:max-w-[280px] aspect-[2/3] flex flex-col ${className}`}>
      <CardContent className="p-0 flex flex-col h-full relative overflow-hidden rounded-lg">
        {/* Image Skeleton */}
        <div className="absolute inset-0 w-full h-full bg-gray-800 animate-pulse" />
        
        {/* Badge Skeleton */}
        <div className="absolute top-2 right-2 sm:top-3 sm:right-3 z-20">
          <div className="bg-gray-700 rounded px-2 py-1 w-12 h-6 animate-pulse" />
        </div>

        {/* Content Skeleton */}
        <div className="absolute bottom-0 left-0 right-0 py-4 px-2 z-10">
          <div className="bg-gray-700 rounded h-4 sm:h-5 w-3/4 mb-0 animate-pulse" />
          <div className="flex items-center space-x-2 mt-2">
            <div className="bg-gray-700 rounded h-3 w-12 animate-pulse" />
            <div className="flex items-center space-x-1">
              <div className="bg-gray-700 rounded-full w-4 h-4 animate-pulse" />
              <div className="bg-gray-700 rounded h-4 w-16 animate-pulse" />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
