"use client"

import { Input } from "@/components/ui/input"
import { DidYouMean } from "@/components/ui/did-you-mean"
import { Search } from "lucide-react"

export interface SearchWithSuggestionsProps {
  /** Current search query */
  searchQuery: string
  /** Function called when search query changes */
  onSearchChange: (query: string) => void
  /** Array of suggestions to display */
  suggestions: string[]
  /** Function called when a suggestion is clicked */
  onSuggestionClick: (suggestion: string) => void
  /** Whether to show suggestions */
  showSuggestions: boolean
  /** Placeholder text for search input */
  placeholder?: string
  /** Additional CSS classes for the container */
  className?: string
  /** Additional CSS classes for the search input */
  inputClassName?: string
  /** Additional CSS classes for the suggestions */
  suggestionsClassName?: string
  /** Whether to show the search icon */
  showSearchIcon?: boolean
  /** Whether the search is currently loading/debouncing */
  isSearching?: boolean
  /** Color variant for suggestions */
  variant?: 'orange' | 'purple' | 'blue' | 'green' | 'amber'
}

/**
 * Reusable search component with "Did You Mean?" suggestions
 * Combines search input and suggestion display in one component
 */
export function SearchWithSuggestions({
  searchQuery,
  onSearchChange,
  suggestions,
  onSuggestionClick,
  showSuggestions,
  placeholder = "Search...",
  className = "",
  inputClassName = "",
  suggestionsClassName = "",
  showSearchIcon = true,
  isSearching = false,
  variant = 'orange'
}: SearchWithSuggestionsProps) {
  return (
    <div className={`space-y-4 ${className}`}>
      {/* Search Input */}
      <div className="relative">
        {showSearchIcon && (
          <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
        )}
        <Input
          type="text"
          placeholder={placeholder}
          value={searchQuery}
          onChange={(e) => onSearchChange(e.target.value)}
          className={`${showSearchIcon ? 'pl-12' : ''} bg-gray-900/80 border-gray-700/50 text-white placeholder-gray-400 focus:border-purple-400/60 ${inputClassName}`}
        />
        {isSearching && (
          <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
            <div className="animate-spin h-4 w-4 border-2 border-gray-400 border-t-transparent rounded-full"></div>
          </div>
        )}
      </div>

      {/* Did You Mean? Suggestions */}
      {showSuggestions && suggestions.length > 0 && (
        <DidYouMean
          suggestions={suggestions}
          onSuggestionClick={onSuggestionClick}
          variant={variant}
          className={suggestionsClassName}
        />
      )}
    </div>
  )
}

/**
 * Simple search input without suggestions (for cases where you only need the input)
 */
export function SearchInput({
  searchQuery,
  onSearchChange,
  placeholder = "Search...",
  className = "",
  showSearchIcon = true,
  isSearching = false
}: Pick<SearchWithSuggestionsProps, 'searchQuery' | 'onSearchChange' | 'placeholder' | 'className' | 'showSearchIcon' | 'isSearching'>) {
  return (
    <div className={`relative ${className}`}>
      {showSearchIcon && (
        <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
      )}
      <Input
        type="text"
        placeholder={placeholder}
        value={searchQuery}
        onChange={(e) => onSearchChange(e.target.value)}
        className={`${showSearchIcon ? 'pl-12' : ''} bg-gray-900/80 border-gray-700/50 text-white placeholder-gray-400 focus:border-purple-400/60`}
      />
      {isSearching && (
        <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
          <div className="animate-spin h-4 w-4 border-2 border-gray-400 border-t-transparent rounded-full"></div>
        </div>
      )}
    </div>
  )
}
