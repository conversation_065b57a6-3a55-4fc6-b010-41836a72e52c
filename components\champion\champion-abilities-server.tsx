import { Card, CardContent } from "@/components/ui/card"
import Image from "next/image"

interface ChampionAbilitiesServerProps {
  championData: any
}

export default function ChampionAbilitiesServer({ championData }: ChampionAbilitiesServerProps) {
  const { abilities } = championData

  return (
    <Card className="bg-black/70 backdrop-blur-md border-orange-700/40 shadow-xl mb-8">
      <CardContent className="p-6">
        <h3 className="text-xl font-semibold text-white mb-6">Abilities</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {abilities.map((ability: any, index: number) => (
            <div key={index} className="flex flex-col h-full">
              <div className="flex items-center space-x-3 mb-3">
                <div className="w-12 h-12 rounded-lg overflow-hidden bg-gray-800/50 border border-orange-500/40 flex items-center justify-center">
                  <Image
                    src={ability.iconImage}
                    alt={ability.name}
                    width={48}
                    height={48}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div>
                  <div className="flex items-center space-x-2">
                    {!ability.isPassive && (
                      <span className="text-orange-400 font-bold text-sm bg-orange-500/20 px-2 py-1 rounded">
                        {ability.key}
                      </span>
                    )}
                    <h4 className="font-semibold text-white text-sm">{ability.name}</h4>
                  </div>
                </div>
              </div>

              <p className="text-gray-300 text-sm leading-relaxed flex-grow mb-3">
                {ability.description}
              </p>

              {!ability.isPassive && (ability.cooldown || ability.cost) && (
                <div className="flex justify-between items-center text-xs mt-auto">
                  <span className="text-blue-300 font-medium">
                    {ability.cooldown ? `Cooldown: ${ability.cooldown}s` : ''}
                  </span>
                  <span className="text-purple-300 font-medium">
                    Cost: {ability.cost || 'No Cost'}
                  </span>
                </div>
              )}
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
