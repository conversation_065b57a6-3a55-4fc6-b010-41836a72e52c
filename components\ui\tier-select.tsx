"use client"

import * as React from "react"
import { Check, ChevronDown } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { SkinRarityIconsSimple } from "@/components/ui/skin-rarity-icons"

interface TierSelectProps {
  value: string
  onValueChange: (value: string) => void
  tiers: string[]
  placeholder?: string
  className?: string
  triggerClassName?: string
}

export function TierSelect({
  value,
  onValueChange,
  tiers,
  placeholder = "Select tier...",
  className,
  triggerClassName
}: TierSelectProps) {
  const [open, setOpen] = React.useState(false)

  const allTiers = ["All", ...tiers]

  const renderTierOption = (tier: string) => {
    if (tier === "All") {
      return <span>All</span>
    }

    return (
      <div className="flex items-center space-x-2">
        <div className="w-4 h-4 flex items-center justify-center">
          <SkinRarityIconsSimple
            rarity={tier}
            isLegacy={tier === "Legacy"}
            isBase={false}
            size={16}
          />
        </div>
        <span>{tier}</span>
      </div>
    )
  }

  const renderSelectedValue = () => {
    if (!value || value === "All") {
      return value || placeholder
    }

    return (
      <div className="flex items-center space-x-2">
        <div className="w-4 h-4 flex items-center justify-center">
          <SkinRarityIconsSimple
            rarity={value}
            isLegacy={value === "Legacy"}
            isBase={false}
            size={16}
          />
        </div>
        <span>{value}</span>
      </div>
    )
  }

  return (
    <div className={className}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className={cn(
              "w-full justify-between bg-gray-900/40 border-gray-700/20 text-white hover:bg-gray-800/50",
              triggerClassName
            )}
          >
            {renderSelectedValue()}
            <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0 bg-gray-900 border-gray-700" align="start">
          <Command className="bg-gray-900">
            <CommandList className="max-h-60">
              <CommandEmpty className="text-gray-400 py-6 text-center text-sm">
                No tiers found.
              </CommandEmpty>
              <CommandGroup>
                {allTiers.map((tier) => (
                  <CommandItem
                    key={tier}
                    value={tier}
                    onSelect={(currentValue) => {
                      onValueChange(currentValue === value ? "" : currentValue)
                      setOpen(false)
                    }}
                    className="text-white hover:bg-gray-800/50 cursor-pointer"
                  >
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4",
                        value === tier ? "opacity-100" : "opacity-0"
                      )}
                    />
                    {renderTierOption(tier)}
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  )
}
