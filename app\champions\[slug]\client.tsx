"use client";

import ChampionBackground from "@/components/champion-background";
import ChampionPageServer from "@/components/champion-page-server";
import SharedLayout from "@/components/shared-layout";

export default function ChampionSlugClient({ champion, championData, backgroundImage }: { champion: any, championData: any, backgroundImage: string }) {
  return (
    <ChampionBackground
      customImageUrl={backgroundImage}
      blur={10}
      brightness={0.25}
    >
      <SharedLayout>
        <ChampionPageServer champion={champion} championData={championData} />
      </SharedLayout>
    </ChampionBackground>
  );
} 