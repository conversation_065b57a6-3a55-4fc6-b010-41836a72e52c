"use client"

import { useState, useEffect, useMemo } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import Image from "next/image"
import SharedLayout from "@/components/shared-layout"
import SkinBackground from "@/components/skin-background"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { SkinRarityIconsSimple } from "@/components/ui/skin-rarity-icons"
import { ChromaRarityIcons, ChromaRarityIconsSimple } from "@/components/ui/chroma-rarity-icons"
import { ProcessedChromaData } from "@/lib/types/league-api"
import { getColorDisplayName, formatChromaPrice, getActualChromaColorName } from "@/lib/utils/chroma-utils"
import { useChromaMythicPrices } from "@/hooks/use-chroma-mythic-prices"
import { mythicCostService, MythicCostData } from "@/lib/services/mythic-cost-service"
import { parseChromaURL, createChromaURL } from "@/lib/utils/chroma-url-utils"
import { Bell, ExternalLink, ArrowLeft, Sparkles } from "lucide-react"
import Link from "next/link"
import { ChromaDetailSkeleton } from "@/components/skeletons/chroma-detail-skeleton"

// Rarity color mapping for chromas (based on skin tier)
const rarityColors: Record<string, string> = {
  'Regular': '#299645',
  'Legacy': '#c1a26a',
  'Epic': '#30cdb1',
  'Legendary': '#e81b23',
  'Mythic': '#EE00FF',
  'Ultimate': '#d7861f',
  'Exalted': '#d3ce5a',
  'Transcendent': '#cda4de'
}

function getRarityStyle(tier: string, isLegacy: boolean) {
  const color = isLegacy ? rarityColors['Legacy'] : rarityColors[tier] || rarityColors['Regular']
  return {
    borderColor: `${color}40`,
    color: color,
    backgroundColor: `${color}20`
  }
}



export default function ChromaDetailPage() {
  const params = useParams()
  const router = useRouter()
  const [chroma, setChroma] = useState<ProcessedChromaData | null>(null)
  const [relatedChromas, setRelatedChromas] = useState<ProcessedChromaData[]>([])
  const [totalChromasForSkin, setTotalChromasForSkin] = useState<number>(0)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [mythicCost, setMythicCost] = useState<MythicCostData | null>(null)
  const [mythicCostLoading, setMythicCostLoading] = useState(false)

  // Fetch mythic prices for related chromas
  const { mythicPrices } = useChromaMythicPrices(relatedChromas)

  // Extract chroma color for filtering
  const chromaColor = useMemo(() => {
    if (!chroma) return null
    return getActualChromaColorName(chroma.name)
  }, [chroma])

  // Function to fetch mythic cost data
  const fetchMythicCost = async (contentId: string) => {
    try {
      setMythicCostLoading(true)

      const mythicResponse = await mythicCostService.getMythicCost(contentId)

      if (mythicResponse.success && mythicResponse.data) {
        setMythicCost(mythicResponse.data)
      } else {
        setMythicCost(null)
      }

    } catch (error) {
      console.error(`❌ Error fetching mythic cost for chroma contentId ${contentId}:`, error)
      setMythicCost(null)
    } finally {
      setMythicCostLoading(false)
    }
  }

  useEffect(() => {
    const fetchChromaData = async () => {
      try {
        setLoading(true)
        setError(null)

        // Fetch all chromas to find the matching one
        const response = await fetch('/api/chromas/all')
        if (!response.ok) {
          throw new Error('Failed to fetch chromas data')
        }

        const result = await response.json()
        if (!result.success) {
          throw new Error(result.error || 'Failed to fetch chromas data')
        }

        const allChromas: ProcessedChromaData[] = result.data
        const slug = params.slug as string

        // Parse the slug to find the chroma
        const parsedSlug = parseChromaURL(slug)
        if (!parsedSlug) {
          throw new Error('Invalid chroma URL')
        }
        // Find the chroma that matches the slug components by regenerating URLs
        // This ensures we get the exact chroma that would generate this URL
        const foundChroma = allChromas.find(c => {
          try {
            const generatedUrl = createChromaURL(c)
            return generatedUrl === slug
          } catch (error) {
            return false
          }
        })

        
        if (!foundChroma) {
          throw new Error('Chroma not found')
        }

        setChroma(foundChroma)

        // Fetch mythic cost data if contentId is available (for unavailable chromas)
        if (foundChroma.contentId && foundChroma.availability !== 'Available') {
          fetchMythicCost(foundChroma.contentId)
        }

        // Find related chromas (same skin only)
        const related = allChromas
          .filter(c =>
            c.skinName === foundChroma.skinName &&
            c.id !== foundChroma.id
          )
          .slice(0, 6)
        setRelatedChromas(related)

        // Store total chroma count for this skin (including current chroma)
        const totalChromasCount = allChromas.filter(c => c.skinName === foundChroma.skinName).length
        setTotalChromasForSkin(totalChromasCount)

      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
        setError(errorMessage)
        console.error('❌ Failed to fetch chroma data:', errorMessage)
      } finally {
        setLoading(false)
      }
    }

    if (params.slug) {
      fetchChromaData()
    }
  }, [params.slug])

  if (loading) {
    return (
      <SharedLayout>
        <ChromaDetailSkeleton />
      </SharedLayout>
    )
  }

  if (error || !chroma) {
    return (
      <SharedLayout>
        <div className="container mx-auto px-8 py-2">
          <div className="flex items-center justify-center min-h-[60vh]">
            <div className="text-center">
              <div className="relative mx-auto mb-4 w-32 h-32">
                <Image
                  src="https://raw.communitydragon.org/latest/game/assets/loadouts/summoneremotes/champions/amumu/amumu_sad_crying_glow.png"
                  alt="Failed to load glow"
                  width={128}
                  height={128}
                  className="absolute inset-0 opacity-60"
                  unoptimized
                />
                <Image
                  src="https://raw.communitydragon.org/latest/game/assets/loadouts/summoneremotes/champions/amumu/amumu_sad_crying_vfx.png"
                  alt="Failed to load"
                  width={128}
                  height={128}
                  className="relative z-10"
                  unoptimized
                />
              </div>
              <p className="text-red-300 mb-4">Failed to load chroma</p>
              <p className="text-gray-400 mb-4">{error}</p>
              <Button
                variant="outline"
                onClick={() => router.push('/skins/chromas')}
                className="border-gray-700 text-gray-300"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Chromas
              </Button>
            </div>
          </div>
        </div>
      </SharedLayout>
    )
  }



  const rarityStyle = getRarityStyle(chroma.skinTier, chroma.availability === 'Legacy')

  // Get the color name for display, handling dual colors dynamically
  const getChromaColorName = (chroma: ProcessedChromaData, slug?: string): string => {
    // Check if this chroma would generate a dual-color URL
    // This matches the logic in createChromaURL function
    const hasNoPrice = !chroma.price || chroma.price === 'Legacy' || chroma.price === 'N/A' || chroma.availability === 'Legacy'
    const hasMultipleColors = chroma.colors && chroma.colors.length > 1

    if (hasNoPrice && hasMultipleColors && slug) {
      // Generate the expected dual-color name from the chroma's actual colors
      const colorNames = chroma.colors.slice(0, 2).map(color => getColorDisplayName(color).toLowerCase().trim())
      const uniqueColorNames = [...new Set(colorNames)] // Remove duplicates
      const expectedDualColorName = uniqueColorNames.join('-')

      // Check if the current URL contains this dual-color pattern
      if (slug.includes(expectedDualColorName)) {
        // Format for display: capitalize each part and join with /
        const formattedColors = uniqueColorNames.map(color =>
          color.charAt(0).toUpperCase() + color.slice(1)
        ).join('/')
        return formattedColors
      }
    }

    // Default to the actual chroma color name or display name
    return getActualChromaColorName(chroma.name) || getColorDisplayName(chroma.color)
  }

  // Generate appropriate title for the chroma
  const getChromaTitle = (chroma: ProcessedChromaData, slug?: string): string => {
    // Special handling for Victorious chromas with rank-queue descriptions
    if ((chroma.skinName.toLowerCase().includes('victorious blitzcrank') ||
         chroma.skinName.toLowerCase().includes('victorious sejuani')) &&
        chroma.description) {

      // Extract rank and queue from description
      const description = chroma.description.toLowerCase()
      const flexMatch = description.match(/reached\s+(\w+)\s+rank\s+in\s+flex\s+ranked\s+queue/)
      const soloMatch = description.match(/reached\s+(\w+)\s+rank\s+in\s+solo\/duo\s+ranked\s+queue/)

      if (flexMatch) {
        const rank = flexMatch[1].charAt(0).toUpperCase() + flexMatch[1].slice(1) // Capitalize first letter
        return `${chroma.skinName} - ${rank} Flex`
      } else if (soloMatch) {
        const rank = soloMatch[1].charAt(0).toUpperCase() + soloMatch[1].slice(1) // Capitalize first letter
        return `${chroma.skinName} - ${rank} Solo`
      }
    }

    // Use the helper function to get the appropriate color name
    return `${chroma.skinName} - ${getChromaColorName(chroma, slug)}`
  }

  return (
    <SkinBackground
      customImageUrl={chroma.image}
      blur={10}
      brightness={0.25}
    >
      <SharedLayout>
        <div className="container mx-auto px-8 py-2 pb-8">

        {/* Title Section - Always First */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-white mb-2">
            {getChromaTitle(chroma, params.slug as string)}
          </h1>

          <div className="flex flex-wrap items-center gap-2">
            {chroma.skinTier !== 'N/A' && (
              <Badge
                variant="secondary"
                className="text-xs px-2 py-1"
                style={rarityStyle}
              >
                {chroma.availability === 'Legacy' ? 'Legacy' : chroma.skinTier}
              </Badge>
            )}

            {chroma.isDefault && (
              <Badge variant="secondary" className="text-xs px-2 py-1 bg-blue-600/20 text-blue-300 border-blue-600/30">
                Default
              </Badge>
            )}

            {chroma.set && (
              <Badge variant="secondary" className="text-xs px-2 py-1 bg-violet-600/20 text-violet-300 border-violet-600/30">
                {chroma.set}
              </Badge>
            )}
          </div>
        </div>

        {/* Mobile: Image Section - Second */}
        <div className="lg:hidden mb-6">
          <Card className="bg-gray-900/40 border-purple-700/20 overflow-hidden">
            <CardContent className="p-0">
              <div className="aspect-video relative">
                <Image
                  src={chroma.image}
                  alt={`${getChromaTitle(chroma, params.slug as string)} Chroma`}
                  fill
                  className="object-contain"
                  priority
                  onContextMenu={(e) => e.preventDefault()}
                  draggable={false}
                />

                {/* Default Badge */}
                {chroma.isDefault && (
                  <div className="absolute top-4 right-4">
                    <Badge variant="secondary" className="bg-blue-600 text-white">
                      Default
                    </Badge>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons - Under the chroma image */}
          <div className="space-y-3 mt-4">
            <Button
              className="w-full bg-purple-600 hover:bg-purple-700 text-white"
              onClick={() => {
                // Create skin slug from skin name
                const skinSlug = chroma.skinName
                  .toLowerCase()
                  .replace(/[^a-z0-9\s-]/g, '')
                  .replace(/\s+/g, '-')
                  .replace(/-+/g, '-')
                  .replace(/^-|-$/g, '')
                router.push(`/skins/${skinSlug}`)
              }}
            >
              <ExternalLink className="h-4 w-4 mr-2" />
              View Skin Page
            </Button>
            <Button variant="outline" className="w-full border-gray-700/20 text-gray-500 cursor-not-allowed opacity-50" disabled>
              <Bell className="h-4 w-4 mr-2" />
              Add to Watchlist (Coming Soon)
            </Button>
          </div>
        </div>

        {/* Main Content - Desktop: Side by Side, Mobile: Details Only */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-6 lg:mb-12">
          {/* Chroma Information - Desktop: LEFT, Mobile: THIRD */}
          <div className="space-y-6">

            {/* Enhanced Chroma Details */}
            <Card className="bg-gray-900/40 border-purple-700/20">
              <CardContent className="p-6">
                <h3 className="text-xl font-semibold text-white mb-4">Chroma Details</h3>
                <div className="space-y-3">

                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Chroma Rarity:</span>
                    <div className="flex items-center space-x-2">
                      <ChromaRarityIcons
                        rarity={chroma.chromaRarity}
                        size={20}
                        showTooltip={false}
                      />
                      <span className="text-white">{chroma.chromaRarity}</span>
                    </div>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">
                      {chroma.availability !== 'Available' && mythicCost ? 'Last Recorded Price:' : 'Cost:'}
                    </span>
                    {chroma.availability !== 'Available' && mythicCostLoading ? (
                      <span className="animate-pulse text-gray-400">Loading...</span>
                    ) : chroma.availability !== 'Available' && mythicCost ? (
                      <div className="flex items-center space-x-1 justify-end">
                        <Image
                          src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/mythic.svg"
                          alt="ME"
                          width={16}
                          height={16}
                          className="w-4 h-4"
                          style={{ userSelect: 'none', pointerEvents: 'none' }}
                          draggable={false}
                        />
                        <span className="font-medium" style={{ color: '#b47bdf' }}>
                          {mythicCost.cost}
                        </span>
                        <span className="text-white">
                          — {new Date(mythicCost.endTime).toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric'
                          })}
                        </span>
                      </div>
                    ) : formatChromaPrice(chroma.price, chroma.availability) === 'N/A' ? (
                      <span className="text-gray-400 font-medium">N/A</span>
                    ) : (
                      <div className="flex items-center space-x-1">
                        <Image
                          src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg"
                          alt="RP"
                          width={16}
                          height={16}
                          className="w-4 h-4"
                          style={{ userSelect: 'none', pointerEvents: 'none' }}
                          draggable={false}
                        />
                        <span className="font-medium" style={{ color: 'rgb(251, 146, 60)' }}>
                          {formatChromaPrice(chroma.price, chroma.availability).replace(' RP', '')}
                        </span>
                      </div>
                    )}
                  </div>
                  {chroma.set && (
                    <div className="flex justify-between">
                      <span className="text-gray-400">Set:</span>
                      <span className="text-white">{chroma.set}</span>
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span className="text-gray-400">Release Date:</span>
                    <span className="text-gray-400">{chroma.releaseDate || 'N/A'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Availability:</span>
                    <span className={`font-medium ${
                      chroma.availability === 'Available' ? 'text-green-400' :
                      chroma.availability === 'Legacy' ? 'text-red-400' : 'text-red-400'
                    }`}>
                      {chroma.availability === 'Legacy' ? 'Unavailable' : chroma.availability}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Skin Chroma Count:</span>
                    <span className="text-white">{totalChromasForSkin}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Color Name:</span>
                    {chromaColor ? (
                      <button
                        onClick={() => {
                          const encodedColorName = encodeURIComponent(chromaColor)
                          router.push(`/skins/chromas?chromaColors=${encodedColorName}`)
                        }}
                        className="text-white hover:text-purple-300 transition-colors flex items-center gap-1 group"
                      >
                        <span>{getChromaColorName(chroma, params.slug as string)}</span>
                        <ExternalLink className="h-3 w-3 text-gray-400 group-hover:text-purple-300 transition-colors" />
                      </button>
                    ) : (
                      <span className="text-white">
                        {getChromaColorName(chroma, params.slug as string)}
                      </span>
                    )}
                  </div>
                  <div className="flex justify-between items-start">
                    <span className="text-gray-400">Color Palette:</span>
                    <div className="flex flex-wrap gap-1 max-w-[200px] justify-end">
                      {chroma.colors.map((color, index) => (
                        <div
                          key={index}
                          className="w-5 h-5 rounded-full border-2 border-white/50 shadow-sm"
                          style={{ backgroundColor: color }}
                          title={getColorDisplayName(color)}
                        />
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Description Card - Only show if chroma has a description */}
            {chroma.description && (
              <Card className="bg-gray-900/40 border-purple-700/20">
                <CardContent className="p-6">
                  <h3 className="text-xl font-semibold text-white mb-4">Description</h3>
                  <p className="text-gray-300 leading-relaxed">
                    {chroma.description}
                  </p>
                </CardContent>
              </Card>
            )}

          </div>

          {/* Chroma Image - Desktop Only (Mobile version is above) */}
          <div className="space-y-4 hidden lg:block">
            <Card className="bg-gray-900/40 border-purple-700/20 overflow-hidden">
              <CardContent className="p-0">
                <div className="aspect-video relative">
                  <Image
                    src={chroma.image}
                    alt={`${getChromaTitle(chroma, params.slug as string)} Chroma`}
                    fill
                    className="object-contain"
                    priority
                    onContextMenu={(e) => e.preventDefault()}
                    draggable={false}
                  />

                  {/* Default Badge */}
                  {chroma.isDefault && (
                    <div className="absolute top-4 right-4">
                      <Badge variant="secondary" className="bg-blue-600 text-white">
                        Default
                      </Badge>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Action Buttons - Under the chroma image */}
            <div className="space-y-3">
              <Button
                className="w-full bg-purple-600 hover:bg-purple-700 text-white"
                onClick={() => {
                  // Create skin slug from skin name
                  const skinSlug = chroma.skinName
                    .toLowerCase()
                    .replace(/[^a-z0-9\s-]/g, '')
                    .replace(/\s+/g, '-')
                    .replace(/-+/g, '-')
                    .replace(/^-|-$/g, '')
                  router.push(`/skins/${skinSlug}`)
                }}
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                View Skin Page
              </Button>
              <Button variant="outline" className="w-full border-gray-700/20 text-gray-500 cursor-not-allowed opacity-50" disabled>
                <Bell className="h-4 w-4 mr-2" />
                Add to Watchlist (Coming Soon)
              </Button>
            </div>
          </div>
        </div>

        {/* Related Chromas */}
        {relatedChromas.length > 0 && (
          <div>
            <div className="flex items-center space-x-3 mb-6">
              <Sparkles className="h-6 w-6 text-purple-400" />
              <h2 className="text-2xl font-bold text-white">Related Chromas</h2>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
              {relatedChromas.map((relatedChroma) => {
                const chromaSlug = createChromaURL(relatedChroma)

                return (
                  <Link
                    key={relatedChroma.id}
                    href={`/skins/chromas/${chromaSlug}`}
                    className="block"
                  >
                    <div className="bg-gradient-to-br from-gray-900/60 to-gray-800/40 border border-purple-700/30 rounded-xl overflow-hidden relative group transition-all duration-500 transform hover:scale-105 cursor-pointer hover:border-purple-400/70 hover:shadow-lg hover:shadow-purple-500/20">
                      {/* Chroma Image - Centered display like mythic shop with correct height */}
                      {relatedChroma.image && (
                        <div className="h-48 flex items-center justify-center p-6">
                          <Image
                            src={relatedChroma.image}
                            alt={`${relatedChroma.name} - ${relatedChroma.skinName}`}
                            width={170}
                            height={170}
                            className="object-contain"
                          />

                          {/* Default Badge */}
                          {relatedChroma.isDefault && (
                            <div className="absolute top-3 right-3">
                              <Badge variant="secondary" className="text-xs bg-blue-600 text-white px-2 py-1">
                                Default
                              </Badge>
                            </div>
                          )}

                          {/* Color Indicators - Top Left */}
                          <div className="absolute top-3 left-3 flex space-x-1">
                            {(() => {
                              // Get unique colors only
                              const uniqueColors = [...new Set(relatedChroma.colors)]
                              return uniqueColors.slice(0, 3).map((color, index) => (
                                <div
                                  key={index}
                                  className="w-4 h-4 rounded-full border-2 border-white/70 shadow-lg"
                                  style={{ backgroundColor: color }}
                                  title={getColorDisplayName(color)}
                                />
                              ))
                            })()}
                            {(() => {
                              const uniqueColors = [...new Set(relatedChroma.colors)]
                              return uniqueColors.length > 3 && (
                                <div className="w-4 h-4 rounded-full bg-gray-600 border-2 border-white/70 shadow-lg flex items-center justify-center">
                                  <span className="text-xs text-white font-bold">+</span>
                                </div>
                              )
                            })()}
                          </div>
                        </div>
                      )}

                      {/* Content overlay at bottom */}
                      <div className="p-4 bg-gradient-to-t from-black/90 via-black/70 to-transparent">
                        <p
                          className="text-white font-semibold text-sm mb-2 truncate"
                          title={relatedChroma.name}
                        >
                          {relatedChroma.name}
                        </p>

                        {/* Price and rarity row */}
                        <div className="flex items-center justify-between">
                          {/* Price on the left */}
                          <div className="flex items-center space-x-1">
                            {(() => {
                              // Check if chroma has mythic price
                              const mythicPrice = relatedChroma.contentId ? mythicPrices.get(relatedChroma.contentId) : null

                              if (relatedChroma.availability !== 'Available' && mythicPrice) {
                                // Show mythic price
                                return (
                                  <>
                                    <Image
                                      src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/mythic.svg"
                                      alt="ME"
                                      width={16}
                                      height={16}
                                      className="h-4 w-4"
                                    />
                                    <span className="font-medium" style={{ color: '#b47bdf' }}>
                                      {mythicPrice.cost}
                                    </span>
                                  </>
                                )
                              } else if (formatChromaPrice(relatedChroma.price, relatedChroma.availability) === 'N/A') {
                                // Show N/A for legacy items without mythic price
                                return (
                                  <span className="text-gray-400 font-medium">N/A</span>
                                )
                              } else {
                                // Show regular RP price
                                return (
                                  <>
                                    <Image
                                      src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg"
                                      alt="RP"
                                      width={16}
                                      height={16}
                                      className="h-4 w-4"
                                    />
                                    <span className="font-medium" style={{ color: 'rgb(251, 146, 60)' }}>
                                      {formatChromaPrice(relatedChroma.price, relatedChroma.availability).replace(' RP', '')}
                                    </span>
                                  </>
                                )
                              }
                            })()}
                          </div>

                          {/* Rarity icons on the right */}
                          <div className="flex items-center gap-1 flex-shrink-0">
                            {/* Chroma Rarity Icon */}
                            <ChromaRarityIconsSimple
                              rarity={relatedChroma.chromaRarity}
                              size={16}
                            />
                            {/* Skin Rarity Icon */}
                            {relatedChroma.skinTier !== 'N/A' && (
                              <SkinRarityIconsSimple
                                rarity={relatedChroma.skinTier}
                                isLegacy={relatedChroma.availability === 'Legacy'}
                                isBase={false}
                                size={16}
                              />
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </Link>
                )
              })}
            </div>
          </div>
        )}
        </div>
      </SharedLayout>
    </SkinBackground>
  )
}
