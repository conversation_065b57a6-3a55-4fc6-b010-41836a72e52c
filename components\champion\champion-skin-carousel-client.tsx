"use client"

import { SkinRarityIcons } from "@/components/ui/skin-rarity-icons"
import { useIsMobile } from "@/hooks/use-mobile"
import { ChevronLeft, ChevronRight } from "lucide-react"
import Image from "next/image"
import { useCallback, useEffect, useRef, useState } from "react"

interface SkinData {
  name: string
  price: string
  rarity: string
  rarityTier?: string
  isLegacy?: boolean
  image: string
  splashArt: string
  inGameImage: string
  isBase: boolean
  hasInGameImage: boolean
  chromas: any[]
  skinNum: number
  communityDragonId?: number
}

interface ChampionSkinCarouselClientProps {
  skins: SkinData[]
  currentSkinIndex: number
  onSkinSelect: (index: number) => void
}

// Legacy functions kept for backward compatibility - now uses SkinRarityIcons component
// These functions are no longer used but kept to avoid breaking changes

// Get rarity icon URL
function getRarityIcon(rarity: string) {
  const iconMap: Record<string, string> = {
    'Legacy': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/summoner-icon/icon-legacy.png',
    'Regular': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-parties/global/default/summoner-icon-rare.png',
    'Epic': 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/cn-gem-3.png',
    'Legendary': 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/legendary.png',
    'Ultimate': 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/ultimate.png',
    'Mythic': 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/cn-gem-8.png',
    'Exalted': 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/cn-gem-10.png',
    'Transcendent': 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/cn-gem-11.png'
  }
  return iconMap[rarity] || null
}

// Get rarity description for tooltip
function getRarityDescription(rarity: string) {
  const descriptions: Record<string, string> = {
    'Regular': 'Standard skin quality with basic visual changes',
    'Legacy': 'Limited-time skin no longer available for purchase',
    'Epic': 'High-quality skin with new model, textures, and effects',
    'Legendary': 'Premium skin with new voice lines and animations',
    'Ultimate': 'Evolving skin that changes throughout the game',
    'Mythic': 'Exclusive skin with unique prestige elements',
    'Exalted': 'Ultra-rare skin with exceptional quality',
    'Transcendent': 'Highest tier skin with transformative features'
  }
  return descriptions[rarity] || `${rarity} rarity skin`
}

export default function ChampionSkinCarouselClient({
  skins,
  currentSkinIndex,
  onSkinSelect
}: ChampionSkinCarouselClientProps) {
  const [isAnimating, setIsAnimating] = useState(false)
  const [screenWidth, setScreenWidth] = useState(0)

  const isMobile = useIsMobile()
  const carouselRef = useRef<HTMLDivElement>(null)

  // Update screen width on resize
  useEffect(() => {
    const updateScreenWidth = () => {
      setScreenWidth(window.innerWidth)
    }

    updateScreenWidth()
    window.addEventListener('resize', updateScreenWidth)
    return () => window.removeEventListener('resize', updateScreenWidth)
  }, [])

  // Enhanced responsive display settings based on screen width
  const getMaxDisplaySkins = useCallback(() => {
    if (isMobile) return 5 // Show 5 skins on mobile for better touch targets

    // Desktop responsive breakpoints - more conservative for better fit
    if (screenWidth >= 1920) return 9  // Very large screens: 9 skins
    if (screenWidth >= 1600) return 8  // Large screens: 8 skins
    if (screenWidth >= 1400) return 7  // Medium-large screens: 7 skins
    if (screenWidth >= 1200) return 6  // Medium screens: 6 skins (1080p falls here)
    if (screenWidth >= 1024) return 5  // Small desktop: 5 skins
    if (screenWidth >= 768) return 4   // Tablet: 4 skins
    return 3 // fallback for very small screens
  }, [isMobile, screenWidth])

  const maxDisplaySkins = getMaxDisplaySkins()
  const actualSkinsCount = skins.length
  const displayCount = Math.min(actualSkinsCount, maxDisplaySkins)

  // Calculate visible skins with the selected skin in center (sequential order)
  const getVisibleSkins = () => {
    const centerPosition = Math.floor(maxDisplaySkins / 2) // Position 4 for 9 slots (0-8)
    const visibleIndices = []

    // Find the default skin (base skin) and create sequential order
    const defaultSkinIndex = skins.findIndex(skin => skin.isBase)

    // Create ordered skin list: [Default, Skin1, Skin2, Skin3, ...]
    const orderedSkins = []
    if (defaultSkinIndex !== -1) {
      orderedSkins.push(defaultSkinIndex) // Default skin first
      // Add other skins in their original order (excluding default)
      for (let i = 0; i < actualSkinsCount; i++) {
        if (i !== defaultSkinIndex) {
          orderedSkins.push(i)
        }
      }
    } else {
      // Fallback if no base skin found
      for (let i = 0; i < actualSkinsCount; i++) {
        orderedSkins.push(i)
      }
    }

    // Find position of current skin in the sequential ordered list
    const currentPositionInOrdered = orderedSkins.indexOf(currentSkinIndex)

    if (actualSkinsCount >= maxDisplaySkins) {
      // For champions with 9+ skins, show 9 skins with circular wrapping around current
      for (let i = 0; i < maxDisplaySkins; i++) {
        const offset = i - centerPosition
        let targetPosition = currentPositionInOrdered + offset

        // Handle circular wrapping
        if (targetPosition < 0) {
          targetPosition = actualSkinsCount + targetPosition
        } else if (targetPosition >= actualSkinsCount) {
          targetPosition = targetPosition - actualSkinsCount
        }

        const skinIndex = orderedSkins[targetPosition]
        visibleIndices.push(skinIndex)
      }
    } else {
      // For champions with fewer skins than display slots
      // Show all skins in sequential order with empty slots on sides

      for (let i = 0; i < maxDisplaySkins; i++) {
        const offset = i - centerPosition
        const targetPosition = currentPositionInOrdered + offset

        if (targetPosition < 0 || targetPosition >= actualSkinsCount) {
          // This position should be empty
          visibleIndices.push(null)
        } else {
          // Show the skin at this sequential position
          const skinIndex = orderedSkins[targetPosition]
          visibleIndices.push(skinIndex)
        }
      }
    }

    return visibleIndices
  }

  const visibleSkins = getVisibleSkins()
  const centerIndex = Math.floor(displayCount / 2)

  const handleSkinClick = async (targetIndex: number) => {
    if (targetIndex === currentSkinIndex || isAnimating) return

    setIsAnimating(true)
    onSkinSelect(targetIndex)

    // Reset animation state after a short delay
    setTimeout(() => setIsAnimating(false), 300)
  }

  const navigateCarousel = (direction: 'prev' | 'next') => {
    if (isAnimating) return

    const newIndex = direction === 'prev'
      ? (currentSkinIndex - 1 + actualSkinsCount) % actualSkinsCount
      : (currentSkinIndex + 1) % actualSkinsCount

    handleSkinClick(newIndex)
  }

  return (
    <div className="relative mt-3 px-2 sm:px-0" style={{ minHeight: isMobile ? '80px' : '100px', paddingTop: '5px', paddingBottom: '0px' }}>
      <div className="flex items-center justify-center h-full" style={{ gap: isMobile ? '8px' : '12px' }}>
        {/* Left Arrow - Hidden on mobile, responsive positioning on desktop */}
        {!isMobile && (
          <button
            onClick={() => navigateCarousel('prev')}
            disabled={isAnimating}
            className="w-6 h-6 rounded-full bg-orange-600/80 hover:bg-orange-500/90 border-2 border-orange-500/60 hover:border-orange-400 flex items-center justify-center transition-all duration-200 disabled:opacity-50 text-white shadow-lg backdrop-blur-sm flex-shrink-0"
          >
            <ChevronLeft size={18} className="text-white" />
          </button>
        )}

        {/* Skin carousel container - Responsive */}
        <div
          ref={carouselRef}
          className={`relative overflow-visible ${isMobile ? 'w-full' : 'flex-1'}`}
          style={{
            height: isMobile ? '100px' : '140px',
            maxWidth: isMobile ? '320px' : `${maxDisplaySkins * 72 + (maxDisplaySkins - 1) * 4}px` // Dynamic width based on skin count
          }}
        >
          <div
            className={`relative select-none`}
            style={{
              height: isMobile ? '80px' : '100px',
              padding: isMobile ? '20px 10px' : '40px 20px',
              width: '100%',
              userSelect: 'none',
              WebkitUserSelect: 'none'
            }}
            // No drag functionality on mobile or PC
          >
            <div
              className={`flex justify-center items-center ${isMobile ? 'space-x-1' : 'space-x-1'}`}
              style={{ height: isMobile ? '48px' : '64px' }}
            >
              {visibleSkins.map((skinIndex, position) => {
                if (skinIndex === null) {
                  // Empty slot with dotted border - Responsive
                  return (
                    <div
                      key={`empty-${position}`}
                      className="flex justify-center items-center flex-shrink-0"
                      style={{ width: isMobile ? '56px' : '72px' }}
                    >
                      <div className={`${isMobile ? 'w-12 h-12' : 'w-16 h-16'} rounded-lg border-2 border-dashed border-gray-500/80 bg-gray-800/40 backdrop-blur-sm flex items-center justify-center`}>
                        <div className="w-2 h-2 rounded-full bg-gray-500/60"></div>
                      </div>
                    </div>
                  )
                }

                const skin = skins[skinIndex]
                const isSelected = skinIndex === currentSkinIndex
                const rarityTier = skin.rarityTier || skin.rarity
                const isLegacy = skin.isLegacy || false

                return (
                  <div
                    key={skinIndex}
                    className="flex justify-center items-center flex-shrink-0"
                    style={{ width: isMobile ? '56px' : '72px' }}
                  >
                    <div className="relative">
                      <button
                        onClick={() => handleSkinClick(skinIndex)}
                        disabled={isAnimating}
                        className={`relative ${isMobile ? 'w-12 h-12' : 'w-16 h-16'} rounded-lg overflow-hidden border-2 transition-all duration-300 touch-target ${
                          isSelected
                            ? 'border-orange-500 shadow-lg shadow-orange-500/50 scale-110'
                            : 'border-gray-600 hover:border-orange-400 hover:scale-105'
                        } ${isAnimating ? 'pointer-events-none' : ''}`}
                      >
                        <Image
                          unoptimized
                          loading="eager"
                          src={skin.image}
                          alt={skin.name}
                          width={isMobile ? 48 : 64}
                          height={isMobile ? 48 : 64}
                          className="w-full h-full object-cover"
                          style={{ userSelect: 'none', pointerEvents: 'none' }}
                          draggable={false}
                          priority={isSelected} // Prioritize loading for selected skin
                        />
                        <Image
                          unoptimized
                          loading="eager"
                          src={skin.splashArt}
                          alt=""
                          fill
                          className="hidden"
                          aria-hidden="true"
                        />

                        {/* Glow effect for selected skin */}
                        {isSelected && (
                          <div className="absolute inset-0 bg-orange-0/20 rounded-lg"></div>
                        )}
                      </button>

                      {/* Rarity icons overlay - Using new SkinRarityIcons component */}
                      <div
                        className={`absolute left-1/2 transition-all duration-300 ${
                          isSelected ? 'scale-110' : 'hover:scale-105'
                        }`}
                        style={{
                          bottom: isMobile ? '-1px' : '-2px',
                          transform: 'translateX(-50%)'
                        }}
                        onClick={() => handleSkinClick(skinIndex)}
                      >
                        <SkinRarityIcons
                          rarity={rarityTier}
                          isLegacy={isLegacy}
                          isBase={skin.isBase}
                          size={isMobile ? 14 : 16}
                          showTooltip={false}
                        />
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        </div>

        {/* Right Arrow - Hidden on mobile, responsive positioning on desktop */}
        {!isMobile && (
          <button
            onClick={() => navigateCarousel('next')}
            disabled={isAnimating}
            className="w-6 h-6 rounded-full bg-orange-600/80 hover:bg-orange-500/90 border-2 border-orange-500/60 hover:border-orange-400 flex items-center justify-center transition-all duration-200 disabled:opacity-50 text-white shadow-lg backdrop-blur-sm flex-shrink-0"
          >
            <ChevronRight size={18} className="text-white" />
          </button>
        )}
      </div>

      {/* Mobile hint - always visible to prevent layout shift */}
      {isMobile && (
        <div className="text-center mt-2">
          <p className="text-xs text-gray-400">
            Tap to select
          </p>
        </div>
      )}
    </div>
  )
}
