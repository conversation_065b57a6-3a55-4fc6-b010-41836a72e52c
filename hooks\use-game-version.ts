// React hook for game version information

import { useCallback, useEffect, useState } from 'react'

interface GameVersionData {
  current: string
  previous: string[]
  all: string[]
}

interface UseGameVersionResult {
  version: GameVersionData | null
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
}

export function useGameVersion(): UseGameVersionResult {
  const [version, setVersion] = useState<GameVersionData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchGameVersion = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await fetch('/api/game-version')
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch game version')
      }
      
      setVersion(result.data)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
      setError(errorMessage)
      console.error('Error fetching game version:', err)
    } finally {
      setLoading(false)
    }
  }, [])

  useEffect(() => {
    fetchGameVersion()
  }, [fetchGameVersion])

  return {
    version,
    loading,
    error,
    refetch: fetchGameVersion
  }
}
