// src/LeagueApiClient.ts


/**
 * Riot Data Dragon version list
 */
type VersionsResponse = string[]

/**
 * Structure of the champion list JSON
 */
interface ChampionListResponse {
  data: Record<string, ChampionDetail>
}

/**
 * Structure of a single champion detail JSON
 */
interface ChampionDetailResponse {
  data: Record<string, ChampionDetail>
}

/**
 * Minimal champion detail fields
 */
export interface ChampionDetail {
  id: string
  key: string
  name: string
  title: string
  skins: ChampionSkin[]
  tags?: string[] // Riot API includes tags
  blurb?: string // Short description
  info?: {
    attack: number
    defense: number
    magic: number
    difficulty: number
  }
  partype?: string // Resource type (e.g., "Mana", "Energy")
  // add other fields as needed
}

/**
 * Champion skin metadata
 */
export interface ChampionSkin {
  id: string
  num: number
  name: string
}

/**
 * Free champion rotation payload
 */
export interface RotationResponse {
  freeChampionIds: number[]
  maxNewPlayerLevel: number
  freeChampionIdsForNewPlayers: number[]
}

/**
 * Client for interacting with Riot's static data and live APIs
 */
export class LeagueApiClient {
  private currentVersion = '15.11.1'
  private readonly baseUrl = 'https://ddragon.leagueoflegends.com'
  private readonly riotApiKey?: string
  private readonly region: string
  private communityDragonSkinsCache: { data: any; timestamp: number } | null = null
  private readonly CACHE_TTL = 1000 * 60 * 60 * 24 // 24 hours

  constructor() {
    this.riotApiKey = 'RGAPI-0bb9070d-cfa5-427f-ad88-545d6e0fe7b9'
    this.region = process.env.LEAGUE_API_REGION ?? 'na1'

    if (this.riotApiKey) {
      
    } else {
      console.warn('⚠️ No Riot API key found - Using mock data for rotation')
    }
  }

  /**
   * Fetch the latest patch versions and update `currentVersion`
   */
  public async updateGameVersion(): Promise<string> {
    try {
      const res: Response = await fetch(`${this.baseUrl}/api/versions.json`)
      if (!res.ok) {
        throw new Error(`HTTP ${res.status}: Failed to fetch versions`)
      }
      const versions: VersionsResponse = await res.json()
      if (versions.length > 0) {
        this.currentVersion = versions[0]
        
      }
    } catch (err) {
      console.warn('Failed to update game version, using fallback:', err)
    }
    return this.currentVersion
  }

  /**
   * Get the full champions list
   */
  public async getChampions(): Promise<Record<string, ChampionDetail>> {
    await this.updateGameVersion()
    const url = `${this.baseUrl}/cdn/${this.currentVersion}/data/en_US/champion.json`
    const res: Response = await fetch(url)
    if (!res.ok) {
      throw new Error(`HTTP ${res.status}: Failed to fetch champions`)
    }
    const payload: ChampionListResponse = await res.json()
    return payload.data
  }

  /**
   * Get detailed data for a single champion
   */
  public async getChampionDetail(championSlug: string): Promise<ChampionDetail> {
    await this.updateGameVersion()
    const url = `${this.baseUrl}/cdn/${this.currentVersion}/data/en_US/champion/${championSlug}.json`
    const res: Response = await fetch(url)
    if (!res.ok) {
      throw new Error(`HTTP ${res.status}: Champion "${championSlug}" not found`)
    }
    const payload: ChampionDetailResponse = await res.json()
    const detail = payload.data[championSlug]
    if (!detail) {
      throw new Error(`Champion data for "${championSlug}" missing in response`)
    }
    return detail
  }

  /**
   * Get static item data
   */
  public async getItems(): Promise<Record<string, unknown>> {
    await this.updateGameVersion()
    const url = `${this.baseUrl}/cdn/${this.currentVersion}/data/en_US/item.json`
    const res: Response = await fetch(url)
    if (!res.ok) {
      throw new Error(`HTTP ${res.status}: Failed to fetch items`)
    }
    const payload = await res.json()
    return payload.data
  }

  /**
   * Fetch Riot's live free champion rotation (requires API key)
   */
  public async getFreeChampionRotation(region?: string): Promise<RotationResponse | null> {
    if (!this.riotApiKey) {
      console.warn('No API key available for free rotation')
      return null
    }
    const targetRegion = region || this.region
    const url = `https://${targetRegion}.api.riotgames.com/lol/platform/v3/champion-rotations`
    
    const res: Response = await fetch(url, {
      headers: {
        'X-Riot-Token': this.riotApiKey,
        'User-Agent': 'LoLDB/1.0',
      },
    })
    if (!res.ok) {
      throw new Error(`API Error: ${res.status} ${res.statusText}`)
    }
    const rotation: RotationResponse = await res.json()
    return rotation
  }

  /**
   * Fetch champion data from Community Dragon by champion ID
   */
  public async getChampionDataById(championId: number): Promise<any> {
    const url = `https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/champions/${championId}.json`
    

    try {
      const res: Response = await fetch(url)
      if (!res.ok) {
        throw new Error(`HTTP ${res.status}: Champion ID ${championId} not found`)
      }
      const championData = await res.json()
      
      return championData
    } catch (error) {
      console.error(`❌ Failed to fetch champion ID ${championId}:`, error)
      throw error
    }
  }

  /**
   * Standard splash art URL
   */
  public getChampionSplashUrl(championId: string, skinNum = 0): string {
    return `${this.baseUrl}/cdn/img/champion/splash/${championId}_${skinNum}.jpg`
  }

  /**
   * Get champion splash art using Community Dragon skins.json API with uncenteredSplashPath
   * For base skins: championKey + "000" (e.g., 266000 for Aatrox)
   * For other skins: use direct skin ID from DDragon
   */
  public async getChampionSplashUrlNew(championId: string, skinNum = 0, championKey?: string): Promise<string> {
    try {
      // Load Community Dragon skins data
      const skinsData = await this.loadCommunityDragonSkinsData()

      // If we don't have championKey, fall back to old method
      if (!championKey) {
        console.warn('Champion key not provided for splash art, falling back to DDragon')
        return this.getChampionSplashUrl(championId, skinNum)
      }

      let skinId: string
      if (skinNum === 0) {
        // Base skin: championKey + "000"
        skinId = `${championKey}000`
      } else {
        // Other skins: championKey + skinNum (padded to 3 digits)
        skinId = `${championKey}${skinNum.toString().padStart(3, '0')}`
      }

      // Find the skin in Community Dragon data
      const skinData = skinsData[skinId]
      if (!skinData) {
        console.warn(`Skin data not found for ID ${skinId}, falling back to DDragon`)
        return this.getChampionSplashUrl(championId, skinNum)
      }

      // Try uncenteredSplashPath first, then splashPath as fallback
      const splashPath = skinData.uncenteredSplashPath || skinData.splashPath
      if (!splashPath) {
        console.warn(`No splash path found for skin ${skinId}, falling back to DDragon`)
        return this.getChampionSplashUrl(championId, skinNum)
      }

      // Extract the path after "/ASSETS/" and convert to lowercase
      const assetsIndex = splashPath.indexOf('/ASSETS/')
      if (assetsIndex === -1) {
        console.warn(`Invalid splash path format for skin ${skinId}: ${splashPath}`)
        return this.getChampionSplashUrl(championId, skinNum)
      }

      const pathAfterAssets = splashPath.substring(assetsIndex + 1) // +1 to remove the leading slash
      const lowercasePath = pathAfterAssets.toLowerCase()

      // Construct the final Community Dragon URL
      const baseUrl = 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default'
      const finalUrl = `${baseUrl}/${lowercasePath}`

      return finalUrl
    } catch (error) {
      console.error('Error in getChampionSplashUrlNew:', error)
      return this.getChampionSplashUrl(championId, skinNum)
    }
  }

  /**
   * Standard square icon URL (fallback)
   */
  public getChampionSquareUrl(championId: string): string {
    return `${this.baseUrl}/cdn/${this.currentVersion}/img/champion/${championId}.png`
  }

  /**
   * Load and cache Community Dragon skins data
   */
  private async loadCommunityDragonSkinsData(): Promise<any> {
    // Check if cache is still valid
    if (this.communityDragonSkinsCache && (Date.now() - this.communityDragonSkinsCache.timestamp) < this.CACHE_TTL) {
      return this.communityDragonSkinsCache.data
    }

    try {
      const response = await fetch('https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/skins.json')
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: Failed to fetch Community Dragon skins data`)
      }

      const skinsData = await response.json()

      // Cache the data
      this.communityDragonSkinsCache = {
        data: skinsData,
        timestamp: Date.now()
      }

      return skinsData
    } catch (error) {
      console.error('Failed to load Community Dragon skins data:', error)
      throw error
    }
  }

  /**
   * Try to fetch the "circle" icon from CommunityDragon's HUD folder.
   * Falls back to the standard square icon if not found.
   */
  public async getChampionCircleUrl(
    championId: string,
    skinNum = 0,
    skinName?: string,
    allSkins?: any[],
    championKey?: string
  ): Promise<string> {
    try {
      // Load Community Dragon skins data
      const skinsData = await this.loadCommunityDragonSkinsData()

      // If we don't have championKey, we need to get it from DDragon API
      if (!championKey && allSkins && allSkins.length > 0) {
        // Try to extract champion key from the champion data if available
        // This is a fallback - ideally championKey should be passed
        console.warn('Champion key not provided, falling back to old method')
        return this.getChampionSquareUrl(championId)
      }

      let skinId: string
      if (skinNum === 0) {
        // Base skin: championKey + "000"
        skinId = `${championKey}000`
      } else {
        // Other skins: championKey + skinNum (padded to 3 digits)
        skinId = `${championKey}${skinNum.toString().padStart(3, '0')}`
      }

      // Find the skin in Community Dragon data
      const skinData = skinsData[skinId]
      if (!skinData || !skinData.tilePath) {
        console.warn(`Skin data not found for ID ${skinId}, falling back to square icon`)
        return this.getChampionSquareUrl(championId)
      }

      // Extract the path after "/ASSETS/" and convert to lowercase
      const assetsIndex = skinData.tilePath.indexOf('/ASSETS/')
      if (assetsIndex === -1) {
        console.warn(`Invalid tilePath format for skin ${skinId}: ${skinData.tilePath}`)
        return this.getChampionSquareUrl(championId)
      }

      const pathAfterAssets = skinData.tilePath.substring(assetsIndex + 1) // +1 to remove the leading slash
      const lowercasePath = pathAfterAssets.toLowerCase()

      // Construct the final Community Dragon URL
      const baseUrl = 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default'
      const finalUrl = `${baseUrl}/${lowercasePath}`

      return finalUrl
    } catch (error) {
      console.error('Error in getChampionCircleUrl:', error)
      return this.getChampionSquareUrl(championId)
    }
  }

  /**
   * Get raw DDragon versions list
   */
  public async getVersions(): Promise<string[]> {
    const res: Response = await fetch(`${this.baseUrl}/api/versions.json`)
    if (!res.ok) {
      throw new Error(`HTTP ${res.status}: Failed to fetch versions`)
    }
    const versions: VersionsResponse = await res.json()
    return versions
  }

  /** True if we have an API key loaded */
  public hasApiKey(): boolean {
    return Boolean(this.riotApiKey)
  }

  /** Read-only access to current DDragon version */
  public getCurrentVersion(): string {
    return this.currentVersion
  }


}

export const leagueApi = new LeagueApiClient()
export default LeagueApiClient
