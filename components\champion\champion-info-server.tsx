"use client"

import { <PERSON><PERSON> } from "@/components/ui/badge"
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ontent,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import Image from "next/image"
import { ChampionWinRateGraph } from "./champion-winrate-graph"
import { getRoleTagColor, getClassTagColor } from "@/lib/utils/tag-colors"
import { useRouter } from "next/navigation"
import { useState, useEffect } from "react"
import { leagueApiBrowser } from "@/lib/api/league-client-browser"

// Utility function to get position icon URLs
function getPositionIconUrl(role: string): string {
  const positionIcons: Record<string, string> = {
    'Top': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-social/global/default/positionicon-top.png',
    'Middle': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-social/global/default/positionicon-mid.png',
    'Jungle': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-social/global/default/positionicon-jungle.png',
    'Bottom': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-social/global/default/positionicon-bottom.png',
    'Support': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-social/global/default/positionicon-support.png'
  }
  return positionIcons[role] || positionIcons['Middle']
}

// Function to get champion's primary class from tags
function getChampionClass(tags: string[]): string {
  if (!tags || tags.length === 0) return 'Fighter'

  // Define the priority order for champion classes
  const classPriority = ['fighter', 'tank', 'mage', 'assassin', 'marksman', 'support']

  // Find the first tag that matches a known class
  for (const tag of tags) {
    const lowerTag = tag.toLowerCase()
    if (classPriority.includes(lowerTag)) {
      // Capitalize first letter for display
      return lowerTag.charAt(0).toUpperCase() + lowerTag.slice(1)
    }
  }

  // Fallback: capitalize the first tag
  return tags[0] ? tags[0].charAt(0).toUpperCase() + tags[0].slice(1) : 'Fighter'
}

// Function to get class icon URL
function getClassIconUrl(championClass: string): string {
  const classIcons: Record<string, string> = {
    'Fighter': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-fighter.png',
    'Tank': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-tank.png',
    'Mage': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-mage.png',
    'Assassin': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-assassin.png',
    'Support': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-support.png',
    'Marksman': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-marksman.png'
  }
  return classIcons[championClass] || classIcons['Fighter']
}

interface ChampionInfoServerProps {
  champion: any
  championData: any
}

export default function ChampionInfoServer({ champion, championData }: ChampionInfoServerProps) {
  const router = useRouter()
  const [championIcon, setChampionIcon] = useState<string>(`https://ddragon.leagueoflegends.com/cdn/15.11.1/img/champion/${champion.id}.png`)
  const [championListData, setChampionListData] = useState<any>(null)

  // Fetch champion data from the same API as /champions to get correct role/class
  useEffect(() => {
    async function fetchChampionListData() {
      try {
        const response = await fetch('/api/champions')
        if (response.ok) {
          const result = await response.json()
          if (result.success && result.data) {
            // Find the current champion in the list
            const currentChampion = result.data.find((c: any) =>
              c.name.toLowerCase() === champion.name.toLowerCase()
            )
            if (currentChampion) {
              setChampionListData(currentChampion)
            }
          }
        }
      } catch (error) {
        console.error('Failed to fetch champion list data:', error)
      }
    }
    fetchChampionListData()
  }, [champion.name])

  useEffect(() => {
    async function loadChampionIcon() {
      try {
        const iconUrl = await leagueApiBrowser.getChampionCircleUrl(champion.id, 0, undefined, champion.skins, champion.key)
        setChampionIcon(iconUrl)
      } catch (error) {
        console.error('Failed to load champion icon:', error)
        // Keep the fallback URL that's already set
      }
    }
    loadChampionIcon()
  }, [champion.id, champion.key])

  // Use championListData only - no fallback
  const primaryRole = championListData?.recommendedPositions?.[0]
  const championClass = championListData ? getChampionClass(championListData.tags) : null
  const positionIconUrl = primaryRole ? getPositionIconUrl(primaryRole) : null
  const classIconUrl = championClass ? getClassIconUrl(championClass) : null

  return (
    <div>
      <div className="flex items-center space-x-4 mb-4">
      {/* Champion Portrait */}
      <div className="relative">
        <Image
          src={championIcon}
          alt={champion.name}
          width={80}
          height={80}
          className="rounded-full border-2 border-orange-500/40"
          style={{ userSelect: 'none', pointerEvents: 'none' }}
          draggable={false}
        />
      </div>

      {/* Champion Details */}
      <div>
        <div className="flex items-center space-x-3 mb-2">
          {/* Position Icon */}
          {positionIconUrl && primaryRole && (
            <Image
              src={positionIconUrl}
              alt={primaryRole}
              width={32}
              height={32}
              className="opacity-80"
              style={{ userSelect: 'none', pointerEvents: 'none' }}
              draggable={false}
            />
          )}

          {/* Champion Name */}
          <h1 className="text-4xl font-bold text-white">{champion.name}</h1>

          {/* Class Icon */}
          {classIconUrl && championClass && (
            <Image
              src={classIconUrl}
              alt={championClass}
              width={32}
              height={32}
              className="opacity-80"
              style={{ userSelect: 'none', pointerEvents: 'none' }}
              draggable={false}
            />
          )}
        </div>

        {/* Champion Title */}
        <p className="text-xl text-orange-300 mb-3">{champion.title}</p>

        {/* Tags */}
        <div className="flex items-center space-x-2 mb-3">
          {primaryRole && (
            <span
              className={`px-3 py-1.5 text-sm rounded-full cursor-pointer hover:scale-105 transition-transform backdrop-blur-sm shadow-lg ${getRoleTagColor(primaryRole)}`}
              onClick={() => router.push(`/champions?role=${primaryRole}`)}
            >
              {primaryRole}
            </span>
          )}
          {championClass && (
            <span
              className={`px-3 py-1.5 text-sm rounded-full cursor-pointer hover:scale-105 transition-transform backdrop-blur-sm shadow-lg ${getClassTagColor(championClass)}`}
              onClick={() => router.push(`/champions?class=${championClass}`)}
            >
              {championClass}
            </span>
          )}
        </div>

        {/* Cost, Skins Count, and Release Date */}
        <div className="space-y-2">
          <div className="flex items-center space-x-4">
            {/* Blue Essence */}
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="flex items-center space-x-2 bg-black/70 backdrop-blur-md rounded-lg px-3 py-2 border border-gray-700/50 shadow-lg cursor-help">
                  <Image
                    src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-navigation/global/default/rewardicons_blueessence.png"
                    alt="Blue Essence"
                    width={20}
                    height={20}
                    className="w-5 h-5"
                    style={{ userSelect: 'none', pointerEvents: 'none' }}
                    draggable={false}
                  />
                  <span className="text-cyan-400 font-medium drop-shadow-md">
                    {championData.championInfo.costBE.toLocaleString()}
                  </span>
                </div>
              </TooltipTrigger>
              <TooltipContent side="top" sideOffset={2}>
                <div className="text-center">
                  <div className="font-semibold text-cyan-300">Blue Essence</div>
                  <div className="text-xs text-gray-300 mt-1">
                    Primary in-game currency earned from battle pass
                  </div>
                </div>
              </TooltipContent>
            </Tooltip>

            {/* RP */}
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="flex items-center space-x-2 bg-black/70 backdrop-blur-md rounded-lg px-3 py-2 border border-gray-700/50 shadow-lg cursor-help">
                  <Image
                    src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg"
                    alt="Riot Points"
                    width={20}
                    height={20}
                    className="w-5 h-5"
                    style={{ userSelect: 'none', pointerEvents: 'none' }}
                    draggable={false}
                  />
                  <span className="text-yellow-400 font-medium drop-shadow-md">{championData.championInfo.costRP}</span>
                </div>
              </TooltipTrigger>
              <TooltipContent side="top" sideOffset={2}>
                <div className="text-center">
                  <div className="font-semibold text-yellow-300">Riot Points (RP)</div>
                  <div className="text-xs text-gray-300 mt-1">
                    Premium currency for instant unlocks
                  </div>
                </div>
              </TooltipContent>
            </Tooltip>

            {/* Skin Count */}
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="flex items-center space-x-2 bg-black/70 backdrop-blur-md rounded-lg px-3 py-2 border border-gray-700/50 shadow-lg cursor-help">
                  <Image
                    src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/skins_rewards.svg"
                    alt="Skins"
                    width={20}
                    height={20}
                    className="w-5 h-5"
                    style={{ userSelect: 'none', pointerEvents: 'none' }}
                    draggable={false}
                  />
                  <span style={{ color: '#dfcda8' }} className="font-medium drop-shadow-md">{championData.skins.filter((skin: any) => !skin.isBase).length}</span>
                </div>
              </TooltipTrigger>
              <TooltipContent side="top" sideOffset={2}>
                <div className="text-center">
                  <div className="font-semibold" style={{ color: '#dfcda8' }}>Champion Skins</div>
                  <div className="text-xs text-gray-300 mt-1">
                    Total skins available (excluding base skin)
                  </div>
                </div>
              </TooltipContent>
            </Tooltip>
          </div>
          <div className="flex items-center space-x-2 bg-black/70 backdrop-blur-md rounded-lg px-3 py-2 w-fit border border-gray-700/50 shadow-lg">
            <span className="text-gray-300 text-sm drop-shadow-md">
              Released: {championData.championInfo.releaseDate}
            </span>
          </div>
        </div>
      </div>
      </div>

      {/* Win Rate Graph positioned under the champion info - Locked/Coming Soon - Hidden on mobile */}
      <div className="mt-6 relative hidden md:block">
        <div className="blur-sm pointer-events-none">
          <ChampionWinRateGraph championName={champion.name} />
        </div>
        {/* Coming Soon Overlay */}
        <div className="absolute inset-0 flex items-center justify-center bg-black/60 backdrop-blur-sm rounded-lg border border-orange-700/40">
          <div className="text-center">
            <div className="text-4xl mb-2">🔒</div>
            <h3 className="text-xl font-bold text-white mb-1">Coming Soon</h3>
            <p className="text-gray-300 text-sm">Champion win rate feature is under development</p>
          </div>
        </div>
      </div>
    </div>
  )
}
