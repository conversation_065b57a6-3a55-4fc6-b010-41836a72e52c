# Universal Champion Card Component

The `UniversalChampionCard` is a reusable component that displays League of Legends champion information in a consistent format across all pages.

## Features

- **Automatic Icon Placement**: Role and class icons are automatically positioned in the top-right corner based on champion data
- **Consistent Styling**: Unified design that matches the website's theme
- **Interactive Tags**: Clickable role and class tags with customizable callbacks
- **Multiple Variants**: Support for different image types (square tiles or loading splash art)
- **Customizable Colors**: Configurable border and hover colors
- **Responsive Design**: Works across all screen sizes
- **Loading States**: Built-in error handling and fallback images

## Usage

```tsx
import UniversalChampionCard from "@/components/champion/universal-champion-card"

// Basic usage
<UniversalChampionCard champion={championData} />

// With custom styling and callbacks
<UniversalChampionCard
  champion={championData}
  variant="loading-image"
  borderColor="orange-700/20"
  hoverColor="orange-400/50"
  onRoleClick={(role) => handleRoleFilter(role)}
  onClassClick={(className) => handleClassFilter(className)}
/>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `champion` | `UniversalChampionData` | Required | Champion data object |
| `variant` | `'default' \| 'loading-image'` | `'loading-image'` | Image type to display |
| `borderColor` | `string` | `'gray-700/20'` | Tailwind border color class |
| `hoverColor` | `string` | `'blue-500/50'` | Tailwind hover border color class |
| `onRoleClick` | `(role: string) => void` | `undefined` | Callback when role tag is clicked |
| `onClassClick` | `(className: string) => void` | `undefined` | Callback when class tag is clicked |
| `className` | `string` | `''` | Additional CSS classes |

## Champion Data Structure

The component expects champion data in the `UniversalChampionData` format:

```typescript
interface UniversalChampionData {
  id: string
  name: string
  title?: string
  role: string        // Top, Jungle, Middle, Bottom, Support
  class: string       // Fighter, Tank, Mage, Assassin, Marksman, Support
  difficulty: number  // 1-10
  image: string       // Square champion image URL
  slug: string        // URL slug for champion page
  tags?: string[]     // Champion tags from API
}
```

## Automatic Icon Mapping

The component automatically displays the correct icons based on champion data:

### Role Icons (Top-Right Corner, First Position)
- **Top**: Red sword icon
- **Jungle**: Green tree icon  
- **Middle**: Blue tower icon
- **Bottom**: Orange arrow icon
- **Support**: Yellow shield icon

### Class Icons (Top-Right Corner, Second Position)
- **Fighter**: Red crossed swords
- **Tank**: Gray shield
- **Mage**: Purple staff
- **Assassin**: Pink dagger
- **Marksman**: Blue bow
- **Support**: Yellow heart

## Tag Colors

Role and class tags use consistent color schemes:

### Role Tag Colors
- **Top**: Red theme
- **Jungle**: Green theme
- **Middle**: Blue theme
- **Bottom**: Orange theme
- **Support**: Yellow theme

### Class Tag Colors
- **Fighter**: Red theme
- **Tank**: Gray theme
- **Mage**: Purple theme
- **Assassin**: Pink theme
- **Marksman**: Blue theme
- **Support**: Yellow theme

## Image Variants

### Default Variant
Uses the square champion tile image from the `image` property.

### Loading Image Variant (Recommended)
Uses the champion loading splash art from DDragon API:
- URL pattern: `https://ddragon.leagueoflegends.com/cdn/img/champion/loading/{championId}_0.jpg`
- Automatically falls back to square image if loading image fails
- Displays from top of image (object-top) to show champion's face

## Integration Examples

### Free Rotation Page
```tsx
<UniversalChampionCard
  champion={champion}
  variant="loading-image"
  borderColor="green-700/20"
  hoverColor="green-400/50"
  onRoleClick={(role) => setSelectedRole(role)}
  onClassClick={(className) => setSelectedClass(className)}
/>
```

### All Champions Page
```tsx
<UniversalChampionCard
  champion={champion}
  variant="loading-image"
  borderColor="orange-700/20"
  hoverColor="orange-400/50"
  onRoleClick={(role) => updateFilters({ role })}
  onClassClick={(className) => setClassFilter(className)}
/>
```

### Champions Homepage
```tsx
<UniversalChampionCard
  champion={champion}
  variant="loading-image"
  borderColor="orange-700/20"
  hoverColor="orange-400/50"
  onRoleClick={(role) => window.location.href = `/champions?role=${role}`}
  onClassClick={(className) => window.location.href = `/champions?class=${className}`}
/>
```

## Migration Complete

All champion pages now use the `UniversalChampionCard` component for consistent design and functionality across:

- Champions homepage (`/champions`)
- All Champions page (`/champions`)
- Free Champion Rotation page (`/champions/free-rotation`)

Legacy components have been removed to ensure true universality.

## Data Normalization

Use the `normalizeChampionData` utility function to convert API data to the expected format:

```tsx
import { normalizeChampionData } from "@/lib/utils/champion-data-utils"

const normalizedChampion = normalizeChampionData(apiChampionData)
```

This ensures consistent data structure across all champion displays.
