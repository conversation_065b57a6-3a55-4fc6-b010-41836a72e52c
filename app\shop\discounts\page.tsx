"use client"

import { useState } from "react"
import SharedLayout from "@/components/shared-layout"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { SelectItemRightCheck } from "@/components/ui/select-item-right-check"
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Slider } from "@/components/ui/slider"
import { DiscountTypeCombobox } from "@/components/ui/discount-type-combobox"
import { RemoveFiltersIcon } from "@/components/ui/remove-filters-icon"
import { AlertCircle, Search, X, ChevronDown } from "lucide-react"
import { FilterIcon } from "@/components/ui/filter-icon"
import Image from "next/image"

import { useDiscounts, useDiscountFilters } from "@/hooks/use-discounts"
import { formatTimeRemaining } from "@/lib/utils/discount-utils"
import DiscountCard, { DiscountCardSkeleton } from "@/components/discount/discount-card"

export default function DiscountsPage() {
  const { discounts, loading, error, stats, timeRemaining, refreshDiscounts } = useDiscounts()
  const {
    filteredDiscounts,
    filterType,
    setFilterType,
    searchTerm,
    setSearchTerm,
    priceRange,
    setPriceRange,
    maxPrice,
    sortBy,
    setSortBy,
    sortOrder,
    setSortOrder,
    resetFilters
  } = useDiscountFilters(discounts)

  // Mobile search panel state
  const [isSearchPanelOpen, setIsSearchPanelOpen] = useState(false)

  // Mobile search button
  const searchButton = (
    <Button
      onClick={() => setIsSearchPanelOpen(true)}
      variant="outline"
      size="sm"
      className="border-amber-700/30 text-amber-400 hover:bg-amber-400/10 hover:border-amber-400/50 p-2"
    >
      <FilterIcon className="h-4 w-4" />
    </Button>
  )

  return (
    <SharedLayout>
      <div className="container mx-auto px-4 sm:px-6 md:px-8 py-0 mobile-no-top-padding max-w-full overflow-hidden">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <Image
                src="https://i.ibb.co/CKq8JQ0D/Discounts.png"
                alt="Discounts"
                width={32}
                height={32}
                className="h-8 w-8"
              />
              <h1 className="text-4xl font-bold text-white">Shop Discounts</h1>
            </div>
            <div className="flex items-center space-x-3">
              {/* Mobile Search Button - Next to Title */}
              <div className="lg:hidden">
                {searchButton}
              </div>
              {/* Desktop Countdown Timer */}
              <div className="hidden lg:flex items-center gap-2 text-amber-400 font-semibold">
                <Image
                  src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/clock-icon-gold1.svg"
                  alt="Clock"
                  width={20}
                  height={20}
                  className="w-5 h-5"
                />
                <span>
                  {loading ? 'Loading...' :
                   stats.total > 0 ? `Discounts End in ${formatTimeRemaining(timeRemaining)}` : 'No active discounts'}
                </span>
              </div>
            </div>
          </div>

          {/* Mobile Countdown Timer - Under Title */}
          <div className="lg:hidden mb-4">
            <div className="flex items-center gap-2 text-amber-400 font-semibold">
              <Image
                src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/clock-icon-gold1.svg"
                alt="Clock"
                width={20}
                height={20}
                className="w-5 h-5"
              />
              <span>
                {loading ? 'Loading...' :
                 stats.total > 0 ? `Discounts End in ${formatTimeRemaining(timeRemaining)}` : 'No active discounts'}
              </span>
            </div>
          </div>

          <p className="text-gray-300 text-lg">
            {stats.total > 0
              ? `Limited time offers - Up to 60% off!`
              : "Limited time offers - Up to 60% off!"
            }
          </p>
        </div>

        {/* Error State */}
        {error && (
          <div className="mb-8">
            <Card className="bg-red-900/20 border-red-700/50">
              <CardContent className="p-6 text-center">
                <AlertCircle className="h-8 w-8 text-red-400 mx-auto mb-3" />
                <h3 className="text-lg font-semibold text-white mb-2">Failed to Load Discounts</h3>
                <p className="text-red-300 mb-4">{error}</p>
                <Button
                  onClick={refreshDiscounts}
                  className="bg-red-600 hover:bg-red-700"
                >
                  Try Again
                </Button>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Desktop Search and Filters */}
        <div className="hidden lg:block mb-8 space-y-4">
          {/* Main Filters Row with Sort at Far Right */}
          <div className="flex flex-col lg:flex-row gap-4 items-end">
            {/* Left side filters group */}
            <div className="flex flex-col lg:flex-row gap-4 flex-1">
              {/* Search Bar - Reduced width */}
              <div className="relative lg:flex-1 lg:max-w-md">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <Input
                  type="text"
                  placeholder="Search discounts..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-12 bg-gray-900/80 border-gray-700/50 text-white placeholder-gray-400 focus:border-amber-400/60"
                />
              </div>

              {/* Type Filter */}
              <DiscountTypeCombobox
                selectedType={filterType}
                onSelectionChange={setFilterType}
                placeholder="Select Type"
                className="w-full lg:w-48"
              />

              {/* Price Range Dropdown */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full lg:w-48 bg-gray-900/40 border-gray-700/20 text-white hover:bg-gray-800/50 justify-between h-[40px]"
                  >
                    <div className="flex items-center space-x-2">
                      <Image
                        src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg"
                        alt="RP"
                        width={16}
                        height={16}
                        className="w-4 h-4"
                      />
                      <span>
                        {priceRange[0]} - {priceRange[1]} RP
                      </span>
                    </div>
                    <ChevronDown className="h-4 w-4 opacity-50" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  className="w-80 bg-gray-900 border-gray-700 p-4"
                  align="start"
                  side="bottom"
                  sideOffset={5}
                >
                  <div className="space-y-4">
                    {/* From and To inputs */}
                    <div className="grid grid-cols-2 gap-3">
                      <div className="space-y-2">
                        <label className="text-sm font-medium text-gray-300">From</label>
                        <div className="relative">
                          <Image
                            src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg"
                            alt="RP"
                            width={16}
                            height={16}
                            className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4"
                          />
                          <Input
                            type="number"
                            value={priceRange[0]}
                            onChange={(e) => {
                              const value = Math.max(0, parseInt(e.target.value) || 0)
                              setPriceRange([value, priceRange[1]])
                            }}
                            className="pl-10 bg-gray-800/50 border-gray-700/50 text-white [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none [-moz-appearance:textfield]"
                            min={0}
                            max={maxPrice}
                          />
                        </div>
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm font-medium text-gray-300">To</label>
                        <div className="relative">
                          <Image
                            src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg"
                            alt="RP"
                            width={16}
                            height={16}
                            className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4"
                          />
                          <Input
                            type="number"
                            value={priceRange[1]}
                            onChange={(e) => {
                              const value = Math.min(maxPrice, parseInt(e.target.value) || maxPrice)
                              setPriceRange([priceRange[0], value])
                            }}
                            className="pl-10 bg-gray-800/50 border-gray-700/50 text-white [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none [-moz-appearance:textfield]"
                            min={0}
                            max={maxPrice}
                          />
                        </div>
                      </div>
                    </div>

                    {/* Dual Knob Slider */}
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm text-gray-400">
                        <span>0</span>
                        <span>{maxPrice}</span>
                      </div>
                      <Slider
                        value={priceRange}
                        onValueChange={(value) => setPriceRange(value as [number, number])}
                        max={maxPrice}
                        min={0}
                        step={10}
                        className="[&_[role=slider]]:border-amber-400 [&_[role=slider]]:bg-gray-900 [&_[data-orientation=horizontal]]:bg-amber-400/20 [&_[data-orientation=horizontal]>span]:bg-amber-400"
                      />
                    </div>
                  </div>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            {/* Sort & Reset - Far Right */}
            <div className="flex-shrink-0 flex items-center gap-2">
              <Select
                value={`${sortBy}-${sortOrder}`}
                onValueChange={(value) => {
                  const [field, direction] = value.split('-')
                  setSortBy(field as 'discount' | 'price' | 'endDate' | 'name')
                  setSortOrder(direction as 'asc' | 'desc')
                }}
              >
                <SelectTrigger className="w-48 bg-gray-900/40 border-gray-700/20 text-white hover:bg-gray-800/50">
                  <SelectValue placeholder="Sort By" />
                </SelectTrigger>
                <SelectContent className="bg-gray-900 border-gray-700">
                  <SelectItemRightCheck value="discount-desc" className="text-white hover:bg-amber-400/10 focus:bg-amber-400/10">
                    Highest Discount
                  </SelectItemRightCheck>
                  <SelectItemRightCheck value="discount-asc" className="text-white hover:bg-amber-400/10 focus:bg-amber-400/10">
                    Lowest Discount
                  </SelectItemRightCheck>
                  <SelectItemRightCheck value="name-asc" className="text-white hover:bg-amber-400/10 focus:bg-amber-400/10">
                    Name (A–Z)
                  </SelectItemRightCheck>
                  <SelectItemRightCheck value="name-desc" className="text-white hover:bg-amber-400/10 focus:bg-amber-400/10">
                    Name (Z–A)
                  </SelectItemRightCheck>
                  <SelectItemRightCheck value="price-asc" className="text-white hover:bg-amber-400/10 focus:bg-amber-400/10">
                    Price (Low to High)
                  </SelectItemRightCheck>
                  <SelectItemRightCheck value="price-desc" className="text-white hover:bg-amber-400/10 focus:bg-amber-400/10">
                    Price (High to Low)
                  </SelectItemRightCheck>
                </SelectContent>
              </Select>

              {/* Reset Filters Button */}
              <Button
                variant="outline"
                size="sm"
                onClick={resetFilters}
                className="border-gray-700/20 text-gray-300 hover:bg-gray-800/50 hover:text-red-400 p-2 h-10 w-10"
                title="Reset all filters"
              >
                <RemoveFiltersIcon className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Results Count */}
        {!loading && stats.total > 0 && (
          <div className="mb-8 text-gray-400">
            Showing {filteredDiscounts.length} of {stats.total} discounts
          </div>
        )}

        {/* Discount Items */}
        <div className="champion-grid">
          {loading ? (
            // Loading skeletons
            Array.from({ length: 12 }).map((_, index) => (
              <DiscountCardSkeleton key={index} />
            ))
          ) : filteredDiscounts.length > 0 ? (
            // Actual discount cards
            filteredDiscounts.map((discount) => (
              <DiscountCard key={discount.id} discount={discount} />
            ))
          ) : !error ? (
            // No discounts available
            <div className="col-span-full text-center py-12">
              <div className="h-16 w-16 bg-gray-600 rounded-full mx-auto mb-4 flex items-center justify-center">
                <span className="text-2xl">🛒</span>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">No Discounts Available</h3>
              <p className="text-gray-400">Check back later for new deals!</p>
            </div>
          ) : null}
        </div>
      </div>

      {/* Mobile Search Panel */}
      <div className="lg:hidden">
        {/* Backdrop */}
        <div
          className={`fixed inset-0 bg-black/50 z-40 transition-opacity duration-300 ${
            isSearchPanelOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'
          }`}
          onClick={() => setIsSearchPanelOpen(false)}
        />

        {/* Search Panel */}
        <div
          className={`fixed top-0 left-0 right-0 bg-gray-900/95 backdrop-blur-sm border-b border-amber-700/30 z-50 transition-all duration-300 ease-out transform ${
            isSearchPanelOpen ? 'translate-y-0 opacity-100' : '-translate-y-full opacity-0'
          }`}
        >
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-6">
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-white flex items-center">
                <Search className="h-5 w-5 mr-2 text-amber-400" />
                Search & Filters
              </h3>
              <Button
                onClick={() => setIsSearchPanelOpen(false)}
                variant="ghost"
                size="sm"
                className="text-gray-400 hover:text-white"
              >
                <X className="h-5 w-5" />
              </Button>
            </div>

            {/* Mobile Filters */}
            <div className="space-y-6">
              {/* Search */}
              <div>
                <label className="block text-sm font-medium text-white mb-2">Search</label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    type="text"
                    placeholder="Search discounts..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 bg-gray-900/40 border-amber-700/30 text-white placeholder-gray-400 focus:border-gray-700/50 focus:ring-0 focus-visible:ring-0 focus-visible:ring-offset-0"
                  />
                </div>
              </div>

              {/* Type Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-300">Type</label>
                <DiscountTypeCombobox
                  selectedType={filterType}
                  onSelectionChange={setFilterType}
                  placeholder="Select Type"
                  className="w-full"
                />
              </div>

              {/* Price Range */}
              <div>
                <label className="block text-sm font-medium text-white mb-2">Price Range</label>
                <div className="space-y-4">
                  {/* From and To Inputs */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-xs text-gray-400 mb-1">From:</label>
                      <div className="relative">
                        <Image
                          src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg"
                          alt="RP"
                          width={16}
                          height={16}
                          className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4"
                        />
                        <Input
                          type="number"
                          value={priceRange[0]}
                          onChange={(e) => {
                            const value = Math.max(0, Math.min(parseInt(e.target.value) || 0, maxPrice))
                            setPriceRange([value, priceRange[1]])
                          }}
                          className="pl-10 bg-gray-800/40 border-amber-700/30 text-white focus:border-gray-700/50 focus:ring-0 focus-visible:ring-0 focus-visible:ring-offset-0 [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none [-moz-appearance:textfield]"
                          min={0}
                          max={maxPrice}
                        />
                      </div>
                    </div>
                    <div>
                      <label className="block text-xs text-gray-400 mb-1">To:</label>
                      <div className="relative">
                        <Image
                          src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg"
                          alt="RP"
                          width={16}
                          height={16}
                          className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4"
                        />
                        <Input
                          type="number"
                          value={priceRange[1]}
                          onChange={(e) => {
                            const value = Math.max(priceRange[0], Math.min(parseInt(e.target.value) || maxPrice, maxPrice))
                            setPriceRange([priceRange[0], value])
                          }}
                          className="pl-10 bg-gray-800/40 border-amber-700/30 text-white focus:border-gray-700/50 focus:ring-0 focus-visible:ring-0 focus-visible:ring-offset-0 [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none [-moz-appearance:textfield]"
                          min={priceRange[0]}
                          max={maxPrice}
                        />
                      </div>
                    </div>
                  </div>

                  {/* Dual Knob Slider */}
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm text-gray-400">
                      <div className="flex items-center gap-1">
                        <Image
                          src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg"
                          alt="RP"
                          width={12}
                          height={12}
                          className="w-3 h-3"
                        />
                        <span>0</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Image
                          src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg"
                          alt="RP"
                          width={12}
                          height={12}
                          className="w-3 h-3"
                        />
                        <span>{maxPrice}</span>
                      </div>
                    </div>
                    <Slider
                      value={priceRange}
                      onValueChange={(value) => setPriceRange(value as [number, number])}
                      max={maxPrice}
                      min={0}
                      step={10}
                      className="[&_[role=slider]]:border-amber-400 [&_[role=slider]]:bg-gray-900 [&_[data-orientation=horizontal]]:bg-amber-400/20 [&_[data-orientation=horizontal]>span]:bg-amber-400"
                    />
                  </div>
                </div>
              </div>

              {/* Sort By Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-300">Sort By</label>
                <Select
                  value={`${sortBy}-${sortOrder}`}
                  onValueChange={(value) => {
                    const [field, direction] = value.split('-')
                    setSortBy(field as 'discount' | 'price' | 'endDate' | 'name')
                    setSortOrder(direction as 'asc' | 'desc')
                  }}
                >
                  <SelectTrigger className="w-full bg-gray-800/50 border-gray-700/50 text-white focus:border-amber-400/60 h-12">
                    <SelectValue placeholder="Sort By" />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-900 border-gray-700 max-h-60 overflow-y-auto" position="popper" sideOffset={4}>
                    <SelectItem value="discount-desc" className="text-white hover:bg-amber-400/10">
                      Highest Discount
                    </SelectItem>
                    <SelectItem value="discount-asc" className="text-white hover:bg-amber-400/10">
                      Lowest Discount
                    </SelectItem>
                    <SelectItem value="name-asc" className="text-white hover:bg-amber-400/10">
                      Name (A–Z)
                    </SelectItem>
                    <SelectItem value="name-desc" className="text-white hover:bg-amber-400/10">
                      Name (Z–A)
                    </SelectItem>
                    <SelectItem value="price-asc" className="text-white hover:bg-amber-400/10">
                      Price (Low to High)
                    </SelectItem>
                    <SelectItem value="price-desc" className="text-white hover:bg-amber-400/10">
                      Price (High to Low)
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-3 pt-4">
                <Button
                  onClick={resetFilters}
                  variant="outline"
                  className="border-gray-700/30 text-gray-400 hover:bg-gray-400/10 hover:text-red-400 p-3"
                  title="Reset all filters"
                >
                  <RemoveFiltersIcon className="h-4 w-4" />
                </Button>
                <Button
                  onClick={() => setIsSearchPanelOpen(false)}
                  className="flex-1 bg-amber-600 hover:bg-amber-700"
                >
                  Apply Filters ({filteredDiscounts.length})
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </SharedLayout>
  )
}
