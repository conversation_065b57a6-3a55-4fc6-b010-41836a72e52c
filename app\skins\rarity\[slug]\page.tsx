import SkinsRarityPageClient from "./client"
import { generateSkinRaritySEO } from "@/components/SEO"
import type { Metadata } from 'next'
import { notFound } from "next/navigation"

// Define valid rarities and their display information
const RARITY_CONFIG = {
  regular: {
    name: "Regular",
    icon: "https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-parties/global/default/summoner-icon-rare.png"
  },
  epic: {
    name: "Epic", 
    icon: "https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/cn-gem-3.png"
  },
  legendary: {
    name: "Legendary",
    icon: "https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/legendary.png"
  },
  mythic: {
    name: "Mythic",
    icon: "https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/cn-gem-8.png"
  },
  ultimate: {
    name: "Ultimate",
    icon: "https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/ultimate.png"
  },
  legacy: {
    name: "Legacy",
    icon: "https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/summoner-icon/icon-legacy.png"
  },
  transcendent: {
    name: "Transcendent",
    icon: "https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/transcendent.png"
  },
  exalted: {
    name: "Exalted",
    icon: "https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/exalted.png"
  }
} as const

export async function generateMetadata({ params }: { params: Promise<{ slug: string }> }): Promise<Metadata> {
  const slug = (await params).slug;

  // Check if the rarity is valid
  if (!slug || !(slug in RARITY_CONFIG)) {
    notFound()
  }

  const rarityConfig = RARITY_CONFIG[slug as keyof typeof RARITY_CONFIG]
  const rarityName = rarityConfig.name

  return generateSkinRaritySEO({
    rarity: rarityName,
    slug: slug,
  });
}

export default async function SkinsRarityPage({ params }: { params: Promise<{ slug: string }> }) {
  const slug = (await params).slug;

  // Check if the rarity is valid
  if (!slug || !(slug in RARITY_CONFIG)) {
    notFound()
  }

  return <SkinsRarityPageClient slug={slug} />;
}
