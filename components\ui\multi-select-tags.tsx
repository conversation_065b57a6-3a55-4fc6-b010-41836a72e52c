"use client"

import * as React from "react"
import { Check, ChevronDown, X, Search } from "lucide-react"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Input } from "@/components/ui/input"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Badge } from "@/components/ui/badge"

interface MultiSelectTagsProps {
  selectedTags: string[]
  onSelectionChange: (tags: string[]) => void
  availableTags: string[]
  placeholder?: string
  className?: string
}

// Function to format tags (add spaces between words)
function formatTag(tag: string): string {
  return tag.replace(/([a-z])([A-Z])/g, '$1 $2')
}

export function MultiSelectTags({
  selectedTags,
  onSelectionChange,
  availableTags,
  placeholder = "Select stats...",
  className
}: MultiSelectTagsProps) {
  const [open, setOpen] = React.useState(false)
  const [searchTerm, setSearchTerm] = React.useState("")

  const filteredTags = React.useMemo(() => {
    if (!searchTerm.trim()) return availableTags
    
    return availableTags.filter(tag =>
      formatTag(tag).toLowerCase().includes(searchTerm.toLowerCase())
    )
  }, [availableTags, searchTerm])

  const handleTagToggle = (tag: string) => {
    const newSelection = selectedTags.includes(tag)
      ? selectedTags.filter(t => t !== tag)
      : [...selectedTags, tag]
    
    onSelectionChange(newSelection)
  }

  const handleClearAll = () => {
    onSelectionChange([])
  }

  const handleRemoveTag = (tagToRemove: string) => {
    onSelectionChange(selectedTags.filter(tag => tag !== tagToRemove))
  }

  const renderSelectedBadges = () => {
    if (selectedTags.length === 0) {
      return <span className="text-gray-400">{placeholder}</span>
    }

    if (selectedTags.length === 1) {
      return (
        <Badge 
          variant="secondary" 
          className="bg-blue-600/20 text-blue-300 border-blue-600/30 mr-1"
        >
          {formatTag(selectedTags[0])}
          <X 
            className="ml-1 h-3 w-3 cursor-pointer hover:text-blue-100" 
            onClick={(e) => {
              e.stopPropagation()
              handleRemoveTag(selectedTags[0])
            }}
          />
        </Badge>
      )
    }

    return (
      <div className="flex items-center gap-1">
        <Badge 
          variant="secondary" 
          className="bg-blue-600/20 text-blue-300 border-blue-600/30"
        >
          {formatTag(selectedTags[0])}
          <X 
            className="ml-1 h-3 w-3 cursor-pointer hover:text-blue-100" 
            onClick={(e) => {
              e.stopPropagation()
              handleRemoveTag(selectedTags[0])
            }}
          />
        </Badge>
        {selectedTags.length > 1 && (
          <span className="text-blue-300 text-sm">
            +{selectedTags.length - 1}
          </span>
        )}
      </div>
    )
  }

  return (
    <div className={className}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between bg-gray-900/40 border-gray-700/20 text-white hover:bg-gray-800/50 h-[40px] items-center"
          >
            <div className="flex-1 text-left">
              {renderSelectedBadges()}
            </div>
            <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0 bg-gray-900 border-gray-700" align="start">
          <div className="p-3">
            <div className="flex items-center justify-between mb-3">
              <span className="text-sm font-medium text-gray-300">Select Tags</span>
              {selectedTags.length > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleClearAll}
                  className="text-xs text-gray-400 hover:text-white h-6"
                >
                  Clear All
                </Button>
              )}
            </div>

            {/* Search Input */}
            <div className="relative mb-3">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                type="text"
                placeholder="Search stats..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-gray-800/50 border-gray-600/50 text-white placeholder-gray-400 focus:border-blue-400/60"
              />
            </div>

            <div className="space-y-1 max-h-60 overflow-y-auto">
              {filteredTags.length === 0 ? (
                <div className="text-center py-4 text-gray-400 text-sm">
                  No tags found
                </div>
              ) : (
                filteredTags.map((tag) => (
                  <div
                    key={tag}
                    className="flex items-center space-x-3 p-1 rounded hover:bg-gray-800/50 cursor-pointer"
                    onClick={() => handleTagToggle(tag)}
                  >
                    <Checkbox
                      checked={selectedTags.includes(tag)}
                      onCheckedChange={() => handleTagToggle(tag)}
                      className="border-gray-600 data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
                    />
                    <span className="text-gray-300">{formatTag(tag)}</span>
                  </div>
                ))
              )}
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  )
}
