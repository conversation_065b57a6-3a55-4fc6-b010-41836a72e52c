// API route for individual champion details

import { NextRequest, NextResponse } from 'next/server'

interface RouteParams {
  params: {
    championId: string
  }
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { championId } = await params
    
    if (!championId) {
      return NextResponse.json({
        success: false,
        error: 'Champion ID is required'
      }, { status: 400 })
    }
    
    
    
    // Get current game version
    const versionsResponse = await fetch('https://ddragon.leagueoflegends.com/api/versions.json')
    const versions = await versionsResponse.json()
    const currentVersion = versions[0]
    
    // Get champion detail data
    const url = `https://ddragon.leagueoflegends.com/cdn/${currentVersion}/data/en_US/champion/${championId}.json`
    const response = await fetch(url)
    
    if (!response.ok) {
      return NextResponse.json({
        success: false,
        error: 'Champion not found',
        championId
      }, { status: 404 })
    }
    
    const data = await response.json()
    const championData = data.data[championId]
    
    if (!championData) {
      return NextResponse.json({
        success: false,
        error: 'Champion not found',
        championId
      }, { status: 404 })
    }
    
    
    
    
    
    

    return NextResponse.json({
      success: true,
      data: championData,
      gameVersion: currentVersion,
      timestamp: new Date().toISOString()
    }, {
      headers: {
        'Cache-Control': 'public, s-maxage=3600, stale-while-revalidate=86400',
        'CDN-Cache-Control': 'public, s-maxage=3600',
        'Vercel-CDN-Cache-Control': 'public, s-maxage=3600'
      }
    })
    
  } catch (error) {
    const { championId } = await params
    console.error(`❌ Error fetching champion ${championId}:`, error)

    return NextResponse.json({
      success: false,
      error: 'Failed to fetch champion data',
      message: error instanceof Error ? error.message : 'Unknown error',
      championId: championId
    }, {
      status: 500,
      headers: {
        'Cache-Control': 'no-cache'
      }
    })
  }
}
