import SharedLayout from "@/components/shared-layout";
import { leagueApi } from "@/lib/api/league-client";
import { getChampionDetails } from "@/lib/services/champion-service";
import { generateChampionSEO } from "@/components/SEO";
import ChampionSlugClient from "./client";
import type { Metadata } from 'next';

export async function generateStaticParams() {
  const champions = await leagueApi.getChampions();

  return Object.keys(champions).map((championId) => ({
    slug: championId,
  }));
}

export async function generateMetadata({ params }: { params: Promise<{ slug: string }> }): Promise<Metadata> {
  try {
    const slug = (await params).slug;
    const { champion, transformedData } = await getChampionDetails(slug);

    // Generate champion splash image URL for SEO
    const splashImageUrl = `https://ddragon.leagueoflegends.com/cdn/img/champion/splash/${champion.id}_0.jpg`;

    return generateChampionSEO({
      name: champion.name,
      title: champion.title,
      role: transformedData.championInfo.role,
      description: `Learn about ${champion.name}, ${champion.title} in League of Legends. View ${champion.name}'s abilities, skins, stats, lore, matchups, and more on LoLDB.`,
      image: splashImageUrl,
    });
  } catch (error) {
    // Fallback metadata if champion data fails to load
    return generateChampionSEO({
      name: 'Champion',
      title: 'League of Legends Champion',
      description: 'Explore champion details, abilities, skins, and more on LoLDB.',
    });
  }
}

export default async function ChampionSlugPage({ params }: { params: Promise<{ slug: string }> }) {
  const slug = (await params).slug;
  try {
    const { champion, transformedData, backgroundImage } = await getChampionDetails(slug);

    return <ChampionSlugClient champion={champion} championData={transformedData} backgroundImage={backgroundImage} />
  } catch (error) {
    console.error(error);
    return (
      <SharedLayout>
        <div className="container mx-auto px-8 py-2">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <div className="text-red-400 text-6xl mb-4">⚠️</div>
              <h1 className="text-2xl font-bold text-white mb-2">
                Champion Not Found
              </h1>
              <p className="text-gray-300 mb-4">
                {error instanceof Error ? error.message : `Champion "${slug}" could not be loaded.`}
              </p>
              <div className="space-x-4">
                <a
                  href="/champions"
                  className="inline-block bg-orange-600 hover:bg-orange-700 text-white px-6 py-2 rounded-lg transition-colors"
                >
                  Browse All Champions
                </a>
              </div>
            </div>
          </div>
        </div>
      </SharedLayout>
    )
  }
}
