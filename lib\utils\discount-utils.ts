/**
 * Discount utilities for processing and formatting discount data
 */

import { ProcessedDiscountItem } from '@/lib/services/sales-service'

/**
 * Calculate time remaining until discount ends
 */
export function calculateTimeRemaining(endDate: string): {
  days: number
  hours: number
  minutes: number
  seconds: number
  totalMs: number
  expired: boolean
} {
  const now = new Date().getTime()
  const end = new Date(endDate).getTime()
  const difference = end - now

  if (difference <= 0) {
    return {
      days: 0,
      hours: 0,
      minutes: 0,
      seconds: 0,
      totalMs: 0,
      expired: true
    }
  }

  return {
    days: Math.floor(difference / (1000 * 60 * 60 * 24)),
    hours: Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
    minutes: Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60)),
    seconds: Math.floor((difference % (1000 * 60)) / 1000),
    totalMs: difference,
    expired: false
  }
}

/**
 * Format time remaining as a readable string
 */
export function formatTimeRemaining(timeRemaining: ReturnType<typeof calculateTimeRemaining>): string {
  if (timeRemaining.expired) {
    return 'Expired'
  }

  const { days, hours, minutes } = timeRemaining

  if (days > 0) {
    return `${days} day${days !== 1 ? 's' : ''}, ${hours} hour${hours !== 1 ? 's' : ''}`
  } else if (hours > 0) {
    return `${hours} hour${hours !== 1 ? 's' : ''}, ${minutes} minute${minutes !== 1 ? 's' : ''}`
  } else {
    return `${minutes} minute${minutes !== 1 ? 's' : ''}`
  }
}

/**
 * Format price with currency
 */
export function formatPrice(price: number, currency: string): string {
  if (currency === 'RP') {
    return `${price} RP`
  } else if (currency === 'ME') {
    return `${price} ME`
  } else if (currency === 'BE') {
    return `${price} BE`
  }
  return `${price} ${currency}`
}



/**
 * Sort discounts by various criteria
 */
export function sortDiscounts(
  discounts: ProcessedDiscountItem[],
  sortBy: 'discount' | 'price' | 'endDate' | 'name' = 'discount',
  order: 'asc' | 'desc' = 'desc'
): ProcessedDiscountItem[] {
  return [...discounts].sort((a, b) => {
    let comparison = 0

    switch (sortBy) {
      case 'discount':
        comparison = a.discount - b.discount
        break
      case 'price':
        comparison = a.salePrice - b.salePrice
        break
      case 'endDate':
        comparison = new Date(a.endDate).getTime() - new Date(b.endDate).getTime()
        break
      case 'name':
        const nameA = a.skinName || a.championName
        const nameB = b.skinName || b.championName
        comparison = nameA.localeCompare(nameB)
        break
    }

    return order === 'desc' ? -comparison : comparison
  })
}

/**
 * Filter discounts by type
 */
export function filterDiscountsByType(
  discounts: ProcessedDiscountItem[],
  type?: 'CHAMPION_SKIN' | 'CHAMPION' | 'ALL'
): ProcessedDiscountItem[] {
  if (!type || type === 'ALL') {
    return discounts
  }
  return discounts.filter(discount => discount.type === type)
}

/**
 * Get discount statistics
 */
export function getDiscountStats(discounts: ProcessedDiscountItem[]) {
  const total = discounts.length
  const skins = discounts.filter(d => d.type === 'CHAMPION_SKIN').length
  const champions = discounts.filter(d => d.type === 'CHAMPION').length

  const maxDiscount = Math.max(...discounts.map(d => d.discount), 0)
  const avgDiscount = total > 0 ? Math.round(discounts.reduce((sum, d) => sum + d.discount, 0) / total) : 0

  // Find the earliest end date (only if we have discounts)
  let timeRemaining = {
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0,
    totalMs: 0,
    expired: true
  }
  let earliestEndDate = ''

  if (total > 0) {
    const endDates = discounts.map(d => new Date(d.endDate).getTime()).filter(date => !isNaN(date))
    if (endDates.length > 0) {
      const earliestEndDateMs = Math.min(...endDates)
      earliestEndDate = new Date(earliestEndDateMs).toISOString()
      timeRemaining = calculateTimeRemaining(earliestEndDate)
    }
  }

  return {
    total,
    skins,
    champions,
    maxDiscount,
    avgDiscount,
    timeRemaining,
    earliestEndDate
  }
}

/**
 * Generate champion card data from discount item
 */
export function discountToChampionCardData(discount: ProcessedDiscountItem) {
  return {
    id: discount.championId.toString(),
    name: discount.championName,
    slug: discount.championSlug,
    image: discount.image,
    role: 'Unknown', // We don't have role data from sales API
    class: 'Unknown', // We don't have class data from sales API
    // Add discount-specific data
    discountData: {
      originalPrice: discount.originalPrice,
      salePrice: discount.salePrice,
      currency: discount.currency,
      discount: discount.discount,
      endDate: discount.endDate,
      type: discount.type,
      skinName: discount.skinName
    }
  }
}

/**
 * Get price range from discounts
 */
export function getPriceRange(discounts: ProcessedDiscountItem[]): {
  min: number
  max: number
} {
  if (discounts.length === 0) {
    return { min: 0, max: 1000 }
  }

  const prices = discounts.map(d => d.salePrice)
  return {
    min: Math.min(...prices),
    max: Math.max(...prices)
  }
}

/**
 * Filter discounts by search term
 */
export function filterDiscountsBySearch(
  discounts: ProcessedDiscountItem[],
  searchTerm: string
): ProcessedDiscountItem[] {
  if (!searchTerm.trim()) {
    return discounts
  }

  const term = searchTerm.toLowerCase()
  return discounts.filter(discount => {
    const championName = discount.championName.toLowerCase()
    const skinName = discount.skinName?.toLowerCase() || ''

    return championName.includes(term) || skinName.includes(term)
  })
}

/**
 * Filter discounts by price range
 */
export function filterDiscountsByPriceRange(
  discounts: ProcessedDiscountItem[],
  minPrice: number,
  maxPrice: number
): ProcessedDiscountItem[] {
  return discounts.filter(discount =>
    discount.salePrice >= minPrice && discount.salePrice <= maxPrice
  )
}


