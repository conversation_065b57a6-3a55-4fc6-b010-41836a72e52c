"use client"

import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Skeleton } from "@/components/ui/skeleton"
import Image from "next/image"
import { leagueApiBrowser } from "@/lib/api/league-client-browser"
import { useEffect, useState, useRef, useMemo } from "react"
import { ChevronDown } from "lucide-react"

// Function to get base stat icon URLs
function getStatIconUrl(statName: string): string {
  const statIcons: Record<string, string> = {
    'health': 'https://raw.communitydragon.org/latest/game/assets/perks/statmods/statmodshealthscalingicon.png',
    'mana': 'https://i.ibb.co/0VFpT40y/Frame-3-1.png',
    'attackDamage': 'https://raw.communitydragon.org/latest/game/assets/perks/statmods/statmodsattackdamageicon.png',
    'attackSpeed': 'https://raw.communitydragon.org/latest/game/assets/perks/statmods/statmodsattackspeedicon.png',
    'armor': 'https://raw.communitydragon.org/latest/game/assets/perks/statmods/statmodsarmoricon.png',
    'magicResist': 'https://raw.communitydragon.org/latest/game/assets/perks/statmods/statmodsmagicresicon.png'
  }
  return statIcons[statName] || ''
}

// Mock matchup data - in a real app this would come from an API
function getMatchupData(championName: string) {
  // Sample champions for matchups
  const sampleChampions = [
    'Aatrox', 'Ahri', 'Akali', 'Alistar', 'Amumu', 'Anivia', 'Annie', 'Ashe', 'Azir', 'Bard',
    'Blitzcrank', 'Brand', 'Braum', 'Caitlyn', 'Camille', 'Cassiopeia', 'Chogath', 'Corki', 'Darius', 'Diana',
    'Draven', 'DrMundo', 'Ekko', 'Elise', 'Evelynn', 'Ezreal', 'Fiddlesticks', 'Fiora', 'Fizz', 'Galio',
    'Gangplank', 'Garen', 'Gnar', 'Gragas', 'Graves', 'Hecarim', 'Heimerdinger', 'Illaoi', 'Irelia', 'Ivern',
    'Janna', 'JarvanIV', 'Jax', 'Jayce', 'Jhin', 'Jinx', 'Kaisa', 'Kalista', 'Karma', 'Karthus',
    'Kassadin', 'Katarina', 'Kayle', 'Kayn', 'Kennen', 'Khazix', 'Kindred', 'Kled', 'KogMaw', 'Leblanc',
    'LeeSin', 'Leona', 'Lissandra', 'Lucian', 'Lulu', 'Lux', 'Malphite', 'Malzahar', 'Maokai', 'MasterYi',
    'MissFortune', 'Mordekaiser', 'Morgana', 'Nami', 'Nasus', 'Nautilus', 'Nidalee', 'Nocturne', 'Nunu', 'Olaf',
    'Orianna', 'Ornn', 'Pantheon', 'Poppy', 'Pyke', 'Qiyana', 'Quinn', 'Rakan', 'Rammus', 'RekSai',
    'Renekton', 'Rengar', 'Riven', 'Rumble', 'Ryze', 'Sejuani', 'Senna', 'Seraphine', 'Sett', 'Shaco',
    'Shen', 'Shyvana', 'Singed', 'Sion', 'Sivir', 'Skarner', 'Sona', 'Soraka', 'Swain', 'Sylas',
    'Syndra', 'TahmKench', 'Taliyah', 'Talon', 'Taric', 'Teemo', 'Thresh', 'Tristana', 'Trundle', 'Tryndamere',
    'TwistedFate', 'Twitch', 'Udyr', 'Urgot', 'Varus', 'Vayne', 'Veigar', 'Velkoz', 'Vi', 'Viego',
    'Viktor', 'Vladimir', 'Volibear', 'Warwick', 'Wukong', 'Xayah', 'Xerath', 'XinZhao', 'Yasuo', 'Yone',
    'Yorick', 'Yuumi', 'Zac', 'Zed', 'Ziggs', 'Zilean', 'Zoe', 'Zyra'
  ]

  // Filter out the current champion from the list
  const availableChampions = sampleChampions.filter(champ => champ !== championName)

  // Generate random matchups
  const getRandomChampions = (count: number) => {
    const shuffled = [...availableChampions].sort(() => 0.5 - Math.random())
    return shuffled.slice(0, count)
  }

  return {
    goodAgainst: getRandomChampions(9).map(champ => ({
      champion: champ,
      advantage: Math.floor(Math.random() * 25) + 60 // 60-85% advantage
    })),
    badAgainst: getRandomChampions(9).map(champ => ({
      champion: champ,
      advantage: Math.floor(Math.random() * 25) + 15 // 15-40% advantage (bad for us)
    })),
    synergisesWith: getRandomChampions(9).map(champ => ({
      champion: champ,
      advantage: Math.floor(Math.random() * 20) + 50 // 50-70% win rate when together
    }))
  }
}

// Skeleton component for matchup rows
function MatchupRowSkeleton() {
  return (
    <div className="flex items-center py-1.5 min-w-0 w-full">
      {/* Current Champion Icon Skeleton */}
      <Skeleton className="w-8 h-8 rounded-full flex-shrink-0" />

      {/* Progress Bar Skeleton */}
      <div className="flex-1 mx-2 min-w-0">
        <Skeleton className="h-2.5 w-full rounded-full mb-1" />
        <div className="flex justify-between">
          <Skeleton className="h-3 w-8" />
          <Skeleton className="h-3 w-8" />
        </div>
      </div>

      {/* Opponent Champion Icon Skeleton */}
      <Skeleton className="w-8 h-8 rounded-full flex-shrink-0" />
    </div>
  )
}

// Individual matchup row component (compact style)
interface MatchupRowProps {
  currentChampion: string
  opponent: string
  advantage: number
  type: 'good' | 'bad' | 'synergy'
}

function MatchupRow({ currentChampion, opponent, advantage, type }: MatchupRowProps) {
  const [currentChampionIcon, setCurrentChampionIcon] = useState<string>('')
  const [opponentIcon, setOpponentIcon] = useState<string>('')

  useEffect(() => {
    async function loadIcons() {
      try {
        const currentIcon = await leagueApiBrowser.getChampionCircleUrl(currentChampion, 0)
        const oppIcon = await leagueApiBrowser.getChampionCircleUrl(opponent, 0)
        setCurrentChampionIcon(currentIcon)
        setOpponentIcon(oppIcon)
      } catch (error) {
        console.error('Failed to load champion icons:', error)
        // Fallback to square icons
        setCurrentChampionIcon(`https://ddragon.leagueoflegends.com/cdn/14.23.1/img/champion/${currentChampion}.png`)
        setOpponentIcon(`https://ddragon.leagueoflegends.com/cdn/14.23.1/img/champion/${opponent}.png`)
      }
    }
    loadIcons()
  }, [currentChampion, opponent])

  const getProgressBarColors = () => {
    switch (type) {
      case 'good':
        return { good: '#22c55e', bad: '#ef4444' } // Green for us, red for them
      case 'bad':
        return { good: '#ef4444', bad: '#22c55e' } // Red for us, green for them
      case 'synergy':
        return { good: '#3b82f6', bad: '#374151' } // Blue for win rate, dark gray background
    }
  }

  const colors = getProgressBarColors()
  const ourPercentage = advantage
  const theirPercentage = 100 - advantage

  // For synergy, we show win rate as a single bar
  const isSynergy = type === 'synergy'

  return (
    <div className="flex items-center py-1.5 min-w-0 w-full">
      {/* Current Champion Icon */}
      <div className="w-8 h-8 rounded-full overflow-hidden border-2 border-orange-400/50 flex-shrink-0">
        {currentChampionIcon && (
          <Image
            src={currentChampionIcon}
            alt={currentChampion}
            width={32}
            height={32}
            className="w-full h-full object-cover"
          />
        )}
      </div>

      {/* Progress Bar */}
      <div className="flex-1 mx-2 min-w-0">
        {isSynergy ? (
          // Single color bar for synergy (win rate) with tooltip
          <>
            <div className="h-2.5 bg-gray-700 rounded-full overflow-hidden">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div
                      className="h-full transition-all duration-300 hover:shadow-lg cursor-pointer"
                      style={{
                        width: `${ourPercentage}%`,
                        backgroundColor: colors.good,
                        filter: 'hover:drop-shadow(0 0 8px currentColor)'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.filter = `drop-shadow(0 0 8px ${colors.good})`
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.filter = 'none'
                      }}
                    />
                  </TooltipTrigger>
                  <TooltipContent className="bg-gray-800 border-gray-600 text-white">
                    <p>{Math.round(ourPercentage * 10)} wins out of 1000 team matches</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
            <div className="text-center text-xs text-gray-400 mt-0.5">
              <span>{ourPercentage}% WR</span>
            </div>
          </>
        ) : (
          // Split color bar for good/bad matchups with tooltips
          <>
            <div className="h-2.5 bg-gray-700 rounded-full overflow-hidden flex relative">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div
                      className="h-full transition-all duration-300 cursor-pointer relative hover:brightness-125"
                      style={{
                        width: `${ourPercentage}%`,
                        backgroundColor: colors.good,
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.filter = `drop-shadow(0 0 8px ${colors.good}) brightness(1.25)`
                        e.currentTarget.style.boxShadow = `0 0 12px ${colors.good}`
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.filter = 'none'
                        e.currentTarget.style.boxShadow = 'none'
                      }}
                    />
                  </TooltipTrigger>
                  <TooltipContent className="bg-gray-800 border-gray-600 text-white">
                    <p>{Math.round(ourPercentage * 10)} wins out of 1000 matches</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div
                      className="h-full transition-all duration-300 hover:shadow-lg cursor-pointer relative"
                      style={{
                        width: `${theirPercentage}%`,
                        backgroundColor: colors.bad,
                        filter: 'hover:drop-shadow(0 0 8px currentColor)'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.filter = `drop-shadow(0 0 8px ${colors.bad})`
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.filter = 'none'
                      }}
                    />
                  </TooltipTrigger>
                  <TooltipContent className="bg-gray-800 border-gray-600 text-white">
                    <p>{Math.round(theirPercentage * 10)} wins out of 1000 matches</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
            <div className="flex justify-between text-xs text-gray-400 mt-0.5">
              <span>{ourPercentage}%</span>
              <span>{theirPercentage}%</span>
            </div>
          </>
        )}
      </div>

      {/* Opponent Champion Icon */}
      <div className="w-8 h-8 rounded-full overflow-hidden border-2 border-gray-500/50 flex-shrink-0">
        {opponentIcon && (
          <Image
            src={opponentIcon}
            alt={opponent}
            width={32}
            height={32}
            className="w-full h-full object-cover"
          />
        )}
      </div>
    </div>
  )
}

interface ChampionStatsServerProps {
  championData: any
}

export default function ChampionStatsServer({ championData }: ChampionStatsServerProps) {
  const { championStats, championInfo } = championData
  const [selectedCategory, setSelectedCategory] = useState<'good' | 'bad' | 'synergy'>('good')
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)
  const [isLoadingCategory, setIsLoadingCategory] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  // Base stats data
  const baseStats = [
    { name: 'Health', value: championStats.health, icon: 'health' },
    { name: 'Mana', value: championStats.mana, icon: 'mana' },
    { name: 'Attack Damage', value: championStats.attackDamage, icon: 'attackDamage' },
    { name: 'Attack Speed', value: championStats.attackSpeed.toFixed(2), icon: 'attackSpeed' },
    { name: 'Armor', value: championStats.armor, icon: 'armor' },
    { name: 'Magic Resist', value: championStats.magicResist, icon: 'magicResist' }
  ]

  // Get matchup data for this champion (memoized to prevent regeneration on re-renders)
  const matchupData = useMemo(() => getMatchupData(championInfo.name), [championInfo.name])

  // Get current category data
  const getCurrentCategoryData = () => {
    switch (selectedCategory) {
      case 'good':
        return { data: matchupData.goodAgainst, title: 'Good Against', color: 'green' }
      case 'bad':
        return { data: matchupData.badAgainst, title: 'Bad Against', color: 'red' }
      case 'synergy':
        return { data: matchupData.synergisesWith, title: 'Synergises With', color: 'blue' }
    }
  }

  const currentCategory = getCurrentCategoryData()

  // Handle click outside to close dropdown
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  const categories = [
    { value: 'good' as const, label: 'Good Against', color: 'bg-green-500' },
    { value: 'bad' as const, label: 'Bad Against', color: 'bg-red-500' },
    { value: 'synergy' as const, label: 'Synergises With', color: 'bg-blue-500' }
  ]

  const selectedCategoryData = categories.find(cat => cat.value === selectedCategory)

  return (
    <>
      {/* Champion Overview Title */}
      <h2 className="text-2xl sm:text-3xl font-bold text-center text-white mb-6 sm:mb-8 px-4">Champion Overview</h2>

      {/* Champion Stats */}
      <div className="mb-6 sm:mb-8 grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 lg:gap-8">
        {/* Base Stats */}
        <Card className="bg-black/70 backdrop-blur-md border-orange-700/40 shadow-xl">
          <CardContent className="p-4 sm:p-6">
            <h3 className="text-lg sm:text-xl font-semibold text-white mb-3 sm:mb-4">Base Stats</h3>
            <div className="space-y-3 sm:space-y-4">
              <div className="base-stats-grid grid grid-cols-2 gap-3 sm:gap-4">
                {baseStats.map((stat) => (
                  <div key={stat.name}>
                    <div className="flex items-center space-x-2 text-xs sm:text-sm text-gray-300 mb-1">
                      <Image
                        src={getStatIconUrl(stat.icon)}
                        alt={stat.name}
                        width={20}
                        height={20}
                        className="w-4 h-4 sm:w-5 sm:h-5"
                      />
                      <span className="truncate">{stat.name}</span>
                    </div>
                    <div className="text-base sm:text-lg font-semibold text-white">{stat.value}</div>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Champion Matchups - Single Card with Dropdown and Grid - Locked/Coming Soon */}
        <Card className="bg-black/70 backdrop-blur-md border-orange-700/40 shadow-xl relative">
          <CardContent className="p-4 sm:p-6">
            {/* Blurred Content */}
            <div className="blur-sm pointer-events-none">
              {/* Header with Dropdown */}
              <div className="flex flex-col gap-3 sm:gap-4 mb-4 sm:mb-6">
                <h3 className="text-lg sm:text-xl font-semibold text-white flex items-center min-w-0">
                  <span className={`w-3 h-3 rounded-full mr-2 flex-shrink-0 ${
                    currentCategory.color === 'green' ? 'bg-green-500' :
                    currentCategory.color === 'red' ? 'bg-red-500' : 'bg-blue-500'
                  }`}></span>
                  <span className="truncate">{currentCategory.title}</span>
                </h3>

                <div className="relative w-full sm:w-48 sm:ml-auto" ref={dropdownRef}>
                  <button
                    onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                    className="w-full bg-gray-800/50 border border-gray-600 text-white px-3 py-2.5 sm:py-2 rounded-md flex items-center justify-between hover:bg-gray-700/50 transition-colors touch-target"
                  >
                    <span className="flex items-center min-w-0">
                      <span className={`w-2 h-2 rounded-full mr-2 flex-shrink-0 ${selectedCategoryData?.color}`}></span>
                      <span className="truncate text-sm sm:text-base">{selectedCategoryData?.label}</span>
                    </span>
                    <ChevronDown className={`w-4 h-4 transition-transform flex-shrink-0 ml-2 ${isDropdownOpen ? 'rotate-180' : ''}`} />
                  </button>

                  {isDropdownOpen && (
                    <div className="absolute top-full left-0 right-0 mt-1 bg-gray-800 border border-gray-600 rounded-md shadow-lg z-[9999] overflow-hidden">
                      {categories.map((category) => (
                        <button
                          key={category.value}
                          onClick={() => {
                            if (category.value !== selectedCategory) {
                              setIsLoadingCategory(true)
                              // Small delay to show loading state and prevent layout shift
                              setTimeout(() => {
                                setSelectedCategory(category.value)
                                setIsLoadingCategory(false)
                              }, 200)
                            }
                            setIsDropdownOpen(false)
                          }}
                          className="w-full px-3 py-2.5 sm:py-2 text-left text-white hover:bg-gray-700 flex items-center transition-colors touch-target"
                        >
                          <span className={`w-2 h-2 rounded-full mr-2 flex-shrink-0 ${category.color}`}></span>
                          <span className="truncate text-sm sm:text-base">{category.label}</span>
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              {/* Responsive Grid Layout with Compact Rows - Grid on desktop, single column on mobile */}
              <div className="matchup-grid grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 sm:gap-3 overflow-hidden">
                {isLoadingCategory ? (
                  // Show skeleton loading during category switch to prevent layout shift
                  Array.from({ length: 9 }).map((_, index) => (
                    <MatchupRowSkeleton key={`skeleton-${index}`} />
                  ))
                ) : (
                  currentCategory.data.map((matchup, index) => (
                    <MatchupRow
                      key={`${selectedCategory}-${index}`}
                      currentChampion={championInfo.name}
                      opponent={matchup.champion}
                      advantage={matchup.advantage}
                      type={selectedCategory}
                    />
                  ))
                )}
              </div>
            </div>

            {/* Coming Soon Overlay */}
            <div className="absolute inset-0 flex items-center justify-center bg-black/60 backdrop-blur-sm rounded-lg">
              <div className="text-center">
                <div className="text-4xl mb-2">🔒</div>
                <h3 className="text-xl font-bold text-white mb-1">Coming Soon</h3>
                <p className="text-gray-300 text-sm">Champion matchup data is under development</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </>
  )
}
