import { Card, CardContent } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"

export function ChampionFiltersSkeleton() {
  return (
    <div className="hidden lg:block mb-8 space-y-4">
      {/* Desktop: All in one row */}
      <div className="flex items-center gap-4">
        {/* Search Bar */}
        <div className="relative flex-1">
          <Skeleton className="h-10 w-full" />
        </div>

        {/* Role Section */}
        <div className="flex items-center gap-2">
          <Skeleton className="h-4 w-12" />
          <div className="w-48">
            <Skeleton className="h-10 w-full" />
          </div>
        </div>

        {/* Class Section */}
        <div className="flex items-center gap-2">
          <Skeleton className="h-4 w-14" />
          <div className="w-48">
            <Skeleton className="h-10 w-full" />
          </div>
        </div>

        {/* Reset Filters Button */}
        <Skeleton className="h-10 w-24" />
      </div>

      {/* Selected Filters Display - Don't show in skeleton as it only appears when filters are selected */}
    </div>
  )
}

export function AllChampionsGridSkeleton({ count = 30 }: { count?: number }) {
  return (
    <div className="champion-grid">
      {Array.from({ length: count }).map((_, index) => (
        <Card key={index} className="bg-gray-900/40 border-orange-700/20 w-full min-w-[140px] sm:min-w-[160px] max-w-[160px] sm:max-w-[280px] aspect-[2/3] flex flex-col">
          <CardContent className="p-0 flex flex-col h-full relative overflow-hidden rounded-lg">
            {/* Champion Image Skeleton - Full Card Background */}
            <div className="absolute inset-0 w-full h-full">
              <Skeleton className="w-full h-full" />
            </div>

            {/* Top Shadow Overlay */}
            <div className="absolute inset-x-0 top-0 h-20 bg-gradient-to-b from-black/40 via-black/20 to-transparent pointer-events-none"></div>

            {/* Role and Class Icons Overlay Skeleton */}
            <div className="absolute top-2 right-2 sm:top-3 sm:right-3 flex flex-col space-y-1 sm:space-y-2 z-10">
              <div className="bg-black/60 backdrop-blur-sm rounded-full p-1.5 sm:p-2">
                <Skeleton className="w-4 h-4 sm:w-5 sm:h-5 rounded-full" />
              </div>
              <div className="bg-black/60 backdrop-blur-sm rounded-full p-1.5 sm:p-2">
                <Skeleton className="w-4 h-4 sm:w-5 sm:h-5 rounded-full" />
              </div>
            </div>

            {/* Champion Name Skeleton - Bottom */}
            <div className="absolute bottom-0 left-0 right-0 p-3 sm:p-4 md:p-5 z-10 text-center">
              <Skeleton className="h-4 sm:h-5 md:h-6 w-20 sm:w-24 md:w-28 mx-auto" />
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

export function ChampionDistributionSkeleton() {
  return (
    <div className="mb-8">
      <div className="flex items-center mb-4">
        <Skeleton className="h-5 w-5 mr-2" />
        <Skeleton className="h-6 w-48" />
      </div>
      {/* Desktop: Full row layout */}
      <div className="hidden md:grid md:grid-cols-5 gap-4 lg:gap-6">
        {Array.from({ length: 5 }).map((_, index) => (
          <Card key={index} className="bg-gray-900/40 border-gray-700/20 w-full">
            <CardContent className="p-3 md:p-4 lg:p-5 text-center h-full flex flex-col justify-center min-h-[90px] md:min-h-[100px] lg:min-h-[110px]">
              <div className="flex items-center justify-center mb-1 md:mb-2">
                <Skeleton className="w-6 h-6 md:w-7 md:h-7 lg:w-8 lg:h-8 mr-2" />
                <Skeleton className="h-5 md:h-6 lg:h-7 w-6 md:w-8 lg:w-10" />
              </div>
              <Skeleton className="h-4 md:h-5 lg:h-6 w-8 md:w-10 lg:w-12 mx-auto" />
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Mobile: 3-2 layout (O O O / O O) */}
      <div className="md:hidden flex flex-wrap justify-center gap-3 sm:gap-4">
        {Array.from({ length: 5 }).map((_, index) => (
          <Card key={index} className={`bg-gray-900/40 border-gray-700/20 ${
            index < 3 ? 'flex-1 min-w-[100px] max-w-[120px]' : 'flex-1 min-w-[120px] max-w-[140px]'
          }`}>
            <CardContent className="p-3 sm:p-4 text-center h-full flex flex-col justify-center min-h-[80px] sm:min-h-[90px]">
              <div className="flex items-center justify-center mb-1 sm:mb-2">
                <Skeleton className="w-5 h-5 sm:w-6 sm:h-6 mr-1 sm:mr-2" />
                <Skeleton className="h-4 sm:h-5 w-4 sm:w-6" />
              </div>
              <Skeleton className="h-3 sm:h-4 w-6 sm:w-8 mx-auto" />
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}

export function AllChampionsPageSkeleton() {
  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6 lg:py-8">
      {/* Header Skeleton */}
      <div className="mb-8">
        <div className="flex items-center space-x-3 mb-4">
          <Skeleton className="h-8 w-8" />
          <Skeleton className="h-10 w-48" />
        </div>
        <Skeleton className="h-6 w-96" />
      </div>

      {/* Champion Distribution Skeleton */}
      <ChampionDistributionSkeleton />

      {/* Filters Skeleton */}
      <ChampionFiltersSkeleton />

      {/* Results Count Skeleton */}
      <div className="mb-8 text-gray-400">
        <Skeleton className="h-4 w-48" />
      </div>

      {/* Champions Grid Skeleton */}
      <AllChampionsGridSkeleton count={30} />
    </div>
  )
}
