// Shared tag color utilities for consistent styling across the application

// Role tag colors - consistent across all components
export const getRoleTagColor = (role: string) => {
  const colors = {
    'Top': 'bg-red-600/20 text-red-300 border-red-500/30',
    'Jungle': 'bg-green-600/20 text-green-300 border-green-500/30',
    'Middle': 'bg-blue-600/20 text-blue-300 border-blue-500/30',
    'Bottom': 'bg-orange-600/20 text-orange-300 border-orange-500/30',
    'Support': 'bg-yellow-600/20 text-yellow-300 border-yellow-500/30'
  }
  return colors[role as keyof typeof colors] || 'bg-gray-600/20 text-gray-300 border-gray-500/30'
}

// Class tag colors - consistent across all components
export const getClassTagColor = (className: string) => {
  const colors = {
    'Fighter': 'bg-red-600/20 text-red-300 border-red-500/30',
    'Tank': 'bg-gray-600/20 text-gray-300 border-gray-500/30',
    'Mage': 'bg-purple-600/20 text-purple-300 border-purple-500/30',
    'Assassin': 'bg-pink-600/20 text-pink-300 border-pink-500/30',
    'Marksman': 'bg-blue-600/20 text-blue-300 border-blue-500/30',
    'Support': 'bg-yellow-600/20 text-yellow-300 border-yellow-500/30'
  }
  return colors[className as keyof typeof colors] || 'bg-gray-600/20 text-gray-300 border-gray-500/30'
}
