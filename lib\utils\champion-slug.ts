/**
 * Utility function to convert champion name to proper champion slug for URL using API data
 * This ensures consistent URL generation across the application by using the champion's alias field
 */

interface ChampionData {
  name: string
  alias: string
}

interface ApiResponse {
  success: boolean
  data: ChampionData[]
}

// Cache for champion data to avoid repeated API calls
let championDataCache: ChampionData[] | null = null
let cacheTimestamp: number = 0
const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

/**
 * Fetches champion data from the API and caches it
 */
async function fetchChampionData(): Promise<ChampionData[]> {
  const now = Date.now()
  
  // Return cached data if it's still valid
  if (championDataCache && (now - cacheTimestamp) < CACHE_DURATION) {
    return championDataCache
  }
  
  try {
    const response = await fetch('https://api.loldb.info/api/champions/inventory/minimal')
    if (!response.ok) {
      throw new Error('Failed to fetch champion data')
    }
    
    const data: ApiResponse = await response.json()
    if (!data.success || !data.data) {
      throw new Error('Invalid API response')
    }
    
    // Cache the data
    championDataCache = data.data
    cacheTimestamp = now
    
    return data.data
  } catch (error) {
    console.error('Error fetching champion data:', error)
    throw error
  }
}

/**
 * Converts champion name to proper champion slug for URL using API data
 * @param championName - The display name of the champion (e.g., "Wukong")
 * @returns Promise<string> - The lowercased alias for URL (e.g., "monkeyking")
 */
export async function getChampionSlug(championName: string): Promise<string> {
  try {
    const championData = await fetchChampionData()
    
    // Find the champion by name and get their alias
    const champion = championData.find((champ) => champ.name === championName)
    if (champion && champion.alias) {
      return champion.alias.toLowerCase()
    }
    
    // Fallback: if champion not found, use the old logic
    console.warn(`Champion "${championName}" not found in API, using fallback`)
    return championName.toLowerCase().replace(/[^a-z0-9]/g, '')
  } catch (error) {
    console.error('Error fetching champion data:', error)
    // Fallback: use the old logic if API fails
    return championName.toLowerCase().replace(/[^a-z0-9]/g, '')
  }
}

/**
 * Synchronous version for cases where async is not possible
 * Uses cached data if available, otherwise falls back to simple string manipulation
 * @param championName - The display name of the champion
 * @returns string - The champion slug
 */
export function getChampionSlugSync(championName: string): string {
  // If we have cached data, use it
  if (championDataCache) {
    const champion = championDataCache.find((champ) => champ.name === championName)
    if (champion && champion.alias) {
      return champion.alias.toLowerCase()
    }
  }
  
  // Fallback to simple string manipulation
  return championName.toLowerCase().replace(/[^a-z0-9]/g, '')
}
