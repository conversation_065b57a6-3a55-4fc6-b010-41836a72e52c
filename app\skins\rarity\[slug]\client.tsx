"use client"

import SharedLayout from "@/components/shared-layout"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { SelectItemRightCheck } from "@/components/ui/select-item-right-check"
import { ChampionsCombobox } from "@/components/ui/champions-combobox"
import { SkinTiersCombobox } from "@/components/ui/skin-tiers-combobox"
import { SkinLinesCombobox } from "@/components/ui/skin-lines-combobox"
import { SelectedFiltersDisplay } from "@/components/ui/selected-filters-display"
import { DidYouMean } from "@/components/ui/did-you-mean"
import SkinSearchPanel from "@/components/search/skin-search-panel"
import { Search, Filter } from "lucide-react"
import { FilterIcon } from "@/components/ui/filter-icon"
import Image from "next/image"
import Link from "next/link"
import { useState, useEffect } from "react"
import { useSearchPara<PERSON>, useRouter, useParams } from "next/navigation"
import { useSkins } from "@/hooks/use-skins"
import { useSkinFilters, SkinFilters } from "@/hooks/use-skin-filters"
import { useMythicPrices } from "@/hooks/use-mythic-prices"
import LazySkinGrid from "@/components/skins/lazy-skins-grid"
import { SkinsPageSkeleton } from "@/components/skeletons/skins-skeleton"
import { notFound } from "next/navigation"
import { RemoveFiltersIcon } from "@/components/ui/remove-filters-icon"

// Define valid rarities and their display information
const RARITY_CONFIG = {
  regular: {
    name: "Regular",
    icon: "https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-parties/global/default/summoner-icon-rare.png"
  },
  epic: {
    name: "Epic", 
    icon: "https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/cn-gem-3.png"
  },
  legendary: {
    name: "Legendary",
    icon: "https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/legendary.png"
  },
  mythic: {
    name: "Mythic",
    icon: "https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/cn-gem-8.png"
  },
  ultimate: {
    name: "Ultimate",
    icon: "https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/ultimate.png"
  },
  legacy: {
    name: "Legacy",
    icon: "https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/summoner-icon/icon-legacy.png"
  },
  transcendent: {
    name: "Transcendent",
    icon: "https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/transcendent.png"
  },
  exalted: {
    name: "Exalted",
    icon: "https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/exalted.png"
  }
} as const

interface SkinsRarityPageClientProps {
  slug: string
}

export default function SkinsRarityPageClient({ slug }: SkinsRarityPageClientProps) {
  const searchParams = useSearchParams()
  const router = useRouter()
  
  // Check if the rarity is valid
  if (!slug || !(slug in RARITY_CONFIG)) {
    notFound()
  }
  
  const rarityConfig = RARITY_CONFIG[slug as keyof typeof RARITY_CONFIG]
  const rarityName = rarityConfig.name
  
  const { skins, loading, error, refetch, totalCount } = useSkins()
  const { mythicPrices } = useMythicPrices(skins)
  const {
    filteredSkins,
    filters,
    updateFilters,
    resetFilters,
    availableChampions,
    availableTiers,
    availableSkinLines,
    searchSuggestions,
    hasSearchResults
  } = useSkinFilters(skins, mythicPrices)

  const [isSearchPanelOpen, setIsSearchPanelOpen] = useState(false)
  const [isInitialized, setIsInitialized] = useState(false)

  // Use responsive grid immediately to prevent layout jump
  const isClient = true

  // Initialize with the specific rarity filter pre-selected
  useEffect(() => {
    if (!loading && skins.length > 0 && !isInitialized) {
      updateFilters({ 
        tiers: [rarityName],
        search: '',
        champions: [],
        skinLines: [],
        sortField: 'name',
        sortDirection: 'asc'
      })
      setIsInitialized(true)
    }
  }, [loading, skins.length, rarityName, updateFilters, isInitialized])

  // Handler to remove individual filter items
  const handleRemoveFilter = (filterType: keyof SkinFilters, value: string) => {
    if (filterType === 'tiers') {
      const newTiers = filters.tiers.filter(tier => tier !== value)
      // If removing the main rarity filter (the one this page is for), redirect to /skins
      if (value === rarityName && newTiers.length === 0) {
        router.push('/skins')
        return
      }
      updateFilters({ tiers: newTiers })
    } else if (filterType === 'champions') {
      updateFilters({ champions: filters.champions.filter(champion => champion !== value) })
    } else if (filterType === 'skinLines') {
      updateFilters({ skinLines: filters.skinLines.filter(skinLine => skinLine !== value) })
    }
  }

  // Handler to clear entire filter category
  const handleClearCategory = (filterType: keyof SkinFilters) => {
    if (filterType === 'tiers') {
      // If clearing the tiers category (which includes the main rarity), redirect to /skins
      router.push('/skins')
      return
    } else if (filterType === 'champions') {
      updateFilters({ champions: [] })
    } else if (filterType === 'skinLines') {
      updateFilters({ skinLines: [] })
    }
  }

  // Custom reset handler that redirects to main skins page
  const handleResetFilters = () => {
    resetFilters()
    // Redirect to main skins page since resetting all filters means user wants to see all skins
    router.push('/skins')
  }

  // Handler for "Did You Mean?" suggestion clicks
  const handleSuggestionClick = (suggestion: string) => {
    updateFilters({ search: suggestion })
  }

  // Handle URL-based filtering
  useEffect(() => {
    const championParam = searchParams.get('champion')
    const skinLinesParam = searchParams.get('skinLines')
    const sortParam = searchParams.get('sort')

    if (championParam || skinLinesParam || sortParam) {
      const newFilters: Partial<SkinFilters> = {
        tiers: [rarityName] // Always maintain the rarity filter
      }

      if (championParam) {
        newFilters.champions = [championParam]
      }

      if (skinLinesParam) {
        newFilters.skinLines = [skinLinesParam]
      }

      if (sortParam) {
        const [field, direction] = sortParam.split('-') as [typeof filters.sortField, typeof filters.sortDirection]
        if (field && direction) {
          newFilters.sortField = field
          newFilters.sortDirection = direction
        }
      }

      updateFilters(newFilters)
    }
  }, [searchParams, rarityName, updateFilters])

  // Apply blue background
  useEffect(() => {
    const blueGradient = 'linear-gradient(135deg, hsl(220 50% 8%) 0%, hsl(220 39% 11%) 50%, hsl(215 30% 6%) 100%)'

    document.body.style.background = blueGradient
    document.body.style.backgroundColor = 'hsl(220 39% 11%)'
    document.body.style.backgroundRepeat = 'no-repeat'
    document.documentElement.style.background = blueGradient
    document.documentElement.style.backgroundColor = 'hsl(220 39% 11%)'
    document.documentElement.style.backgroundRepeat = 'no-repeat'
  }, [])

  // Loading state
  if (loading) {
    return (
      <SharedLayout>
        <SkinsPageSkeleton />
      </SharedLayout>
    )
  }

  // Error state
  if (error) {
    return (
      <SharedLayout>
        <div className="container mx-auto px-8 pb-8 no-top-padding">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <div className="relative mx-auto mb-4 w-32 h-32">
                <Image
                  src="https://raw.communitydragon.org/latest/game/assets/loadouts/summoneremotes/champions/amumu/amumu_sad_crying_glow.png"
                  alt="Failed to load glow"
                  width={128}
                  height={128}
                  className="absolute inset-0 opacity-60"
                  unoptimized
                />
                <Image
                  src="https://raw.communitydragon.org/latest/game/assets/loadouts/summoneremotes/champions/amumu/amumu_sad_crying_vfx.png"
                  alt="Failed to load"
                  width={128}
                  height={128}
                  className="relative z-10"
                  unoptimized
                />
              </div>
              <p className="text-red-300 text-lg mb-4">Failed to load skins</p>
              <p className="text-gray-400 mb-6">{error}</p>
              <Button onClick={refetch} className="bg-purple-600 hover:bg-purple-700">
                Try Again
              </Button>
            </div>
          </div>
        </div>
      </SharedLayout>
    )
  }

  return (
    <SharedLayout>
      <div className="container mx-auto px-4 sm:px-6 md:px-8 pb-8 no-top-padding max-w-full overflow-hidden">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <div className="relative w-8 h-8">
                <Image
                  src={rarityConfig.icon}
                  alt={`${rarityName} rarity icon`}
                  width={32}
                  height={32}
                  className="w-8 h-8 object-contain"
                  unoptimized
                />
              </div>
              <h1 className="text-4xl font-bold text-white">{rarityName} Skins</h1>
            </div>
            {/* Mobile Search Button - Next to Title */}
            <div className="lg:hidden">
              <Button
                onClick={() => setIsSearchPanelOpen(true)}
                variant="outline"
                size="sm"
                className="border-purple-700/30 text-purple-400 hover:bg-purple-400/10 hover:border-purple-400/50 p-2"
              >
                <FilterIcon className="h-4 w-4" />
              </Button>
            </div>
          </div>
          <p className="text-gray-300 text-lg">
            Explore {filteredSkins.length} {rarityName.toLowerCase()} rarity skins
          </p>
        </div>

        {/* Search and Filters - Desktop Only */}
        <div className="hidden lg:block mb-8 space-y-4">
          {/* Main Filters Row with Sort at Far Right */}
          <div className="flex flex-col lg:flex-row gap-4 items-end">
            {/* Left side filters group */}
            <div className="flex flex-col lg:flex-row gap-4 flex-1">
              {/* Search Bar - Reduced width */}
              <div className="relative lg:flex-1 lg:max-w-md">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <Input
                  type="text"
                  placeholder="Search skins or champions..."
                  value={filters.search}
                  onChange={(e) => updateFilters({ search: e.target.value })}
                  className="pl-12 bg-gray-900/80 border-gray-700/50 text-white placeholder-gray-400 focus:border-purple-400/60"
                />
              </div>

              {/* Champion Filter */}
              <ChampionsCombobox
                selectedChampions={filters.champions}
                onSelectionChange={(champions) => updateFilters({ champions })}
                availableChampions={availableChampions}
                placeholder="Select Champions"
                className="w-full lg:w-48"
              />

              {/* Skin Lines Filter */}
              <SkinLinesCombobox
                selectedSkinLines={filters.skinLines}
                onSelectionChange={(skinLines) => updateFilters({ skinLines })}
                availableSkinLines={availableSkinLines}
                placeholder="All Skin Lines"
                className="w-full lg:w-48"
              />
            </div>

            {/* Alphabetical Filter & Reset - Far Right */}
            <div className="flex-shrink-0 flex items-center gap-2">
              <Select
                value={`${filters.sortField}-${filters.sortDirection}`}
                onValueChange={(value) => {
                  const [field, direction] = value.split('-') as [typeof filters.sortField, typeof filters.sortDirection]
                  updateFilters({ sortField: field, sortDirection: direction })
                }}
              >
                <SelectTrigger className="w-48 bg-gray-900/40 border-gray-700/20 text-white hover:bg-gray-800/50">
                  <SelectValue placeholder="Alphabetical Filter" />
                </SelectTrigger>
                <SelectContent className="bg-gray-900 border-gray-700">
                  <SelectItemRightCheck value="name-asc" className="text-white hover:bg-purple-400/10 focus:bg-purple-400/10">
                    Name (A–Z)
                  </SelectItemRightCheck>
                  <SelectItemRightCheck value="name-desc" className="text-white hover:bg-purple-400/10 focus:bg-purple-400/10">
                    Name (Z–A)
                  </SelectItemRightCheck>
                  <SelectItemRightCheck value="release-desc" className="text-white hover:bg-purple-400/10 focus:bg-purple-400/10">
                    Newest First
                  </SelectItemRightCheck>
                  <SelectItemRightCheck value="release-asc" className="text-white hover:bg-purple-400/10 focus:bg-purple-400/10">
                    Oldest First
                  </SelectItemRightCheck>
                  <SelectItemRightCheck value="price-asc" className="text-white hover:bg-purple-400/10 focus:bg-purple-400/10">
                    Price (Low to High)
                  </SelectItemRightCheck>
                  <SelectItemRightCheck value="price-desc" className="text-white hover:bg-purple-400/10 focus:bg-purple-400/10">
                    Price (High to Low)
                  </SelectItemRightCheck>
                </SelectContent>
              </Select>

              {/* Reset Filters Button */}
              <Button
                variant="outline"
                size="sm"
                onClick={handleResetFilters}
                className="border-gray-700/20 text-gray-300 hover:bg-gray-800/50 hover:text-red-400 p-2 h-10 w-10"
                title="Reset all filters"
              >
                <RemoveFiltersIcon className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Selected Filters Display */}
          <SelectedFiltersDisplay
            filters={filters}
            onRemoveFilter={handleRemoveFilter}
            onClearCategory={handleClearCategory}
          />
        </div>

        {/* Results Count */}
        <div className="mb-8 text-gray-400">
          Showing {filteredSkins.length} of {totalCount || skins.length} skins
        </div>

        {/* Did You Mean? Suggestions - Show when no search results but have suggestions */}
        {!hasSearchResults && searchSuggestions.length > 0 && filters.search.trim() && (
          <DidYouMean
            suggestions={searchSuggestions}
            onSuggestionClick={handleSuggestionClick}
            variant="purple"
            className="mb-8"
          />
        )}

        {/* Lazy Loading Skins Grid */}
        <LazySkinGrid
          skins={filteredSkins}
          isClient={isClient}
        />

        {/* No Results */}
        {filteredSkins.length === 0 && !loading && (
          <div className="text-center py-12">
            <div className="relative mx-auto mb-4 w-32 h-32">
              <Image
                src="https://raw.communitydragon.org/latest/game/assets/loadouts/summoneremotes/champions/blitzcrank/blitzcrank_sad_confused_glow.png"
                alt="No results glow"
                width={128}
                height={128}
                className="absolute inset-0 opacity-60"
                unoptimized
              />
              <Image
                src="https://raw.communitydragon.org/latest/game/assets/loadouts/summoneremotes/champions/blitzcrank/blitzcrank_sad_confused_vfx.png"
                alt="No results"
                width={128}
                height={128}
                className="relative z-10"
                unoptimized
              />
            </div>
            <h3 className="text-xl font-semibold text-gray-300 mb-2">No {rarityName.toLowerCase()} skins found</h3>
            <p className="text-gray-400 mb-4">Try adjusting your filters or search terms</p>
            <Button onClick={handleResetFilters} variant="outline" className="border-gray-700/20 text-gray-300">
              Clear Filters
            </Button>
          </div>
        )}

        {/* Quick Links */}
        <div className="mt-12 grid grid-cols-1 gap-6 overflow-visible">
          <Link href="/skins">
            <Card className="bg-gray-900/40 border-purple-700/20 hover:border-purple-400/50 hover:-translate-y-2 hover:z-10 transition-all duration-300 group cursor-pointer relative">
              <CardContent className="p-6">
                <Filter className="h-8 w-8 text-purple-400 mb-3" />
                <h3 className="text-xl font-semibold text-white mb-2 group-hover:text-purple-300 transition-colors">
                  All Skins
                </h3>
                <p className="text-gray-400">Browse all League of Legends skins</p>
              </CardContent>
            </Card>
          </Link>
        </div>

        {/* Mobile Search Panel */}
        <SkinSearchPanel
          isOpen={isSearchPanelOpen}
          onClose={() => setIsSearchPanelOpen(false)}
          filters={filters}
          onFiltersChange={updateFilters}
          onResetFilters={handleResetFilters}
          availableTiers={availableTiers}
          availableChampions={availableChampions}
          availableSkinLines={availableSkinLines}
          filteredCount={filteredSkins.length}
          totalCount={totalCount || filteredSkins.length}
        />
      </div>
    </SharedLayout>
  )
}
