import { Card, CardContent } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"

export function ChromaDetailSkeleton() {
  return (
    <div className="container mx-auto px-8 py-2">
      {/* Main Chroma Details - Two Column Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
        {/* Chroma Information - Left Column */}
        <div className="space-y-6">
          {/* Title and Badge */}
          <div>
            <Skeleton className="h-10 w-80 mb-2" />
            <div className="flex flex-wrap items-center gap-2 mb-6">
              <Skeleton className="h-6 w-20 rounded-full" />
              <Skeleton className="h-6 w-16 rounded-full" />
            </div>
          </div>

          {/* Chroma Name */}
          <div>
            <Skeleton className="h-5 w-24 mb-2" />
            <Skeleton className="h-6 w-40" />
          </div>

          {/* Champion Link */}
          <div className="flex items-center space-x-2">
            <Skeleton className="h-4 w-4" />
            <Skeleton className="h-5 w-32" />
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-2 gap-4">
            <Card className="bg-gray-900/40 border-purple-700/20">
              <CardContent className="p-4 text-center">
                <Skeleton className="h-8 w-8 mx-auto mb-2" />
                <Skeleton className="h-4 w-16 mx-auto mb-1" />
                <Skeleton className="h-3 w-12 mx-auto" />
              </CardContent>
            </Card>
            <Card className="bg-gray-900/40 border-purple-700/20">
              <CardContent className="p-4 text-center">
                <Skeleton className="h-8 w-8 mx-auto mb-2" />
                <Skeleton className="h-4 w-20 mx-auto mb-1" />
                <Skeleton className="h-3 w-16 mx-auto" />
              </CardContent>
            </Card>
          </div>

          {/* Acquisition Method */}
          <div>
            <Skeleton className="h-5 w-32 mb-2" />
            <Skeleton className="h-4 w-48" />
          </div>

          {/* Release Date */}
          <div className="flex items-center space-x-2">
            <Skeleton className="h-4 w-4" />
            <Skeleton className="h-4 w-32" />
          </div>

          {/* Action Buttons */}
          <div className="space-y-3">
            <Skeleton className="h-10 w-full rounded-lg" />
            <Skeleton className="h-10 w-full rounded-lg" />
          </div>
        </div>

        {/* Chroma Image - Right Column */}
        <div className="relative">
          <Card className="bg-gray-900/40 border-purple-700/20 overflow-hidden">
            <CardContent className="p-0">
              <div className="relative aspect-[16/9] w-full">
                <Skeleton className="w-full h-full" />
              </div>
            </CardContent>
          </Card>

          {/* Color Palette */}
          <div className="mt-6">
            <Skeleton className="h-6 w-32 mb-4" />
            <div className="flex space-x-2">
              {Array.from({ length: 5 }).map((_, index) => (
                <Skeleton key={index} className="w-8 h-8 rounded-full" />
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Description Section */}
      <div className="mb-12">
        <Card className="bg-gray-900/40 border-purple-700/20">
          <CardContent className="p-6">
            <Skeleton className="h-6 w-32 mb-4" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-2/3" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Related Chromas */}
      <div>
        <Skeleton className="h-8 w-56 mb-6" />
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-4">
          {Array.from({ length: 6 }).map((_, index) => (
            <Card key={index} className="bg-gray-900/40 border-purple-700/20">
              <CardContent className="p-2">
                <div className="aspect-[16/9] mb-2">
                  <Skeleton className="w-full h-full rounded" />
                </div>
                <Skeleton className="h-4 w-full mb-1" />
                <Skeleton className="h-3 w-20" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}
