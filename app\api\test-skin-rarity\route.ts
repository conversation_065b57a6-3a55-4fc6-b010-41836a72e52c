import { skinRarityService, SkinRarityService } from '@/lib/api/skin-rarity-service'
import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    

    // Test with some known skin IDs
    const testSkinIds = [
      1000, // Annie Classic (should be base)
      1001, // Goth Annie (should be kNoRarity -> Regular)
      1013, // Lunar Beast Annie (should be kEpic + Legacy)
      222000, // Jinx Classic (should be base)
      222001, // Mafia Jinx (should have rarity)
      103085, // Risen <PERSON> (stage 1 - kTranscendent)
      103086, // Immortalized <PERSON> (stage 2 - should inherit kTranscendent)
    ]

    

    // Test individual skin fetching
    const individualResults = []
    for (const skinId of testSkinIds) {
      const skinData = await skinRarityService.getSkinRarityData(skinId)
      if (skinData) {
        const normalizedRarity = SkinRarityService.normalizeRarity(skinData.rarity)
        individualResults.push({
          skinId,
          name: skinData.name,
          isBase: skinData.isBase,
          originalRarity: skinData.rarity,
          normalizedRarity,
          isLegacy: skinData.isLegacy,
          rarityIconUrl: SkinRarityService.getRarityIconUrl(normalizedRarity),
          legacyIconUrl: skinData.isLegacy ? SkinRarityService.getLegacyIconUrl() : null
        })
      } else {
        individualResults.push({
          skinId,
          error: 'Skin not found'
        })
      }
    }

    // Test batch fetching
    const batchResults = await skinRarityService.getMultipleSkinRarityData(testSkinIds)

    // Test cache stats
    const cacheStats = skinRarityService.getCacheStats()

    return NextResponse.json({
      success: true,
      message: 'Skin rarity system test completed',
      data: {
        individualResults,
        batchResults: Object.keys(batchResults).map(skinId => ({
          skinId: parseInt(skinId),
          found: batchResults[parseInt(skinId)] !== null,
          data: batchResults[parseInt(skinId)]
        })),
        cacheStats,
        testSkinIds
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('❌ Error testing skin rarity system:', error)

    return NextResponse.json({
      success: false,
      error: 'Failed to test skin rarity system',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
