"use client"

import UniversalChampionCard from "@/components/champion/universal-champion-card"
import SharedLayout from "@/components/shared-layout"
import { But<PERSON> } from "@/components/ui/button"
import { ClassesCombobox } from "@/components/ui/classes-combobox"
import { FilterIcon } from "@/components/ui/filter-icon"
import { Input } from "@/components/ui/input"
import { RolesCombobox } from "@/components/ui/roles-combobox"
import { Select, SelectContent, SelectTrigger, SelectValue } from "@/components/ui/select"
import { SelectItemRightCheck } from "@/components/ui/select-item-right-check"
import { SelectedChampionFiltersDisplay } from "@/components/ui/selected-champion-filters-display"
import { useFreeRotation } from "@/hooks/use-champions"
import { AlertCircle, Search } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { useEffect, useMemo, useState } from "react"

import SearchPanel from "@/components/search/search-panel"
import { FreeRotationChampionsSkeleton } from "@/components/skeletons/free-rotation-skeleton"
import { RemoveFiltersIcon } from "@/components/ui/remove-filters-icon"

// Utility function for smooth scroll to top
const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  })
}

export default function FreeRotationClient() {
  const { freeChampions, loading, error } = useFreeRotation()

  // Search and filter states
  const [searchTerm, setSearchTerm] = useState('')
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('')
  const [selectedRoles, setSelectedRoles] = useState<string[]>([])
  const [selectedClasses, setSelectedClasses] = useState<string[]>([])
  const [sortField, setSortField] = useState<'name' | 'release'>('name')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc')
  const [isSearchPanelOpen, setIsSearchPanelOpen] = useState(false)

  // Debounce search term with 300ms delay
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm)
    }, 300)

    return () => clearTimeout(timer)
  }, [searchTerm])

  // Filter and sort champions based on search and filters
  const filteredFreeChampions = useMemo(() => {
    let filtered = freeChampions.filter(champion => {
      const matchesSearch = champion.name.toLowerCase().includes(debouncedSearchTerm.toLowerCase())

      // Use recommended positions for role filtering (same as /champions)
      const matchesRole = selectedRoles.length === 0 ||
        (champion.recommendedPositions && champion.recommendedPositions.length > 0 &&
         champion.recommendedPositions.some(position => selectedRoles.includes(position)))

      const matchesClass = selectedClasses.length === 0 || (champion.tags && champion.tags.some(tag => {
        // Convert filter classes to lowercase for comparison with API tags
        const lowercaseFilterClasses = selectedClasses.map(cls => cls.toLowerCase())
        return lowercaseFilterClasses.includes(tag.toLowerCase())
      }))
      return matchesSearch && matchesRole && matchesClass
    })

    // Apply sorting
    filtered.sort((a, b) => {
      if (sortField === 'name') {
        const comparison = a.name.localeCompare(b.name)
        return sortDirection === 'asc' ? comparison : -comparison
      }
      // For release date, we don't have that data in free rotation, so just use name
      return a.name.localeCompare(b.name)
    })

    return filtered
  }, [freeChampions, debouncedSearchTerm, selectedRoles, selectedClasses, sortField, sortDirection])

  // Calculate total champions count
  const totalChampionsCount = useMemo(() => {
    return filteredFreeChampions.length
  }, [filteredFreeChampions])

  // Calculate display text for champion count
  const getChampionCountText = () => {
    return `Play these 20 free rotation champions this week`
  }

  // Get unique roles and classes for filter options (same as /champions)
  const uniqueRoles = useMemo(() => {
    const roles = new Set<string>()
    freeChampions.forEach(champion => {
      if (champion.recommendedPositions && champion.recommendedPositions.length > 0) {
        champion.recommendedPositions.forEach(role => roles.add(role))
      }
    })
    // Return in the standard order, but only include roles that exist in the data
    const roleDisplayOrder = ["Top", "Jungle", "Middle", "Bottom", "Support"]
    return roleDisplayOrder.filter(role => roles.has(role))
  }, [freeChampions])

  const uniqueClasses = useMemo(() => {
    const classes = new Set<string>()
    freeChampions.forEach(champion => {
      if (champion.tags && champion.tags.length > 0) {
        champion.tags.forEach(tag => {
          // Map common tags to standard class names
          const classMap: Record<string, string> = {
            'fighter': 'Fighter',
            'tank': 'Tank',
            'mage': 'Mage',
            'assassin': 'Assassin',
            'marksman': 'Marksman',
            'support': 'Support'
          }
          const mappedClass = classMap[tag.toLowerCase()] || tag
          if (['Fighter', 'Tank', 'Mage', 'Assassin', 'Marksman', 'Support'].includes(mappedClass)) {
            classes.add(mappedClass)
          }
        })
      }
    })
    return Array.from(classes).sort()
  }, [freeChampions])

  // Clear filters
  const clearFilters = () => {
    setSearchTerm('')
    setDebouncedSearchTerm('')
    setSelectedRoles([])
    setSelectedClasses([])
    setSortField('name')
    setSortDirection('asc')
    // Scroll to top with smooth animation
    scrollToTop()
  }

  const handleRemoveFilter = (filterType: string, value: string) => {
    if (filterType === 'roles') {
      const newRoles = selectedRoles.filter(role => role !== value)
      setSelectedRoles(newRoles)
    } else if (filterType === 'classes') {
      const newClasses = selectedClasses.filter(cls => cls !== value)
      setSelectedClasses(newClasses)
    }
  }

  const handleClearCategory = (filterType: string) => {
    if (filterType === 'roles') {
      setSelectedRoles([])
    } else if (filterType === 'classes') {
      setSelectedClasses([])
    }
  }

  const searchButton = (
    <Button
      onClick={() => setIsSearchPanelOpen(true)}
      variant="outline"
      size="sm"
      className="border-orange-700/30 text-orange-400 hover:bg-orange-400/10 hover:border-orange-400/50 p-2"
    >
      <FilterIcon className="h-4 w-4" />
    </Button>
  )

  return (
    <SharedLayout>
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 pb-4 sm:pb-6 lg:pb-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <Image
                src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-collections/global/default/images/item-element/free-rotation.png"
                alt="Free Rotation"
                width={32}
                height={32}
                className="w-8 h-8"
                unoptimized
              />
              <h1 className="text-4xl font-bold text-white">Free Champion Rotation</h1>
            </div>
            <div className="flex items-center space-x-3">
              {/* Mobile Search Button - Next to Title */}
              <div className="lg:hidden">
                {searchButton}
              </div>
            </div>
          </div>

          <p className="text-gray-300 text-lg">
            {getChampionCountText()}
          </p>
        </div>

        {/* Desktop Search and Filters */}
        <div className="hidden lg:block mb-8 space-y-4">
          {/* Main Filters Row with Sort at Far Right */}
          <div className="flex flex-col lg:flex-row gap-4 items-end">
            {/* Left side filters group */}
            <div className="flex flex-col lg:flex-row gap-4 flex-1">
              {/* Search Bar - Reduced width */}
              <div className="relative lg:flex-1 lg:max-w-md">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <Input
                  type="text"
                  placeholder="Search champions..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-12 bg-gray-900/80 border-gray-700/50 text-white placeholder-gray-400 focus:border-orange-400/60"
                />
              </div>

              {/* Role Filter */}
              <RolesCombobox
                selectedRoles={selectedRoles}
                onSelectionChange={setSelectedRoles}
                availableRoles={uniqueRoles}
                placeholder="Select Roles"
                className="w-full lg:w-48"
              />

              {/* Class Filter */}
              <ClassesCombobox
                selectedClasses={selectedClasses}
                onSelectionChange={setSelectedClasses}
                availableClasses={uniqueClasses}
                placeholder="Select Classes"
                className="w-full lg:w-48"
              />
            </div>

            {/* Alphabetical Filter & Reset - Far Right */}
            <div className="flex-shrink-0 flex items-center gap-2">
              <Select
                value={`${sortField}-${sortDirection}`}
                onValueChange={(value) => {
                  const [field, direction] = value.split('-') as [typeof sortField, typeof sortDirection]
                  setSortField(field)
                  setSortDirection(direction)
                }}
              >
                <SelectTrigger className="w-48 bg-gray-900/40 border-gray-700/20 text-white hover:bg-gray-800/50">
                  <SelectValue placeholder="Alphabetical Filter" />
                </SelectTrigger>
                <SelectContent className="bg-gray-900 border-gray-700">
                  <SelectItemRightCheck value="name-asc" className="text-white hover:bg-orange-400/10 focus:bg-orange-400/10">
                    Name (A–Z)
                  </SelectItemRightCheck>
                  <SelectItemRightCheck value="name-desc" className="text-white hover:bg-orange-400/10 focus:bg-orange-400/10">
                    Name (Z–A)
                  </SelectItemRightCheck>
                </SelectContent>
              </Select>

              {/* Reset Filters Button */}
              <Button
                variant="outline"
                size="sm"
                onClick={clearFilters}
                className="border-gray-700/20 text-gray-300 hover:bg-gray-800/50 hover:text-red-400 p-2 h-10 w-10"
                title="Reset all filters"
              >
                <RemoveFiltersIcon className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Selected Filters Display */}
          <SelectedChampionFiltersDisplay
            filters={{
              search: searchTerm,
              roles: selectedRoles,
              classes: selectedClasses,
              difficulty: [0, 10] as [number, number]
            }}
            onRemoveFilter={handleRemoveFilter}
            onClearCategory={handleClearCategory}
          />
        </div>

        {/* Error State */}
        {error && (
          <div className="mb-8 p-4 bg-red-900/20 border border-red-700/30 rounded-lg">
            <div className="flex items-center space-x-2 text-red-400">
              <AlertCircle className="h-5 w-5" />
              <span>Error loading free rotation: {error}</span>
            </div>
          </div>
        )}

        {/* Free Rotation Champions */}
        {loading ? (
          <FreeRotationChampionsSkeleton count={14} title="Free Champion Rotation" />
        ) : (
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-white mb-6 flex items-center">
              <Image
                src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-social/global/default/coc-icon-group.svg"
                alt="Free Champion Rotation"
                width={24}
                height={24}
                className="w-6 h-6 mr-2"
                style={{ filter: 'brightness(0) saturate(100%) invert(94%) sepia(8%) saturate(1458%) hue-rotate(17deg) brightness(96%) contrast(93%)' }}
                unoptimized
              />
              Free Champion Rotation
            </h2>
            {filteredFreeChampions.length === 0 ? (
              <div className="text-center py-12">
                <p className="text-gray-400 text-lg">No champions match your current filters</p>
                <Button onClick={clearFilters} className="mt-4 bg-orange-600 hover:bg-orange-700">
                  Clear Filters
                </Button>
              </div>
            ) : (
              <div className="champion-grid">
                {filteredFreeChampions.map((champion, index) => (
                  <UniversalChampionCard
                    key={index}
                    champion={{...champion, role: champion.role, class: champion.class}}
                    variant="loading-image"
                    borderColor="orange-700/20"
                    hoverColor="orange-400/50"
                    onRoleClick={(role) => {
                      const newRoles = selectedRoles.includes(role)
                        ? selectedRoles.filter(r => r !== role)
                        : [...selectedRoles, role]
                      setSelectedRoles(newRoles)
                      // Open search panel on mobile
                      if (window.innerWidth < 1024) {
                        setIsSearchPanelOpen(true)
                      } else {
                        scrollToTop()
                      }
                    }}
                    onClassClick={(className) => {
                      const newClasses = selectedClasses.includes(className)
                        ? selectedClasses.filter(c => c !== className)
                        : [...selectedClasses, className]
                      setSelectedClasses(newClasses)
                      scrollToTop()
                    }}
                  />
                ))}
              </div>
            )}
          </div>
        )}

        {/* Additional Info */}
        <div className="bg-gray-900/40 border border-orange-700/20 rounded-lg p-6">
          <h3 className="text-xl font-semibold text-white mb-4">About Free Champion Rotation</h3>
          <div className="space-y-3 text-gray-300">
            <p>• The free champion rotation changes every Tuesday at 10:00 AM PT</p>
            <p>• You can play these champions in all game modes except Ranked</p>
            <p>• Use this opportunity to try new champions before purchasing them</p>
          </div>
          <div className="mt-6">
            <Link href="/champions">
              <Button className="bg-gradient-to-r from-orange-600 to-amber-600 hover:from-orange-700 hover:to-amber-700">
                View All Champions
              </Button>
            </Link>
          </div>
        </div>
      </div>

      {/* Search Panel */}
      <SearchPanel
        isOpen={isSearchPanelOpen}
        onClose={() => setIsSearchPanelOpen(false)}
        searchTerm={searchTerm}
        onSearchChange={setSearchTerm}
        selectedRoles={selectedRoles}
        onRolesChange={setSelectedRoles}
        selectedClasses={selectedClasses}
        onClassesChange={setSelectedClasses}
        onResetFilters={clearFilters}
        sortField={sortField}
        sortDirection={sortDirection}
        onSortChange={(field, direction) => {
          setSortField(field)
          setSortDirection(direction)
        }}
        roles={uniqueRoles}
        classes={uniqueClasses}
        filteredCount={filteredFreeChampions.length}
        totalCount={totalChampionsCount}
      />
    </SharedLayout>
  )
}
