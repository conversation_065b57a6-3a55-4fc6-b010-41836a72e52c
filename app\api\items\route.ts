import { NextRequest, NextResponse } from 'next/server'

interface ItemData {
  id: string
  name: string
  description: string
  plaintext: string
  image: string
  category: string
  formattedPrice: string
  sellPrice: string
  components: string[]
  buildsInto: string[]
  isPurchasable: boolean
  tags: string[]
  gold: {
    base: number
    total: number
    sell: number
    purchasable: boolean
  }
  stats: Record<string, number>
  mainStats: string[]
  isFullItem: boolean
  maps: Record<string, boolean>
  availability: 'Available' | 'Legacy' | 'Removed'
}

// Cache for items data
let itemsCache: {
  data: ItemData[]
  tags: string[]
  mainStats: string[]
  timestamp: number
} | null = null

const CACHE_TTL = 24 * 60 * 60 * 1000 // 24 hours in milliseconds

function formatTag(tag: string): string {
  // Add spaces before capital letters (except the first one)
  return tag.replace(/([a-z])([A-Z])/g, '$1 $2')
}

// Function to extract main stats from item description
function extractMainStats(description: string): string[] {
  const stats: string[] = []

  // Common stat patterns to look for
  const statPatterns = [
    { pattern: /(\d+)\s*Attack Damage/i, stat: 'Attack Damage' },
    { pattern: /(\d+)\s*Ability Power/i, stat: 'Ability Power' },
    { pattern: /(\d+)%?\s*Attack Speed/i, stat: 'Attack Speed' },
    { pattern: /(\d+)%?\s*Critical Strike Chance/i, stat: 'Critical Strike Chance' },
    { pattern: /(\d+)\s*Armor/i, stat: 'Armor' },
    { pattern: /(\d+)\s*Magic Resist/i, stat: 'Magic Resist' },
    { pattern: /(\d+)\s*Health/i, stat: 'Health' },
    { pattern: /(\d+)\s*Mana/i, stat: 'Mana' },
    { pattern: /(\d+)%?\s*Move Speed/i, stat: 'Move Speed' },
    { pattern: /(\d+)%?\s*Life Steal/i, stat: 'Life Steal' },
    { pattern: /(\d+)\s*Lethality/i, stat: 'Lethality' },
    { pattern: /(\d+)\s*Ability Haste/i, stat: 'Ability Haste' },
    { pattern: /(\d+)%?\s*Omnivamp/i, stat: 'Omnivamp' },
    { pattern: /(\d+)%?\s*Spell Vamp/i, stat: 'Spell Vamp' },
    { pattern: /(\d+)%?\s*Cooldown Reduction/i, stat: 'Cooldown Reduction' },
    { pattern: /(\d+)\s*Magic Penetration/i, stat: 'Magic Penetration' },
    { pattern: /(\d+)\s*Armor Penetration/i, stat: 'Armor Penetration' },
    { pattern: /(\d+)%?\s*Base Health Regen/i, stat: 'Health Regen' },
    { pattern: /(\d+)%?\s*Base Mana Regen/i, stat: 'Mana Regen' }
  ]

  for (const { pattern, stat } of statPatterns) {
    if (pattern.test(description)) {
      stats.push(stat)
    }
  }

  return stats
}

// Function to determine if an item is a "Full Item" (final item vs secondary/component)
function isFullItem(item: any): boolean {
  // Full items typically:
  // 1. Don't build into other items (no "into" property or empty array)
  // 2. Have higher gold cost (usually > 1000)
  // 3. Are built from components (have "from" property)

  const hasNoBuildsInto = !item.into || item.into.length === 0
  const hasHighCost = item.gold?.total > 1000
  const isBuiltFromComponents = item.from && item.from.length > 0

  return hasNoBuildsInto && (hasHighCost || isBuiltFromComponents)
}

// Function to identify legitimate historical items
function isHistoricalItem(item: any): boolean {
  // Historical items are items that were once in the game but are no longer available
  // They should have:
  // 1. Real item stats, gold values, and descriptions
  // 2. Proper item names (not test items or placeholders)
  // 3. May have "from" or "into" arrays with other item IDs
  // 4. May have tags like "Removed" in their data

  // Must have a proper name (not empty, not just numbers, not test items)
  const hasProperName = item.name &&
    item.name.trim().length > 0 &&
    !item.name.toLowerCase().includes('test') &&
    !item.name.toLowerCase().includes('placeholder') &&
    !/^[\d\s]*$/.test(item.name)

  // Must have some meaningful content (description or plaintext)
  const hasMeaningfulContent = (item.description && item.description.trim().length > 0) ||
    (item.plaintext && item.plaintext.trim().length > 0)

  // Must have gold values (even if 0, should have the structure)
  const hasGoldData = item.gold && typeof item.gold === 'object'

  // Should have stats or build relationships
  const hasGameplayData = (item.stats && Object.keys(item.stats).length > 0) ||
    (item.from && item.from.length > 0) ||
    (item.into && item.into.length > 0) ||
    (item.tags && item.tags.length > 0)

  return hasProperName && hasMeaningfulContent && hasGoldData && hasGameplayData
}

// Function to determine item availability status
function getItemAvailability(item: any): 'Available' | 'Legacy' | 'Removed' {
  // Available: Currently in store and available on Summoner's Rift or ARAM
  if (item.inStore !== false && item.maps) {
    if (item.maps['11'] === true || item.maps['12'] === true) {
      return 'Available'
    }
  }

  // Legacy: Historical items that were once available but are no longer in store
  if (item.inStore === false && isHistoricalItem(item)) {
    // Check if it was previously available on Summoner's Rift or ARAM
    if (item.maps && (item.maps['11'] === false || item.maps['12'] === false)) {
      return 'Legacy'
    }
  }

  // Removed: Items that are completely removed from the game
  return 'Removed'
}



function processItemData(rawData: any, currentVersion: string): { items: ItemData[], tags: string[], mainStats: string[] } {
  const items: ItemData[] = []
  const tagSet = new Set<string>()
  const mainStatsSet = new Set<string>()
  const itemsByName = new Map<string, ItemData[]>()

  Object.entries(rawData.data).forEach(([id, item]: [string, any]) => {
    // Exclude items with requiredChampion: "Gangplank"
    if (item.requiredChampion === "Gangplank") return

    // Exclude Arena-only items (map 30 or 33 true, map 11 false)
    if (item.maps && item.maps['11'] === false && (item.maps['30'] === true || item.maps['33'] === true)) {
      return
    }

    // Apply inclusion rules:
    let shouldInclude = false

    // 1. INCLUDE regular items: maps["11"] === true (Summoner's Rift)
    if (item.maps && item.maps['11'] === true) {
      shouldInclude = true
    }
    // 2. INCLUDE ARAM items: maps["12"] === true
    else if (item.maps && item.maps['12'] === true) {
      shouldInclude = true
    }
    // 3. INCLUDE historical/legacy items for Summoner's Rift
    else if (item.inStore === false && item.maps && item.maps['11'] === false && isHistoricalItem(item)) {
      shouldInclude = true
    }
    // 4. INCLUDE old ARAM items
    else if (item.inStore === false && item.maps && item.maps['12'] === false && isHistoricalItem(item)) {
      shouldInclude = true
    }

    if (!shouldInclude) return

    // Process tags and add to tag set
    const itemTags = item.tags || []
    itemTags.forEach((tag: string) => {
      tagSet.add(tag)
    })

    // Extract main stats from description
    const itemMainStats = extractMainStats(item.description || '')
    itemMainStats.forEach((stat: string) => {
      mainStatsSet.add(stat)
    })

    // Determine item availability
    const availability = getItemAvailability(item)

    // Set formatted price based on availability
    let formattedPrice: string
    if (availability === 'Legacy' || availability === 'Removed') {
      formattedPrice = 'Legacy'
    } else {
      formattedPrice = item.gold?.total ? `${item.gold.total}g` : 'N/A'
    }

    const processedItem: ItemData = {
      id,
      name: item.name || 'Unknown Item',
      description: item.description || '',
      plaintext: item.plaintext || '',
      image: `https://ddragon.leagueoflegends.com/cdn/${currentVersion}/img/item/${item.image?.full || `${id}.png`}`,
      category: '', // Remove category
      formattedPrice,
      sellPrice: item.gold?.sell ? `${item.gold.sell}g` : 'N/A',
      components: item.from || [],
      buildsInto: item.into || [],
      isPurchasable: item.gold?.purchasable !== false,
      tags: item.tags || [],
      gold: {
        base: item.gold?.base || 0,
        total: item.gold?.total || 0,
        sell: item.gold?.sell || 0,
        purchasable: item.gold?.purchasable !== false
      },
      stats: item.stats || {},
      mainStats: itemMainStats,
      isFullItem: isFullItem(item),
      maps: item.maps || {},
      availability
    }

    // Group items by name for deduplication
    const itemName = processedItem.name
    if (!itemsByName.has(itemName)) {
      itemsByName.set(itemName, [])
    }
    itemsByName.get(itemName)!.push(processedItem)
  })

  // Deduplicate items by selecting the correct version based on priority rules
  itemsByName.forEach((duplicateItems) => {
    if (duplicateItems.length === 1) {
      items.push(duplicateItems[0])
    } else {
      // Apply filtering rules to select the correct version:
      // 1. Exclude items with maps["11"] === false (Arena/special game modes)
      // 2. Prioritize items with maps["11"] === true (Summoner's Rift)
      // 3. Select the most recent version (last in array)

      // First, filter out items with maps["11"] === false
      const validItems = duplicateItems.filter(item => {
        // Keep items that have maps["11"] === true OR don't have maps data
        return !item.maps || item.maps['11'] !== false
      })

      // If we have valid items after filtering, use them; otherwise fall back to original list
      const itemsToConsider = validItems.length > 0 ? validItems : duplicateItems

      // Among the valid items, prioritize those with maps["11"] === true
      const summonersRiftItems = itemsToConsider.filter(item =>
        item.maps && item.maps['11'] === true
      )

      // Select the correct version from the prioritized list
      let selectedItem: ItemData
      if (summonersRiftItems.length > 0) {
        // For Summoner's Rift items, prefer the one with the simpler/lower ID number
        // This helps select the base version (e.g., 6617) over variants (e.g., 326617)
        selectedItem = summonersRiftItems.reduce((prev, current) => {
          const prevId = parseInt(prev.id)
          const currentId = parseInt(current.id)
          return currentId < prevId ? current : prev
        })
      } else {
        // Fall back to the last (most recent) item from the valid items
        selectedItem = itemsToConsider[itemsToConsider.length - 1]
      }

      items.push(selectedItem)
    }
  })

  const tags = Array.from(tagSet).sort()
  const mainStats = Array.from(mainStatsSet).sort()
  return { items, tags, mainStats }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const refresh = searchParams.get('refresh') === 'true'
    
    
    
    // Check cache first (unless refresh is requested)
    if (!refresh && itemsCache && (Date.now() - itemsCache.timestamp) < CACHE_TTL) {
      
      return NextResponse.json({
        success: true,
        data: itemsCache.data,
        tags: itemsCache.tags,
        mainStats: itemsCache.mainStats,
        cached: true,
        timestamp: new Date().toISOString()
      }, {
        headers: {
          'Cache-Control': 'public, s-maxage=86400, stale-while-revalidate=172800', // 24h cache, 48h stale
          'CDN-Cache-Control': 'public, s-maxage=86400',
          'Vercel-CDN-Cache-Control': 'public, s-maxage=86400'
        }
      })
    }
    
    // Get current game version
    const versionsResponse = await fetch('https://ddragon.leagueoflegends.com/api/versions.json')
    if (!versionsResponse.ok) {
      throw new Error('Failed to fetch game versions')
    }
    const versions = await versionsResponse.json()
    const currentVersion = versions[0]
    
    // Get items data
    const url = `https://ddragon.leagueoflegends.com/cdn/${currentVersion}/data/en_US/item.json`
    const response = await fetch(url)
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: Failed to fetch items data`)
    }
    
    const rawData = await response.json()
    const { items, tags, mainStats } = processItemData(rawData, currentVersion)

    // Cache the processed data
    itemsCache = {
      data: items,
      tags,
      mainStats,
      timestamp: Date.now()
    }
    
    

    return NextResponse.json({
      success: true,
      data: items,
      tags,
      mainStats,
      count: items.length,
      version: currentVersion,
      timestamp: new Date().toISOString()
    }, {
      headers: {
        'Cache-Control': 'public, s-maxage=86400, stale-while-revalidate=172800', // 24h cache, 48h stale
        'CDN-Cache-Control': 'public, s-maxage=86400',
        'Vercel-CDN-Cache-Control': 'public, s-maxage=86400'
      }
    })
    
  } catch (error) {
    console.error('❌ Failed to fetch items data:', error)

    return NextResponse.json({
      success: false,
      error: 'Failed to fetch items data',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

// Handle cache management operations
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action } = body

    switch (action) {
      case 'clear-cache':
        itemsCache = null
        return NextResponse.json({
          success: true,
          message: 'Items cache cleared successfully'
        })

      case 'get-stats':
        const stats = {
          cached: !!itemsCache,
          cacheAge: itemsCache ? Date.now() - itemsCache.timestamp : 0,
          itemCount: itemsCache?.data.length || 0,
          tagCount: itemsCache?.tags.length || 0
        }
        return NextResponse.json({
          success: true,
          data: stats
        })

      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid action. Supported actions: clear-cache, get-stats'
        }, { status: 400 })
    }
  } catch (error) {
    console.error('❌ Failed to handle cache operation:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to handle cache operation'
    }, { status: 500 })
  }
}
