import { transformToCDNUrl } from '@/lib/utils/cdn'
import { NextRequest, NextResponse } from 'next/server'

/**
 * Normalize champion name for Data Dragon API
 */
function normalizeChampionName(championName: string): string {
  // Handle special cases for champions with complex names
  const nameMap: Record<string, string> = {
    '<PERSON><PERSON><PERSON> & Willump': '<PERSON><PERSON><PERSON>',
    '<PERSON>': '<PERSON><PERSON><PERSON>',
    '<PERSON> <PERSON>': '<PERSON><PERSON><PERSON>',
    '<PERSON> Fortune': 'Miss<PERSON><PERSON><PERSON>',
    'Twisted Fate': 'TwistedFate',
    '<PERSON><PERSON> <PERSON>': '<PERSON>n<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON> IV': '<PERSON>arvanIV',
    'Cho\'Gath': 'Chogath',
    'Kha\'Zix': '<PERSON>ha<PERSON><PERSON>',
    'Vel\'Koz': 'Vel<PERSON><PERSON>',
    '<PERSON>\'Sa': '<PERSON><PERSON>',
    '<PERSON>k\'Sai': 'Rek<PERSON><PERSON>',
    'Tahm Kench': 'TahmKench',
    'Aurelion Sol': 'AurelionSol',
    'Renata Glasc': 'Ren<PERSON>',
    '<PERSON>\'Veth': '<PERSON><PERSON><PERSON>',
    '<PERSON>\'Sante': '<PERSON><PERSON><PERSON>'
  }

  // Check if we have a special mapping
  if (nameMap[championName]) {
    return nameMap[championName]
  }

  // Default normalization: remove spaces and special characters
  return championName.replace(/[^a-zA-Z]/g, '')
}

interface Chroma {
  id: string
  name: string
  color: string
  colors?: string[]
  chromaPath?: string | null
  isDefault: boolean
  skinId: number
}

interface SkinWithChromas {
  id: number
  name: string
  chromas: Chroma[]
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ championId: string }> }
) {
  try {
    const { championId } = await params

    

    // Normalize champion ID for Data Dragon API (remove spaces and special characters)
    const normalizedChampionId = normalizeChampionName(championId)

    // Get current game version
    const versionsResponse = await fetch('https://ddragon.leagueoflegends.com/api/versions.json')
    const versions = await versionsResponse.json()
    const currentVersion = versions[0]

    // Get champion detail data to get the champion key
    const championUrl = `https://ddragon.leagueoflegends.com/cdn/${currentVersion}/data/en_US/champion/${normalizedChampionId}.json`
    const championResponse = await fetch(championUrl)
    
    if (!championResponse.ok) {
      return NextResponse.json({
        success: false,
        error: 'Champion not found',
        championId
      }, { status: 404 })
    }
    
    const championData = await championResponse.json()
    const champion = championData.data[normalizedChampionId]
    const championKey = champion.key
    
    
    
    // Fetch Community Dragon data for chroma information
    const cdResponse = await fetch(
      `https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/champions/${championKey}.json`
    )
    
    if (!cdResponse.ok) {
      console.warn(`⚠️ API: Failed to fetch Community Dragon data for ${championId}`)
      return NextResponse.json({
        success: false,
        error: 'Failed to fetch chroma data',
        championId
      }, { status: 500 })
    }
    
    const cdData = await cdResponse.json()
    
    
    
    // Process the chroma data
    const skinsWithChromas: SkinWithChromas[] = []
    
    for (const skin of champion.skins) {
      const skinNum = skin.num
      const expectedSkinId = parseInt(championKey) * 1000 + skinNum
      
      const skinEntry: SkinWithChromas = {
        id: skinNum,
        name: skin.name,
        chromas: [],
      }
      
      // Find corresponding skin in Community Dragon data
      let cdSkin = cdData.skins?.find((cdSkin: any) => cdSkin.id === expectedSkinId)
      
      // If not found by calculated ID, try to match by other patterns
      if (!cdSkin) {
        // For base skin (num 0), look for isBase: true
        if (skinNum === 0) {
          cdSkin = cdData.skins?.find((cdSkin: any) => cdSkin.isBase === true)
        } else {
          // For other skins, try to find by name similarity
          cdSkin = cdData.skins?.find((cdSkin: any) => {
            const cdSkinName = cdSkin.name?.toLowerCase() || ''
            const skinName = skin.name?.toLowerCase() || ''
            return cdSkinName.includes(skinName.replace(/\s+/g, '').toLowerCase()) ||
                   skinName.includes(cdSkinName.replace(/\s+/g, '').toLowerCase())
          })
        }
      }
      
      
      
      if (cdSkin && cdSkin.chromas && cdSkin.chromas.length > 0) {
        
        
        // Always add default chroma first using the actual skin ID
        // Handle tiered skins (like K/DA ALL OUT Seraphine) by checking for tiers
        let defaultSplashPath: string
        if (cdSkin && cdSkin.questSkinInfo?.tiers && cdSkin.questSkinInfo.tiers.length > 0) {
          // For tiered skins, use the first tier's splash path
          const firstTier = cdSkin.questSkinInfo.tiers[0]
          defaultSplashPath = firstTier.uncenteredSplashPath || firstTier.splashPath
        } else {
          // For regular skins, use the root level path
          defaultSplashPath = cdSkin.uncenteredSplashPath
        }

        const chromaList: Chroma[] = [
          {
            id: cdSkin.id.toString(),
            name: 'Default',
            color: '#ffffff',
            isDefault: true,
            skinId: cdSkin.id,
            chromaPath: transformToCDNUrl(defaultSplashPath),
          },
        ]
        
        // Add real chromas from Community Dragon
        cdSkin.chromas.forEach((chroma: any) => {
          // Extract primary color from colors array
          let chromaColor = '#ffffff'
          let allColors: string[] = []

          if (chroma.colors && chroma.colors.length > 0) {
            chromaColor = chroma.colors[0]
            allColors = chroma.colors
          }

          chromaList.push({
            id: chroma.id.toString(),
            name: chroma.name || `Chroma ${chroma.id}`,
            color: chromaColor,
            colors: allColors.length > 0 ? allColors : undefined,
            chromaPath: transformToCDNUrl(chroma.chromaPath),
            skinId: cdSkin.id,
            isDefault: false,
          })
        })
        
        skinEntry.chromas = chromaList
        
      } else {
        
      }
      
      skinsWithChromas.push(skinEntry)
    }
    
    
    
    return NextResponse.json({
      success: true,
      data: {
        championId,
        championKey,
        skins: skinsWithChromas
      },
      gameVersion: currentVersion,
      timestamp: new Date().toISOString()
    }, {
      headers: {
        'Cache-Control': 'public, s-maxage=3600, stale-while-revalidate=86400',
        'CDN-Cache-Control': 'public, s-maxage=3600',
        'Vercel-CDN-Cache-Control': 'public, s-maxage=3600'
      }
    })
    
  } catch (error) {
    const { championId } = await params
    console.error(`❌ API: Error fetching chroma data for champion ${championId}:`, error)

    return NextResponse.json({
      success: false,
      error: 'Failed to fetch chroma data',
      message: error instanceof Error ? error.message : 'Unknown error',
      championId: championId
    }, {
      status: 500,
      headers: {
        'Cache-Control': 'no-cache'
      }
    })
  }
}
