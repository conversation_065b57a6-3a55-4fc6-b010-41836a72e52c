#!/usr/bin/env node

// Simple script to test the League of Legends API integration

const https = require('https');

// Test Data Dragon API endpoints
const testEndpoints = [
  {
    name: 'Game Versions',
    url: 'https://ddragon.leagueoflegends.com/api/versions.json'
  },
  {
    name: 'Champions List',
    url: 'https://ddragon.leagueoflegends.com/cdn/15.11.1/data/en_US/champion.json'
  },
  {
    name: 'Items List',
    url: 'https://ddragon.leagueoflegends.com/cdn/15.11.1/data/en_US/item.json'
  }
];

function testUrl(endpoint) {
  return new Promise((resolve) => {
    const startTime = Date.now();
    
    https.get(endpoint.url, (res) => {
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      let data = '';
      res.on('data', chunk => data += chunk);
      
      res.on('end', () => {
        const result = {
          name: endpoint.name,
          status: res.statusCode,
          responseTime: `${responseTime}ms`,
          success: res.statusCode === 200,
          dataSize: data.length
        };
        
        if (res.statusCode === 200 && data.length > 0) {
          try {
            const parsed = JSON.parse(data);
            if (parsed.data) {
              result.itemCount = Object.keys(parsed.data).length;
            }
            if (parsed.length) {
              result.itemCount = parsed.length;
            }
          } catch (e) {
            // Not JSON or different structure
          }
        }
        
        resolve(result);
      });
    }).on('error', (err) => {
      resolve({
        name: endpoint.name,
        status: 'ERROR',
        success: false,
        error: err.message
      });
    });
  });
}

async function runTests() {
  console.log('🚀 Testing League of Legends API Integration\n');
  
  console.log('📊 Testing Data Dragon API Endpoints:');
  console.log('=' .repeat(50));
  
  for (const endpoint of testEndpoints) {
    const result = await testUrl(endpoint);
    const status = result.success ? '✅' : '❌';
    
    console.log(`${status} ${result.name}`);
    console.log(`   Status: ${result.status}`);
    console.log(`   Response Time: ${result.responseTime}`);
    
    if (result.success) {
      console.log(`   Data Size: ${result.dataSize} bytes`);
      if (result.itemCount) {
        console.log(`   Items: ${result.itemCount}`);
      }
    } else if (result.error) {
      console.log(`   Error: ${result.error}`);
    }
    
    console.log('');
  }
  
  console.log('🎉 API test completed!');
}

runTests().catch(console.error);
