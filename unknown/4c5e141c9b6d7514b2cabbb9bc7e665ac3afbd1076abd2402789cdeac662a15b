"use client"

import React, { useState } from "react"
import Image from "next/image"
import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ProcessedChromaData } from "@/lib/types/league-api"
import { formatChromaPrice, getColorDisplayName } from "@/lib/utils/chroma-utils"
import { createChromaURL } from "@/lib/utils/chroma-url-utils"
import { Palette, Bell, ExternalLink } from "lucide-react"
import { MythicCostData } from "@/lib/services/mythic-cost-service"

interface ChromaCardProps {
  chroma: ProcessedChromaData
  onClick?: (chroma: ProcessedChromaData) => void
  showChampionName?: boolean
  showSkinName?: boolean
  size?: 'small' | 'medium' | 'large'
  mythicPrice?: MythicCostData | null
}

export default function ChromaCard({
  chroma,
  onClick,
  showChampionName = true,
  showSkinName = true,
  size = 'medium',
  mythicPrice
}: ChromaCardProps) {
  const [imageError, setImageError] = useState(false)
  const [isHovered, setIsHovered] = useState(false)

  const handleClick = () => {
    if (onClick) {
      onClick(chroma)
    }
    // Link navigation is now handled by the Link component
  }

  const handleImageError = () => {
    setImageError(true)
  }

  // Size configurations
  const sizeConfig = {
    small: {
      height: 'h-32',
      padding: 'p-2',
      textSize: 'text-xs',
      titleSize: 'text-sm'
    },
    medium: {
      height: 'h-40',
      padding: 'p-3',
      textSize: 'text-sm',
      titleSize: 'text-base'
    },
    large: {
      height: 'h-48',
      padding: 'p-4',
      textSize: 'text-base',
      titleSize: 'text-lg'
    }
  }

  const config = sizeConfig[size]

  // Get tier color for badge
  const getTierColor = (tier: string) => {
    const colors: Record<string, string> = {
      'Regular': 'bg-green-600',
      'Epic': 'bg-blue-600',
      'Legendary': 'bg-orange-600',
      'Mythic': 'bg-purple-600',
      'Ultimate': 'bg-yellow-600',
      'Exalted': 'bg-emerald-600',
      'Transcendent': 'bg-pink-600',
      'N/A': 'bg-gray-600',
      'Legacy': 'bg-yellow-600'
    }
    return colors[tier] || 'bg-gray-600'
  }

  // If onClick is provided, use the Card with onClick, otherwise use Link
  if (onClick) {
    return (
      <Card
        className="bg-gray-900/40 border-purple-700/20 hover:border-purple-400/50 hover:-translate-y-2 transition-all duration-300 group cursor-pointer"
        onClick={handleClick}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
      <CardContent className={`${config.padding} h-full flex flex-col`}>
        {/* Image Container */}
        <div className={`w-full ${config.height} rounded-lg overflow-hidden bg-gray-800/50 mb-3 relative`}>
          {!imageError ? (
            <Image
              src={chroma.image}
              alt={`${chroma.name} - ${chroma.skinName}`}
              fill
              className={`object-cover transition-transform duration-300 ${
                isHovered ? 'scale-110' : 'scale-105'
              }`}
              onError={handleImageError}
              onContextMenu={(e) => e.preventDefault()}
              draggable={false}
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-gray-800/50">
              <div className="relative w-16 h-16">
                <Image
                  src="https://raw.communitydragon.org/latest/game/assets/loadouts/summoneremotes/champions/amumu/amumu_sad_crying_glow.png"
                  alt="Failed to load glow"
                  width={64}
                  height={64}
                  className="absolute inset-0 opacity-60"
                  unoptimized
                />
                <Image
                  src="https://raw.communitydragon.org/latest/game/assets/loadouts/summoneremotes/champions/amumu/amumu_sad_crying_vfx.png"
                  alt="Failed to load"
                  width={64}
                  height={64}
                  className="relative z-10"
                  unoptimized
                />
              </div>
            </div>
          )}

          {/* Color Indicator */}
          <div className="absolute top-2 left-2 flex space-x-1">
            {chroma.colors.slice(0, 3).map((color, index) => (
              <div
                key={index}
                className="w-4 h-4 rounded-full border-2 border-white/50 shadow-lg"
                style={{ backgroundColor: color }}
                title={getColorDisplayName(color)}
              />
            ))}
            {chroma.colors.length > 3 && (
              <div className="w-4 h-4 rounded-full bg-gray-600 border-2 border-white/50 shadow-lg flex items-center justify-center">
                <span className="text-xs text-white font-bold">+</span>
              </div>
            )}
          </div>

          {/* Default Badge */}
          {chroma.isDefault && (
            <div className="absolute top-2 right-2">
              <Badge variant="secondary" className="text-xs bg-blue-600 text-white">
                Default
              </Badge>
            </div>
          )}
        </div>

        {/* Content */}
        <div className="flex-1 flex flex-col justify-between">
          <div>
            {/* Chroma Name */}
            <h3 className={`${config.titleSize} font-semibold text-white mb-1 line-clamp-1`}>
              {chroma.name}
            </h3>

            {/* Champion Name */}
            {showChampionName && (
              <p className={`${config.textSize} text-gray-300 mb-1 line-clamp-1`}>
                {chroma.champion}
              </p>
            )}

            {/* Skin Name */}
            {showSkinName && (
              <p className={`${config.textSize} text-gray-400 mb-2 line-clamp-1`}>
                {chroma.skinName}
              </p>
            )}
          </div>

          {/* Bottom Section */}
          <div className="space-y-2">
            {/* Badges */}
            <div className="flex flex-wrap gap-1">
              {/* Skin Tier Badge - Only show if not N/A */}
              {chroma.skinTier !== 'N/A' && (
                <Badge
                  variant="secondary"
                  className={`text-xs ${getTierColor(chroma.skinTier)} text-white`}
                >
                  {chroma.skinTier}
                </Badge>
              )}

              {/* Set Badge */}
              {chroma.set && (
                <Badge variant="outline" className="text-xs border-gray-600 text-gray-300">
                  {chroma.set}
                </Badge>
              )}

              {/* Availability Badge */}
              {chroma.availability !== 'Available' && (
                <Badge
                  variant="secondary"
                  className={`text-xs ${
                    chroma.availability === 'Legacy' ? 'bg-yellow-600' : 'bg-red-600'
                  } text-white`}
                >
                  {chroma.availability}
                </Badge>
              )}
            </div>

            {/* Price */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-1">
                {(() => {
                  // Check if chroma has mythic price
                  if (chroma.availability !== 'Available' && mythicPrice) {
                    // Show mythic price
                    return (
                      <>
                        <Image
                          src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/mythic.svg"
                          alt="ME"
                          width={14}
                          height={14}
                          className="h-3.5 w-3.5"
                        />
                        <span className={`${config.textSize} font-medium`} style={{ color: '#b47bdf' }}>
                          {mythicPrice.cost}
                        </span>
                      </>
                    )
                  } else if (formatChromaPrice(chroma.price) === 'N/A') {
                    // Show N/A for legacy items without mythic price
                    return (
                      <span className={`${config.textSize} text-gray-400 font-medium`}>
                        N/A
                      </span>
                    )
                  } else {
                    // Show regular RP price
                    return (
                      <>
                        <Image
                          src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg"
                          alt="RP"
                          width={14}
                          height={14}
                          className="h-3.5 w-3.5"
                        />
                        <span className={`${config.textSize} font-medium`} style={{ color: 'rgb(251, 146, 60)' }}>
                          {formatChromaPrice(chroma.price).replace(' RP', '')}
                        </span>
                      </>
                    )
                  }
                })()}
              </div>

              {/* Action Icons */}
              <div className="flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                <button
                  className="p-1 rounded-full bg-gray-600/20 cursor-not-allowed opacity-50"
                  onClick={(e) => {
                    e.stopPropagation()
                    // Add to watchlist logic - disabled
                  }}
                  title="Add to Watchlist (Coming Soon)"
                  disabled
                >
                  <Bell className="h-3 w-3 text-gray-500" />
                </button>
                <button
                  className="p-1 rounded-full bg-purple-600/20 hover:bg-purple-600/40 transition-colors"
                  onClick={(e) => {
                    e.stopPropagation()
                    // View details logic
                  }}
                  title="View Details"
                >
                  <ExternalLink className="h-3 w-3 text-purple-400" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
    )
  }

  // Default case: use Link for navigation
  const slug = createChromaURL(chroma)
  return (
    <Link href={`/skins/chromas/${slug}`} className="block">
      <Card
        className="bg-gray-900/40 border-purple-700/20 hover:border-purple-400/50 hover:-translate-y-2 transition-all duration-300 group cursor-pointer"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <CardContent className={`${config.padding} h-full flex flex-col`}>
          {/* Image Container */}
          <div className={`w-full ${config.height} rounded-lg overflow-hidden bg-gray-800/50 mb-3 relative`}>
            {!imageError ? (
              <Image
                src={chroma.image}
                alt={`${chroma.name} - ${chroma.skinName}`}
                fill
                className={`object-cover transition-transform duration-300 ${
                  isHovered ? 'scale-110' : 'scale-105'
                }`}
                onError={handleImageError}
              />
            ) : (
              <div className="w-full h-full bg-gray-700/50 flex items-center justify-center">
                <Palette className="h-8 w-8 text-gray-500" />
              </div>
            )}

            {/* Color Indicator */}
            <div className="absolute top-2 left-2 flex space-x-1">
              {chroma.colors.slice(0, 3).map((color, index) => (
                <div
                  key={index}
                  className="w-4 h-4 rounded-full border-2 border-white/50 shadow-lg"
                  style={{ backgroundColor: color }}
                  title={getColorDisplayName(color)}
                />
              ))}
              {chroma.colors.length > 3 && (
                <div className="w-4 h-4 rounded-full bg-gray-600 border-2 border-white/50 shadow-lg flex items-center justify-center">
                  <span className="text-xs text-white font-bold">+</span>
                </div>
              )}
            </div>

            {/* Default Badge */}
            {chroma.isDefault && (
              <div className="absolute top-2 right-2">
                <Badge variant="secondary" className="text-xs bg-blue-600 text-white">
                  Default
                </Badge>
              </div>
            )}
          </div>

          {/* Content */}
          <div className="flex-1 flex flex-col">
            {/* Names */}
            <div className="mb-2">
              {showChampionName && (
                <p className="text-gray-400 mb-1 text-xs">
                  {chroma.champion}
                </p>
              )}
              {showSkinName && (
                <p className="text-gray-300 mb-1 text-xs">
                  {chroma.skinName}
                </p>
              )}
              <h3 className="font-semibold text-white group-hover:text-purple-300 transition-colors text-sm">
                {getColorDisplayName(chroma.color)} Chroma
              </h3>
            </div>

            {/* Bottom section with price and actions */}
            <div className="mt-auto">
              {/* Price */}
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-1">
                  <span className="text-orange-400 font-medium text-sm">
                    {formatChromaPrice(chroma.price, chroma.availability)}
                  </span>
                </div>

                {/* Action Icons */}
                <div className="flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                  <button
                    className="p-1 rounded-full bg-gray-600/20 cursor-not-allowed opacity-50"
                    onClick={(e) => {
                      e.stopPropagation()
                      e.preventDefault()
                      // Add to watchlist logic - disabled
                    }}
                    title="Add to Watchlist (Coming Soon)"
                    disabled
                  >
                    <Bell className="h-3 w-3 text-gray-500" />
                  </button>
                  <button
                    className="p-1 rounded-full bg-purple-600/20 hover:bg-purple-600/40 transition-colors"
                    onClick={(e) => {
                      e.stopPropagation()
                      e.preventDefault()
                      // View details logic
                    }}
                    title="View Details"
                  >
                    <ExternalLink className="h-3 w-3 text-purple-400" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </Link>
  )
}
