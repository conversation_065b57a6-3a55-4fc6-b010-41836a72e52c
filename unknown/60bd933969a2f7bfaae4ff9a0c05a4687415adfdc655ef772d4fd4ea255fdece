import { NextRequest, NextResponse } from 'next/server'

interface RouteParams {
  params: Promise<{
    itemId: string
  }>
}

interface SkinSalesResponse {
  success: boolean
  data?: {
    cost: number
    beforeSale: number
    currency: string
    percentOff: number
    endDate: string
  }
  error?: string
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { itemId } = await params
    
    if (!itemId) {
      return NextResponse.json({
        success: false,
        error: 'Item ID is required'
      }, { status: 400 })
    }

    // Validate itemId is a number
    const numericItemId = parseInt(itemId)
    if (isNaN(numericItemId)) {
      return NextResponse.json({
        success: false,
        error: 'Invalid item ID format'
      }, { status: 400 })
    }


    // Fetch skin sales data from the API
    const salesApiUrl = `https://api.loldb.info/api/skin-sales/${itemId}`
    const response = await fetch(salesApiUrl)

    if (!response.ok) {
      console.warn(`⚠️ Sales API returned ${response.status} for item ID ${itemId}`)
      return NextResponse.json({
        success: false,
        error: `Failed to fetch sales data: HTTP ${response.status}`
      }, { status: response.status })
    }

    const salesData: SkinSalesResponse = await response.json()


    // Return the response as-is from the external API
    return NextResponse.json(salesData, {
      headers: {
        'Cache-Control': 'public, s-maxage=3600, stale-while-revalidate=7200', // 1h cache, 2h stale
        'CDN-Cache-Control': 'public, s-maxage=3600',
        'Vercel-CDN-Cache-Control': 'public, s-maxage=3600'
      }
    })

  } catch (error) {
    console.error('❌ Failed to fetch skin sales data:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error while fetching sales data'
    }, { status: 500 })
  }
}
