"use client"

import SharedLayout from "@/components/shared-layout"
import { ChevronLeft, ChevronRight } from "lucide-react"
import Image from "next/image"
import { useEffect, useState } from "react"

interface CarouselBanner {
  id: number
  title: string
  imgUrl: string
  type: string
  destination: string
  pageType?: string
  items?: Array<{
    inventoryType: string
    itemId: number
  }>
}

interface BundleItem {
  itemId: number
  inventoryType: string
  iconUrl: string
  rp: number
  name: string
  description?: string
  quantity: number
  discountedRp: number
  hasVelocityRules: boolean
}

interface CatalogItem {
  itemId: number
  inventoryType: string
  iconUrl: string
  rp: number
  name: string
  description?: string
  tags?: string[]
  maxQuantity?: number
  releaseDate?: number
  inactiveDate?: string
  minimumLevel?: number
  bundleDiscountEnabled?: boolean
  bundleDiscountMinCost?: number
  purchaseLimitReached: boolean
  bundleItems?: BundleItem[]
  fullPrice?: number
  disclaimerId?: string
  owned?: boolean
  hasVelocityRules: boolean
  metadata?: Array<{
    type: string
    value: string
  }>
  sale?: {
    rp: number
    percentOff: number
    endDate: string
  }
}

interface FeaturedSection {
  title: string
  sectionId: string
  callouts: Array<{
    title?: string
    endDate: string
    size: string
    itemId: number
    inventoryType: string
  }>
}

interface StoreApiResponse {
  success: boolean
  message: string
  timestamp: string
  data: {
    player: {
      accountId: number
      rp: number
      ip: number
      summonerLevel: number
    }
    catalog: CatalogItem[]
    layout: string
    carousel: {
      banners: CarouselBanner[]
    }
    featuredSections: FeaturedSection[]
  }
}

// Utility function to format image URLs
const formatImageUrl = (iconUrl: string): string => {
  if (!iconUrl) {
    return 'https://via.placeholder.com/300x200?text=No+Image'
  }

  if (iconUrl.startsWith('//')) {
    return `https:${iconUrl}`
  } else if (iconUrl.startsWith('http')) {
    return iconUrl
  } else {
    return `https://d392eissrffsyf.cloudfront.net/storeImages/bundles/${iconUrl}`
  }
}

export default function ShopPage() {
  const [currentBannerIndex, setCurrentBannerIndex] = useState<number>(0)
  const [isAnimating, setIsAnimating] = useState<boolean>(false)
  const [carouselBanners, setCarouselBanners] = useState<CarouselBanner[]>([])
  const [bestSellingItems, setBestSellingItems] = useState<CatalogItem[]>([])
  const [featuredItems, setFeaturedItems] = useState<CatalogItem[]>([])
  const [loading, setLoading] = useState<boolean>(true)

  // Load carousel banners and catalog data from API
  useEffect(() => {
    const loadFeaturedData = async (): Promise<void> => {
      try {
        const response = await fetch(`https://api.loldb.info/api/store/featured?t=${Date.now()}`)
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: Failed to fetch featured data`)
        }

        const apiResponse: StoreApiResponse = await response.json()

        if (!apiResponse.success) {
          throw new Error(apiResponse.message || 'API returned unsuccessful response')
        }

        const featuredData = apiResponse.data

        setCarouselBanners(featuredData.carousel?.banners || [])

        // Extract featured items from featuredSections
        // These items will be displayed in the Featured section (right side of bento grid)
        // and removed from Catalog to avoid duplication
        const featuredSections: FeaturedSection[] = featuredData.featuredSections || []
        const featuredItemIds = new Set<number>()

        // Get items from 'whatsnew' and 'featured' sections
        featuredSections.forEach((section: FeaturedSection) => {
          if (section.sectionId === 'whatsnew' || section.sectionId === 'featured') {
            section.callouts?.forEach((callout) => {
              if (callout.inventoryType === 'BUNDLES') {
                featuredItemIds.add(callout.itemId)
              }
            })
          }
        })

        // Filter catalog items for bundles only
        const catalogItems: CatalogItem[] = featuredData.catalog || []
        const allBundleItems: CatalogItem[] = catalogItems
          .filter((item: CatalogItem) => item.inventoryType === 'BUNDLES')

        // Separate featured items from regular bundle items
        const featuredBundleItems: CatalogItem[] = allBundleItems.filter((item: CatalogItem) =>
          featuredItemIds.has(item.itemId)
        )
        const bestSellingBundleItems: CatalogItem[] = allBundleItems.filter((item: CatalogItem) =>
          !featuredItemIds.has(item.itemId)
        )

        setFeaturedItems(featuredBundleItems)
        setBestSellingItems(bestSellingBundleItems)
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
        console.error('Error loading featured data:', errorMessage)
        setCarouselBanners([])
        setBestSellingItems([])
        setFeaturedItems([])
      } finally {
        setLoading(false)
      }
    }

    loadFeaturedData()
  }, [])

  // Auto-rotate carousel every 5 seconds
  useEffect(() => {
    if (carouselBanners.length <= 1) return

    const interval = setInterval(() => {
      if (!isAnimating) {
        setCurrentBannerIndex((prev) => (prev + 1) % carouselBanners.length)
      }
    }, 5000)

    return () => clearInterval(interval)
  }, [carouselBanners.length, isAnimating])

  const handleBannerNavigation = (direction: 'prev' | 'next') => {
    if (isAnimating || carouselBanners.length <= 1) return

    setIsAnimating(true)
    setCurrentBannerIndex((prev) => {
      if (direction === 'prev') {
        return prev === 0 ? carouselBanners.length - 1 : prev - 1
      } else {
        return (prev + 1) % carouselBanners.length
      }
    })

    setTimeout(() => setIsAnimating(false), 300)
  }

  // Render bundle item card with full-size image and overlay text
  const renderBundleCard = (item: CatalogItem, index: number): React.JSX.Element | null => {
    // Add safety check for item
    if (!item || !item.itemId) {
      return null
    }

    const imageUrl: string = formatImageUrl(item.iconUrl)

    return (
      <div
        key={item.itemId}
        className="h-64 bg-gradient-to-br from-gray-900/60 to-gray-800/40 border border-amber-700/30 rounded-xl overflow-hidden relative group cursor-default hover:border-amber-700/50 transition-all duration-500 transform hover:scale-105"
      >
        {/* Full-size Image */}
        <div className="w-full h-full relative">
          <Image
            src={imageUrl}
            alt={item.name || 'Bundle'}
            fill
            className="object-cover transition-transform duration-300 group-hover:scale-110"

          />

          {/* Overlay with gradient and text */}
          <div className="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/90 via-black/70 to-transparent">
            {item.name && (
              <h3 className="text-white font-semibold text-sm mb-2 line-clamp-2">
                {item.name}
              </h3>
            )}
            <div className="flex items-center space-x-1">
              <Image
                src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg"
                alt="RP"
                width={16}
                height={16}
                className="w-4 h-4"
              />
              <span className="text-amber-400 font-bold text-sm">{item.rp || 0}</span>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Render featured item card (smaller version for the 4 featured slots)
  const renderFeaturedCard = (item: CatalogItem, index: number): React.JSX.Element | null => {
    // Add safety check for item
    if (!item || !item.itemId) {
      return null
    }

    const imageUrl: string = formatImageUrl(item.iconUrl)

    return (
      <div
        key={item.itemId}
        className="bg-gradient-to-br from-gray-900/60 to-gray-800/40 border border-amber-700/30 rounded-xl overflow-hidden relative group cursor-default hover:border-amber-700/50 transition-all duration-500 transform hover:scale-105"
      >
        {/* Full-size Image */}
        <div className="w-full h-full relative">
          <Image
            src={imageUrl}
            alt={item.name || 'Featured Bundle'}
            fill
            className="object-cover transition-transform duration-300 group-hover:scale-110"
          />

          {/* Overlay with gradient and text */}
          <div className="absolute bottom-0 left-0 right-0 p-2 bg-gradient-to-t from-black/90 via-black/70 to-transparent">
            {item.name && (
              <h3 className="text-white font-semibold text-sm mb-1 line-clamp-2">
                {item.name}
              </h3>
            )}
            <div className="flex items-center space-x-1">
              <Image
                src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg"
                alt="RP"
                width={16}
                height={16}
                className="w-4 h-4"
              />
              <span className="text-amber-400 font-bold text-sm">{item.rp || 0}</span>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <SharedLayout>
      <div className="container mx-auto px-4 sm:px-6 md:px-8 py-0 pb-8 mobile-no-top-padding max-w-full overflow-hidden">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-[32px] h-[32px] text-amber-400" style={{
              maskImage: `url(https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/nav-icon-store.svg)`,
              maskRepeat: 'no-repeat',
              maskPosition: 'center',
              maskSize: 'contain',
              backgroundColor: 'currentColor'
            }} />
            <h1 className="text-4xl font-bold text-white">Shop</h1>
          </div>
          <p className="text-gray-300 text-lg">Browse current offers, bundles, and featured content</p>
        </div>

        <div className="space-y-16">
          {/* Featured Section (Carousel) */}
          <section>
            <div className="flex items-center space-x-3 mb-8">
              <h2 className="text-3xl font-bold text-white">Featured</h2>
            </div>

            {/* Featured Carousel - Full width on mobile, part of bento grid on desktop */}
            <div className="lg:grid lg:grid-cols-4 lg:grid-rows-2 lg:gap-4 lg:h-[500px] mb-8">
              {/* Featured Carousel */}
              <div className="lg:col-span-2 lg:row-span-2 h-64 lg:h-auto bg-gradient-to-br from-gray-900/60 to-gray-800/40 border border-amber-700/30 rounded-xl overflow-hidden relative cursor-default mb-6 lg:mb-0">
                {loading ? (
                  <div className="w-full h-full flex items-center justify-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-400"></div>
                  </div>
                ) : carouselBanners.length > 0 ? (
                  <>
                    {/* Current Banner Image */}
                    <div className="w-full h-full relative">
                      <Image
                        src={`https:${carouselBanners[currentBannerIndex].imgUrl}`}
                        alt={carouselBanners[currentBannerIndex].title}
                        fill
                        className="object-cover transition-opacity duration-500"
                        priority
                      />
                    </div>

                    {/* Navigation Arrows */}
                    {carouselBanners.length > 1 && (
                      <>
                        <button
                          onClick={() => handleBannerNavigation('prev')}
                          disabled={isAnimating}
                          className="absolute left-2 top-1/2 -translate-y-1/2 w-8 h-8 rounded-full bg-black/50 hover:bg-black/70 border border-amber-500/50 hover:border-amber-400 flex items-center justify-center transition-all duration-200 disabled:opacity-50 text-white shadow-lg backdrop-blur-sm"
                        >
                          <ChevronLeft size={16} />
                        </button>

                        <button
                          onClick={() => handleBannerNavigation('next')}
                          disabled={isAnimating}
                          className="absolute right-2 top-1/2 -translate-y-1/2 w-8 h-8 rounded-full bg-black/50 hover:bg-black/70 border border-amber-500/50 hover:border-amber-400 flex items-center justify-center transition-all duration-200 disabled:opacity-50 text-white shadow-lg backdrop-blur-sm"
                        >
                          <ChevronRight size={16} />
                        </button>
                      </>
                    )}

                    {/* Dots Indicator */}
                    {carouselBanners.length > 1 && (
                      <div className="absolute bottom-2 left-1/2 -translate-x-1/2 flex space-x-2">
                        {carouselBanners.map((_, index) => (
                          <button
                            key={index}
                            onClick={() => !isAnimating && setCurrentBannerIndex(index)}
                            className={`w-2 h-2 rounded-full transition-all duration-200 ${
                              index === currentBannerIndex
                                ? 'bg-amber-400 scale-125'
                                : 'bg-white/50 hover:bg-white/70'
                            }`}
                          />
                        ))}
                      </div>
                    )}
                  </>
                ) : (
                  <div className="w-full h-full flex items-center justify-center">
                    <p className="text-gray-400 text-lg">No Featured Items</p>
                  </div>
                )}
              </div>

              {/* Desktop: Featured items in bento grid (right side) */}
              <div className="hidden lg:contents">
                {loading ? (
                  // Loading state for featured items
                  Array.from({ length: 4 }, (_, index) => (
                    <div key={index} className="bg-gradient-to-br from-gray-900/60 to-gray-800/40 border border-amber-700/30 rounded-xl overflow-hidden relative">
                      <div className="w-full h-full relative">
                        <div className="absolute inset-0 bg-gray-800/50 animate-pulse" />
                        <div className="absolute bottom-0 left-0 right-0 p-2 bg-gradient-to-t from-black/90 via-black/70 to-transparent">
                          <div className="h-3 bg-gray-600/50 rounded mb-1 animate-pulse" />
                          <div className="h-2 bg-gray-600/50 rounded w-12 animate-pulse" />
                        </div>
                      </div>
                    </div>
                  ))
                ) : featuredItems.length > 0 ? (
                  // Display actual featured items
                  featuredItems.slice(0, 4).map((item, index) => renderFeaturedCard(item, index))
                ) : (
                  // Fallback when no featured items
                  Array.from({ length: 4 }, (_, index) => (
                    <div key={index} className="bg-gradient-to-br from-gray-900/60 to-gray-800/40 border border-amber-700/30 rounded-xl overflow-hidden relative group cursor-default hover:border-amber-700/50 transition-all duration-500 transform hover:scale-105">
                      <div className="w-full h-full flex items-center justify-center">
                        <p className="text-gray-400 text-sm">Featured Item {index + 1}</p>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>

            {/* Mobile: Featured items in responsive grid (below carousel) */}
            <div className="lg:hidden">
              {loading ? (
                <div className="grid grid-cols-1 gap-6">
                  {Array.from({ length: 4 }, (_, index) => (
                    <div key={index} className="h-64 bg-gradient-to-br from-gray-900/60 to-gray-800/40 border border-amber-700/30 rounded-xl overflow-hidden relative">
                      <div className="w-full h-full relative">
                        <div className="absolute inset-0 bg-gray-800/50 animate-pulse" />
                        <div className="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/90 via-black/70 to-transparent">
                          <div className="h-4 bg-gray-600/50 rounded mb-2 animate-pulse" />
                          <div className="h-3 bg-gray-600/50 rounded w-16 animate-pulse" />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : featuredItems.length > 0 ? (
                <div className="grid grid-cols-1 gap-6">
                  {featuredItems.slice(0, 4).map((item, index) => (
                    <div
                      key={item.itemId}
                      className="h-64 bg-gradient-to-br from-gray-900/60 to-gray-800/40 border border-amber-700/30 rounded-xl overflow-hidden relative group cursor-default hover:border-amber-700/50 transition-all duration-500 transform hover:scale-105"
                    >
                      {/* Full-size Image */}
                      <div className="w-full h-full relative">
                        <Image
                          src={formatImageUrl(item.iconUrl)}
                          alt={item.name || 'Featured Bundle'}
                          fill
                          className="object-cover transition-transform duration-300 group-hover:scale-110"
                        />

                        {/* Overlay with gradient and text */}
                        <div className="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/90 via-black/70 to-transparent">
                          {item.name && (
                            <h3 className="text-white font-semibold text-lg mb-2 line-clamp-2">
                              {item.name}
                            </h3>
                          )}
                          <div className="flex items-center space-x-2">
                            <Image
                              src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg"
                              alt="RP"
                              width={20}
                              height={20}
                              className="w-5 h-5"
                            />
                            <span className="text-amber-400 font-bold text-lg">{item.rp || 0}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="grid grid-cols-1 gap-6">
                  {Array.from({ length: 4 }, (_, index) => (
                    <div key={index} className="h-64 bg-gradient-to-br from-gray-900/60 to-gray-800/40 border border-amber-700/30 rounded-xl overflow-hidden relative group cursor-default hover:border-amber-700/50 transition-all duration-500 transform hover:scale-105">
                      <div className="w-full h-full flex items-center justify-center">
                        <p className="text-gray-400 text-lg">Featured Item {index + 1}</p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </section>

          {/* Catalog Section (Catalog) */}
          <section>
            <div className="flex items-center space-x-3 mb-8">
              <h2 className="text-3xl font-bold text-white">Catalog</h2>
            </div>

            {loading ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
                {Array.from({ length: 12 }, (_, index) => (
                  <div key={index} className="h-64 bg-gradient-to-br from-gray-900/60 to-gray-800/40 border border-amber-700/30 rounded-xl overflow-hidden relative">
                    <div className="w-full h-full relative">
                      <div className="absolute inset-0 bg-gray-800/50 animate-pulse" />
                      <div className="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/90 via-black/70 to-transparent">
                        <div className="h-4 bg-gray-600/50 rounded mb-2 animate-pulse" />
                        <div className="h-3 bg-gray-600/50 rounded w-16 animate-pulse" />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : bestSellingItems.length > 0 ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
                {bestSellingItems.map((item, index) => renderBundleCard(item, index)).filter(Boolean)}
              </div>
            ) : (
              <div className="bg-gradient-to-br from-gray-900/60 to-gray-800/40 border border-amber-700/30 rounded-xl p-16 text-center">
                <p className="text-gray-400 text-lg">
                  No Catalog items available (Found {bestSellingItems.length} items)
                </p>
              </div>
            )}
          </section>


        </div>
      </div>
    </SharedLayout>
  )
}
