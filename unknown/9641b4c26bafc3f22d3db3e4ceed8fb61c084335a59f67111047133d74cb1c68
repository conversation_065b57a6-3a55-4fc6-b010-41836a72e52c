import type { Metadata, Viewport } from 'next'
import './globals.css'
import GoogleAnalytics from '@/components/google-analytics'
import MicrosoftClarity from '@/components/microsoft-clarity'

export const metadata: Metadata = {
  title: 'LoLDB - Your Ultimate League of Legends Database',
  description: 'LoLDB, the complete League of Legends database with champions, skins, chromas, items, mythic shop content, and more! Find everything you need for League.',
  generator: 'Next.js',
  keywords: ['League of Legends', 'LoL', 'Champions', 'Items', 'Database', 'Riot Games', 'Skins', 'Chromas', 'Mythic Shop'],
  authors: [{ name: 'LoLDB Team' }],
  creator: 'LoLD<PERSON>',
  publisher: 'LoLDB',
  applicationName: 'LoLDB',
  referrer: 'origin-when-cross-origin',
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  icons: {
    icon: '/favicon.ico',
    shortcut: '/favicon.ico',
    apple: '/LoLDB-PNG.png',
  },
  manifest: '/manifest.json',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  openGraph: {
    type: 'website',
    siteName: 'LoLDB',
    title: 'LoLDB - Your Ultimate League of Legends Database',
    description: 'LoLDB, the complete League of Legends database with champions, skins, chromas, items, mythic shop content, and more! Find everything you need for League.',
    url: 'https://loldb.info',
    images: [
      {
        url: '/images/loldb-og-image.png',
        width: 1200,
        height: 630,
        alt: 'LoLDB - Ultimate League of Legends Database',
      },
    ],
    locale: 'en_US',
  },
  twitter: {
    card: 'summary_large_image',
    site: '@loldb',
    creator: '@loldb',
    title: 'LoLDB - Your Ultimate League of Legends Database',
    description: 'LoLDB, the complete League of Legends database with champions, skins, chromas, items, mythic shop content, and more! Find everything you need for League.',
    images: ['/images/loldb-og-image.png'],
  },
}

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
  userScalable: true,
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#f0e6d2' },
    { media: '(prefers-color-scheme: dark)', color: '#1e2328' },
  ],
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" className="h-full scroll-smooth">
      <body className="min-h-screen" suppressHydrationWarning={true}>
        <GoogleAnalytics />
        <MicrosoftClarity />
        {children}
      </body>
    </html>
  )
}
