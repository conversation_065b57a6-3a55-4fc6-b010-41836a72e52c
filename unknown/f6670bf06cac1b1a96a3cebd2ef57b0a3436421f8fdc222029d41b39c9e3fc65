import { storeService } from '@/lib/services/store-service'
import { NextResponse } from 'next/server'

export async function GET() {
  try {
    

    const processedMythicStorefront =
      await storeService.getProcessedMythicStorefront()

    const responsePayload = {
      success: true,
      data: {
        storefront: {
          mythicshop: processedMythicStorefront,
        },
      },
      timestamp: new Date().toISOString(),
    }

    

    return NextResponse.json(responsePayload, {
      headers: {
        'Cache-Control': 'public, s-maxage=60, stale-while-revalidate=86400',
        'CDN-Cache-Control': 'public, s-maxage=60',
        'Vercel-CDN-Cache-Control': 'public, s-maxage=60',
      },
    })
  } catch (error) {
    console.error('❌ Error processing storefront data:', error)
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    console.error('Error details:', error);

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to process storefront data',
        message: errorMessage,
      },
      {
        status: 500,
        headers: {
          'Cache-Control': 'no-cache',
        },
      }
    )
  }
}
