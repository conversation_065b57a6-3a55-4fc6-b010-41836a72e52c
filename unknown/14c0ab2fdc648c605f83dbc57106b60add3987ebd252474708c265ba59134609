"use client"

import * as React from "react"
import { X } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { ChromaRarityIconsSimple } from "@/components/ui/chroma-rarity-icons"
import { ChromaFilters } from "@/hooks/use-chroma-filters"
import { useChromas } from "@/hooks/use-chromas"
import { getActualChromaColorName } from "@/lib/utils/chroma-utils"

interface SelectedChromaFiltersDisplayProps {
  filters: ChromaFilters
  onRemoveFilter: (filterType: keyof ChromaFilters, value: string) => void
  onClearCategory: (filterType: keyof ChromaFilters) => void
}

export function SelectedChromaFiltersDisplay({
  filters,
  onRemoveFilter,
  onClearCategory
}: SelectedChromaFiltersDisplayProps) {
  const { chromas } = useChromas()

  // Get color hex value from actual chroma data - MUST be before early return
  const getColorHex = React.useMemo(() => {
    const colorMap: Record<string, string> = {}

    // Build color mapping from actual chroma data
    chromas.forEach(chroma => {
      const actualColorName = getActualChromaColorName(chroma.name)
      if (actualColorName && chroma.colors && chroma.colors.length > 0) {
        // Use the first color from the colors array
        colorMap[actualColorName] = chroma.colors[0]
      }
    })

    return (colorName: string): string => {
      return colorMap[colorName] || '#8b5cf6' // Default to purple if not found
    }
  }, [chromas])

  const hasFilters = filters.chromaRarities.length > 0 ||
                    filters.champions.length > 0 ||
                    filters.chromaColors.length > 0

  if (!hasFilters) {
    return null
  }

  const renderFilterGroup = (
    title: string,
    items: string[],
    filterType: keyof ChromaFilters,
    color: string,
    showIcons: boolean = false,
    showColors: boolean = false
  ) => {
    if (items.length === 0) return null

    return (
      <div className="flex items-center space-x-2">
        <span className="text-gray-400 text-sm font-medium">{title}:</span>
        <div className="flex items-center space-x-1 flex-wrap">
          {items.length <= 3 ? (
            items.map(item => (
              <Badge
                key={item}
                variant="secondary"
                className={`${color} text-xs flex items-center space-x-1 cursor-pointer hover:opacity-80 transition-opacity`}
                onClick={() => onRemoveFilter(filterType, item)}
              >
                {showIcons && item !== "Others" && (
                  <div className="w-3 h-3 flex items-center justify-center">
                    <ChromaRarityIconsSimple
                      rarity={item}
                      size={12}
                    />
                  </div>
                )}
                {showColors && (
                  <div
                    className="w-3 h-3 rounded-full border border-white/30 flex-shrink-0"
                    style={{ backgroundColor: getColorHex(item) }}
                  />
                )}
                <span>{item}</span>
                <X className="h-3 w-3" />
              </Badge>
            ))
          ) : (
            <Badge
              variant="secondary"
              className={`${color} text-xs flex items-center space-x-1 cursor-pointer hover:opacity-80 transition-opacity`}
              onClick={() => onClearCategory(filterType)}
            >
              <span>{items.length} Selected</span>
              <X className="h-3 w-3" />
            </Badge>
          )}
        </div>
      </div>
    )
  }

  return (
    <div className="mb-4 p-3 bg-gray-800/30 rounded-lg border border-gray-700/20">
      <div className="flex flex-wrap items-center gap-4">
        {renderFilterGroup(
          "Rarities",
          filters.chromaRarities,
          "chromaRarities",
          "bg-purple-600/20 text-purple-300 border-purple-600/30",
          true
        )}
        
        {renderFilterGroup(
          "Champions",
          filters.champions,
          "champions",
          "bg-blue-600/20 text-blue-300 border-blue-600/30"
        )}
        


        {renderFilterGroup(
          "Colors",
          filters.chromaColors,
          "chromaColors",
          "bg-orange-600/20 text-orange-300 border-orange-600/30",
          false,
          true
        )}
      </div>
    </div>
  )
}
