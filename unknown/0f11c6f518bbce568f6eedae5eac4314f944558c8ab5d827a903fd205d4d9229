import ChampionInfoServer from "./champion/champion-info-server"
import ChampionStatsServer from "./champion/champion-stats-server"
import ChampionAbilitiesServer from "./champion/champion-abilities-server"
import ChampionLoreServer from "./champion/champion-lore-server"
import ChampionInteractiveClient from "./champion/champion-interactive-client"

interface ChampionPageServerProps {
  champion: any
  championData: any
}

export default function ChampionPageServer({ champion, championData }: ChampionPageServerProps) {
  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 pt-2 pb-8">
      {/* Header with Skin Navigation */}
      <div className="mb-6 lg:mb-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8">
          {/* Champion Info - Server Side */}
          <div>
            <ChampionInfoServer champion={champion} championData={championData} />
          </div>

          {/* Skin Display - Client Side (Interactive) */}
          <div className="relative">
            <ChampionInteractiveClient
              skins={championData.skins}
              championName={champion.name}
              championKey={champion.key}
            />
          </div>
        </div>
      </div>

      {/* Champion Stats - Server Side */}
      <ChampionStatsServer championData={championData} />

      {/* Abilities Section - Server Side */}
      <ChampionAbilitiesServer championData={championData} />

      {/* Lore Section - Server Side */}
      <ChampionLoreServer championData={championData} />
    </div>
  )
}
