export interface Payment {
  currencyId: string
  delta: number
  discountPercent: number
  discountedDelta: number
  finalDelta: number
  itemId: string
  itemTypeId: string
  name: string
}

export interface PaymentOption {
  key: string
  payments: Payment[]
}

export interface Fulfillment {
  counterId: string
  currencyId: string
  delta: number
  displayMetadata: any
  dropTableId: string
  finalDelta: number
  itemId: string
  itemInstanceId: string
  itemPayload: {}
  itemTypeId: string
  location: string
  maxQuantity: number
  name: string
  ownedQuantity: number
  ownershipCompensationMode: string
  payload: any
  progressionCounterId: string
  results: {
    rolls: any[]
  }
  subCurrencyDeltas: {}
  target: string
  tierTypeId: string
}

export interface PurchaseUnit {
  fulfillment: Fulfillment
  paymentOptions: PaymentOption[]
}

export interface CatalogEntryDisplayMetadata {
    purchaseRule?: string;
    currencyIds?: string[];
    description?: string;
    shoppefront?: {
        bundleType?: string;
    };
    typeId?: boolean;
    assetsByCatalogEntry?: Record<string, any>;
}

export interface CatalogEntry {
  displayMetadata: CatalogEntryDisplayMetadata
  endTime: string
  giftRule: string
  id: string
  name: string
  prerequisites: any[]
  productId: string
  purchaseLimits: any[]
  purchaseUnits: PurchaseUnit[]
  purchaseVisibility: string
  refundLimits: any[]
  refundRule: string
}

export interface StorefrontDisplayMetadata {
    description: string;
    endDate: string;
    shoppefront: {
        categories: string[];
        id: string;
        overrideTileSize?: string;
        storeOrder?: number;
    };
    startDate: string;
}

export interface RawStorefront {
  catalogEntries: CatalogEntry[]
  displayMetadata: StorefrontDisplayMetadata
  id: string
  name: string
  productId: string
}

export interface RawStorefrontResponse {
  data: RawStorefront[]
  notes: any[]
  paging: {
    limit: number
    maxLimit: number
    next: string
    offset: number
    previous: string
    total: number
  }
  stats: {
    durationMs: number
  }
}

export interface RawAPIResponse {
  success: boolean;
  message: string;
  timestamp: string;
  data: RawStorefrontResponse;
}

// Processed types for our application
export interface ProcessedStoreItem {
  id: string;
  name:string;
  displayName: string;
  description: string | null;
  cost: number;
  currency: string;
  imagePath: string | null;
  videoPath: string | null;
  type: string;
  subType?: 'skin' | 'chroma' | 'icon' | 'emote' | 'nexus_finisher' | 'bundle' | 'ward';
  endTime?: string;
  rotationCategory?: 'FEATURED' | 'BIWEEKLY' | 'WEEKLY' | 'DAILY';
  shadowImagePath?: string | null; // For ward skins
  chromaData?: {
    name: string;
    colors: string[];
  };
}

export interface MythicStoreSection {
  endDate: string;
  id: string;
  name: string;
  highlighted: boolean;
  spotlight: boolean;
}

export interface ProcessedMythicStorefront {
  name: string;
  background: string;
  items: ProcessedStoreItem[];
  featured: MythicStoreSection;
  biweekly: MythicStoreSection;
  weekly: MythicStoreSection;
  daily: MythicStoreSection;
}

export interface ProcessedStorefrontData {
    mythicshop: ProcessedMythicStorefront;
}

export interface ProcessedAPIResponse {
  success: boolean;
  data: {
    storefront: ProcessedStorefrontData;
  };
  timestamp: string;
} 