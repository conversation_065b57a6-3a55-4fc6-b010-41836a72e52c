"use client"

import * as React from "react"
import * as TooltipPrimitive from "@radix-ui/react-tooltip"
import { useMobileTooltip } from "@/hooks/use-mobile-tooltip"

import { cn } from "@/lib/utils"

function TooltipProvider({
  delayDuration = 0,
  ...props
}: React.ComponentProps<typeof TooltipPrimitive.Provider>) {
  return (
    <TooltipPrimitive.Provider
      data-slot="tooltip-provider"
      delayDuration={delayDuration}
      {...props}
    />
  )
}

function Tooltip({
  ...props
}: React.ComponentProps<typeof TooltipPrimitive.Root>) {
  return (
    <TooltipProvider>
      <TooltipPrimitive.Root data-slot="tooltip" {...props} />
    </TooltipProvider>
  )
}

function TooltipTrigger({
  ...props
}: React.ComponentProps<typeof TooltipPrimitive.Trigger>) {
  return <TooltipPrimitive.Trigger data-slot="tooltip-trigger" {...props} />
}

function TooltipContent({
  className,
  sideOffset = 0,
  children,
  ...props
}: React.ComponentProps<typeof TooltipPrimitive.Content>) {
  return (
    <TooltipPrimitive.Portal>
      <TooltipPrimitive.Content
        data-slot="tooltip-content"
        sideOffset={sideOffset}
        className={cn(
          "bg-slate-800/95 text-slate-100 border border-slate-700/50 backdrop-blur-sm animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance shadow-lg",
          className
        )}
        {...props}
      >
        {children}
        <TooltipPrimitive.Arrow className="bg-slate-800/95 fill-slate-800/95 z-50 size-2.5 translate-y-[calc(50%+2px)] rotate-45 rounded-[2px]" />
      </TooltipPrimitive.Content>
    </TooltipPrimitive.Portal>
  )
}

// Mobile-enhanced tooltip that supports click on mobile and hover on desktop
interface MobileTooltipProps {
  children: React.ReactNode
  content: React.ReactNode
  disabled?: boolean
  side?: "top" | "right" | "bottom" | "left"
  sideOffset?: number
  className?: string
  contentClassName?: string
}

function MobileTooltip({
  children,
  content,
  disabled = false,
  side = "top",
  sideOffset = 4,
  className,
  contentClassName
}: MobileTooltipProps) {
  const {
    isOpen,
    isMobile,
    handleClick,
    handleMouseEnter,
    handleMouseLeave
  } = useMobileTooltip({ disabled })

  return (
    <TooltipProvider>
      <TooltipPrimitive.Root open={isOpen}>
        <TooltipPrimitive.Trigger asChild>
          <div
            className={cn(
              "inline-block",
              isMobile && "cursor-pointer touch-manipulation",
              className
            )}
            onClick={handleClick}
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
            style={{
              WebkitTapHighlightColor: 'transparent',
              touchAction: 'manipulation'
            }}
          >
            {children}
          </div>
        </TooltipPrimitive.Trigger>
        <TooltipPrimitive.Portal>
          <TooltipPrimitive.Content
            side={side}
            sideOffset={sideOffset}
            className={cn(
              "bg-slate-800/95 text-slate-100 border border-slate-700/50 backdrop-blur-sm animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance shadow-lg",
              contentClassName
            )}
            onPointerDownOutside={(e) => {
              // Prevent closing when clicking on the tooltip content itself on mobile
              if (isMobile) {
                e.preventDefault()
              }
            }}
          >
            {content}
            <TooltipPrimitive.Arrow className="bg-slate-800/95 fill-slate-800/95 z-50 size-2.5 translate-y-[calc(50%+2px)] rotate-45 rounded-[2px]" />
          </TooltipPrimitive.Content>
        </TooltipPrimitive.Portal>
      </TooltipPrimitive.Root>
    </TooltipProvider>
  )
}

export { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider, MobileTooltip }
