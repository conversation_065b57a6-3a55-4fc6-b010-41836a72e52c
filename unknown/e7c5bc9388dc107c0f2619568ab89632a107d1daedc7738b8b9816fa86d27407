import React from 'react'

interface FilterIconProps {
  className?: string
  size?: number
}

export function FilterIcon({ className = "", size = 16 }: FilterIconProps) {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      {/* Top horizontal line (longest) */}
      <rect
        x="2"
        y="4"
        width="20"
        height="2"
        rx="1"
        fill="currentColor"
      />
      
      {/* Middle horizontal line (medium) */}
      <rect
        x="5"
        y="11"
        width="14"
        height="2"
        rx="1"
        fill="currentColor"
      />
      
      {/* Bottom horizontal line (shortest) */}
      <rect
        x="8"
        y="18"
        width="8"
        height="2"
        rx="1"
        fill="currentColor"
      />
    </svg>
  )
}
