import { useState, useEffect, useCallback } from 'react'
import { ProcessedSkinData } from '@/lib/services/skin-service'

interface UseSkinsReturn {
  skins: ProcessedSkinData[]
  loading: boolean
  error: string | null
  refetch: () => void
  totalCount: number
}

export function useSkins(): UseSkinsReturn {
  const [skins, setSkins] = useState<ProcessedSkinData[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [totalCount, setTotalCount] = useState(0)

  const fetchSkins = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/skins/all')

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: Failed to fetch skins`)
      }

      const result = await response.json()

      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch skins')
      }

      setSkins(result.data)
      setTotalCount(result.data.length)

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
      setError(errorMessage)
      console.error('❌ Failed to fetch skins:', errorMessage)
    } finally {
      setLoading(false)
    }
  }, [])

  useEffect(() => {
    fetchSkins()
  }, [fetchSkins])

  return {
    skins,
    loading,
    error,
    refetch: fetchSkins,
    totalCount
  }
}
