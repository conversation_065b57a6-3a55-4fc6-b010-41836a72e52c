import { Card, CardContent } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"

export function ChampionStatsSkeleton() {
  return (
    <div className="mb-8">
      <div className="flex items-center mb-6">
        <Skeleton className="h-6 w-6 mr-2" />
        <Skeleton className="h-8 w-48" />
      </div>
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        {Array.from({ length: 5 }).map((_, index) => (
          <Card key={index} className="bg-gray-900/40 border-gray-700/20">
            <CardContent className="p-4 text-center">
              <Skeleton className="h-8 w-8 mx-auto mb-2" />
              <Skeleton className="h-4 w-12 mx-auto" />
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}

export function ChampionGridSkeleton({ count = 6 }: { count?: number }) {
  return (
    <div className="champion-grid">
      {Array.from({ length: count }).map((_, index) => (
        <Card key={index} className="bg-gray-900/40 border-orange-700/20 w-full min-w-[140px] sm:min-w-[160px] max-w-[160px] sm:max-w-[280px] aspect-[2/3] flex flex-col">
          <CardContent className="p-0 flex flex-col h-full relative overflow-hidden rounded-lg">
            {/* Champion Image Skeleton - Full Card Background */}
            <div className="absolute inset-0 w-full h-full">
              <Skeleton className="w-full h-full" />
            </div>

            {/* Top Shadow Overlay */}
            <div className="absolute inset-x-0 top-0 h-20 bg-gradient-to-b from-black/40 via-black/20 to-transparent pointer-events-none"></div>

            {/* Role and Class Icons Overlay Skeleton */}
            <div className="absolute top-2 right-2 sm:top-3 sm:right-3 flex flex-col space-y-1 sm:space-y-2 z-10">
              <div className="bg-black/60 backdrop-blur-sm rounded-full p-1.5 sm:p-2">
                <Skeleton className="w-4 h-4 sm:w-5 sm:h-5 rounded-full" />
              </div>
              <div className="bg-black/60 backdrop-blur-sm rounded-full p-1.5 sm:p-2">
                <Skeleton className="w-4 h-4 sm:w-5 sm:h-5 rounded-full" />
              </div>
            </div>

            {/* Champion Name Skeleton - Bottom */}
            <div className="absolute bottom-0 left-0 right-0 p-3 sm:p-4 md:p-5 z-10 text-center">
              <Skeleton className="h-4 sm:h-5 md:h-6 w-20 sm:w-24 md:w-28 mx-auto" />
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

export function ChampionSectionSkeleton({ title, count = 6 }: { title: string; count?: number }) {
  return (
    <div className="mb-8">
      <div className="flex items-center mb-6">
        <Skeleton className="h-6 w-6 mr-2" />
        <Skeleton className="h-8 w-48" />
      </div>
      <ChampionGridSkeleton count={count} />
    </div>
  )
}

export function RoleGridSkeleton() {
  return (
    <div className="mb-8">
      <Skeleton className="h-8 w-40 mb-6" />
      <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-3 sm:gap-4 lg:gap-6">
        {Array.from({ length: 5 }).map((_, index) => (
          <Card key={index} className="bg-gray-900/40 border-gray-700/20 h-24 sm:h-28 lg:h-32 w-full">
            <CardContent className="p-3 sm:p-4 lg:p-6 text-center h-full flex flex-col justify-center items-center">
              <Skeleton className="w-6 h-6 sm:w-8 sm:h-8 lg:w-10 lg:h-10 mx-auto mb-1 sm:mb-2 lg:mb-3" />
              <Skeleton className="h-4 sm:h-5 lg:h-6 w-12 sm:w-16 lg:w-20 mx-auto" />
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}

export function ChampionPageSkeleton() {
  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-2">
      {/* Header Skeleton */}
      <div className="mb-8">
        <div className="flex items-center space-x-3 mb-4">
          <Skeleton className="h-8 w-8" />
          <Skeleton className="h-10 w-32" />
        </div>
        <Skeleton className="h-6 w-96" />
      </div>

      {/* Quick Navigation Skeleton */}
      <div className="mb-8 grid grid-cols-1 md:grid-cols-3 gap-6">
        {Array.from({ length: 3 }).map((_, index) => (
          <Card key={index} className="bg-gray-900/40 border-orange-700/20">
            <CardContent className="p-6 text-center">
              <Skeleton className="h-12 w-12 mx-auto mb-4" />
              <Skeleton className="h-6 w-32 mx-auto mb-2" />
              <Skeleton className="h-4 w-40 mx-auto" />
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Featured Champions Skeleton */}
      <ChampionSectionSkeleton title="Featured Champions" count={6} />

      {/* Role Grid Skeleton */}
      <RoleGridSkeleton />
    </div>
  )
}
