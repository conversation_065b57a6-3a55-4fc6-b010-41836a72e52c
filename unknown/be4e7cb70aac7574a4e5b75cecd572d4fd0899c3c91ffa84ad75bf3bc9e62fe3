"use client"

import { MobileTooltip } from "@/components/ui/tooltip"
import Image from "next/image"

export default function TooltipTestPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-black p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold text-white mb-8 text-center">
          Mobile-Friendly Tooltips Test
        </h1>
        
        <div className="bg-black/50 backdrop-blur-md rounded-lg p-8 border border-gray-700/50">
          <h2 className="text-2xl font-semibold text-white mb-6">
            Try these tooltips on both desktop and mobile:
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            
            {/* Blue Essence Tooltip */}
            <MobileTooltip
              content={
                <div className="text-center">
                  <div className="font-semibold text-cyan-300">Blue Essence</div>
                  <div className="text-sm text-gray-300 mt-1">
                    Primary in-game currency earned from battle pass
                  </div>
                </div>
              }
            >
              <div className="flex items-center space-x-2 bg-black/70 backdrop-blur-md rounded-lg px-4 py-3 border border-gray-700/50 shadow-lg cursor-help hover:border-cyan-500/50 transition-colors">
                <Image
                  src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-navigation/global/default/rewardicons_blueessence.png"
                  alt="Blue Essence"
                  width={24}
                  height={24}
                  className="w-6 h-6"
                />
                <span className="text-cyan-400 font-medium">6,300</span>
              </div>
            </MobileTooltip>

            {/* RP Tooltip */}
            <MobileTooltip
              content={
                <div className="text-center">
                  <div className="font-semibold text-yellow-300">Riot Points (RP)</div>
                  <div className="text-sm text-gray-300 mt-1">
                    Premium currency for instant unlocks
                  </div>
                </div>
              }
            >
              <div className="flex items-center space-x-2 bg-black/70 backdrop-blur-md rounded-lg px-4 py-3 border border-gray-700/50 shadow-lg cursor-help hover:border-yellow-500/50 transition-colors">
                <Image
                  src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg"
                  alt="Riot Points"
                  width={24}
                  height={24}
                  className="w-6 h-6"
                />
                <span className="text-yellow-400 font-medium">880</span>
              </div>
            </MobileTooltip>

            {/* Skins Tooltip */}
            <MobileTooltip
              content={
                <div className="text-center">
                  <div className="font-semibold" style={{ color: '#dfcda8' }}>Skins Available</div>
                  <div className="text-sm text-gray-300 mt-1">
                    Total number of cosmetic skins for this champion
                  </div>
                </div>
              }
            >
              <div className="flex items-center space-x-2 bg-black/70 backdrop-blur-md rounded-lg px-4 py-3 border border-gray-700/50 shadow-lg cursor-help hover:border-orange-500/50 transition-colors">
                <Image
                  src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/skins_rewards.svg"
                  alt="Skins"
                  width={24}
                  height={24}
                  className="w-6 h-6"
                />
                <span style={{ color: '#dfcda8' }} className="font-medium">12</span>
              </div>
            </MobileTooltip>

            {/* Rarity Tooltip */}
            <MobileTooltip
              content={
                <div className="text-center">
                  <div className="font-semibold text-orange-300">Epic Rarity</div>
                  <div className="text-sm text-gray-300 mt-1">
                    High-quality skin with enhanced visual effects
                  </div>
                </div>
              }
            >
              <div className="w-16 h-16 rounded-full bg-black/80 backdrop-blur-sm flex items-center justify-center border border-gray-600/50 cursor-help hover:border-orange-500/50 transition-colors mx-auto">
                <Image
                  src="https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/cn-gem-3.png"
                  alt="Epic"
                  width={32}
                  height={32}
                  className="w-8 h-8"
                />
              </div>
            </MobileTooltip>

            {/* Text-only Tooltip */}
            <MobileTooltip
              content={
                <div className="text-center">
                  <div className="font-semibold text-green-300">Success!</div>
                  <div className="text-sm text-gray-300 mt-1">
                    This tooltip works on both mobile and desktop
                  </div>
                </div>
              }
            >
              <div className="bg-green-600/20 border border-green-500/50 rounded-lg px-4 py-3 text-center cursor-help hover:bg-green-600/30 transition-colors">
                <span className="text-green-300 font-medium">✓ Click or Hover Me</span>
              </div>
            </MobileTooltip>

            {/* Different Position Tooltip */}
            <MobileTooltip
              side="bottom"
              content={
                <div className="text-center">
                  <div className="font-semibold text-purple-300">Bottom Tooltip</div>
                  <div className="text-sm text-gray-300 mt-1">
                    This tooltip appears below the trigger
                  </div>
                </div>
              }
            >
              <div className="bg-purple-600/20 border border-purple-500/50 rounded-lg px-4 py-3 text-center cursor-help hover:bg-purple-600/30 transition-colors">
                <span className="text-purple-300 font-medium">↓ Bottom Tooltip</span>
              </div>
            </MobileTooltip>

          </div>

          <div className="mt-8 p-4 bg-blue-900/30 rounded-lg border border-blue-500/30">
            <h3 className="text-lg font-semibold text-blue-300 mb-2">How to test:</h3>
            <ul className="text-gray-300 space-y-1 text-sm">
              <li><strong>Desktop:</strong> Hover over any element to see the tooltip</li>
              <li><strong>Mobile:</strong> Tap any element to show the tooltip, tap elsewhere to hide it</li>
              <li><strong>Behavior:</strong> Tooltips automatically detect your device type</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
