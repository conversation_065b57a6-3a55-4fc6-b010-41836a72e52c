"use client"

import { useState } from "react"
import ChampionSkinViewerClient from "./champion-skin-viewer-client"
import ChampionSkinCarouselClient from "./champion-skin-carousel-client"

interface ChromaData {
  id: string
  name: string
  color: string
  colors?: string[]
  chromaPath?: string
  skinId?: number
  isDefault?: boolean
}

interface SkinData {
  name: string
  price: string
  rarity: string
  rarityTier?: string
  isLegacy?: boolean
  image: string
  splashArt: string
  inGameImage: string
  isBase: boolean
  hasInGameImage: boolean
  chromas: ChromaData[]
  skinNum: number
  communityDragonId?: number
}

interface ChampionInteractiveClientProps {
  skins: SkinData[]
  championName: string
  championKey: string
}

export default function ChampionInteractiveClient({ 
  skins, 
  championName, 
  championKey 
}: ChampionInteractiveClientProps) {
  const [currentSkinIndex, setCurrentSkinIndex] = useState(0)

  const handleSkinSelect = (index: number) => {
    setCurrentSkinIndex(index)
  }

  return (
    <div className="relative">
      {/* Skin Display */}
      <ChampionSkinViewerClient
        skins={skins}
        championName={championName}
        championKey={championKey}
        currentSkinIndex={currentSkinIndex}
        onSkinChange={setCurrentSkinIndex}
      />

      {/* Skin Carousel */}
      <ChampionSkinCarouselClient
        skins={skins}
        currentSkinIndex={currentSkinIndex}
        onSkinSelect={handleSkinSelect}
      />
    </div>
  )
}
