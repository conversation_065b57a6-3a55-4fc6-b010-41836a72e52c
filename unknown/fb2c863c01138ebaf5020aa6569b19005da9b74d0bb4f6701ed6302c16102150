# LoLDB API Integration Documentation

## Overview

This document describes the comprehensive League of Legends API integration implemented in LoLDB, featuring heavy caching mechanisms and optimized data fetching.

## Architecture

### Multi-Layer Caching System

The application implements a sophisticated caching strategy with multiple layers:

1. **Memory Cache** - Fastest access for frequently used data
2. **File System Cache** - Persistent cache that survives server restarts
3. **Next.js App Router Caching** - Built-in caching for API routes
4. **CDN Caching** - Browser and CDN-level caching with proper headers

### API Client Structure

```
lib/
├── api/
│   ├── league-client.ts      # Main API client with retry logic
│   └── data-transformers.ts  # Data transformation utilities
├── cache/
│   └── cache-manager.ts      # Multi-layer cache implementation
└── types/
    └── league-api.ts         # TypeScript interfaces
```

## API Endpoints

### Data Dragon API (No API Key Required)

- **Champions**: `/cdn/{version}/data/{locale}/champion.json`
- **Champion Details**: `/cdn/{version}/data/{locale}/champion/{championId}.json`
- **Items**: `/cdn/{version}/data/{locale}/item.json`
- **Game Versions**: `/api/versions.json`

### Riot Games API (Requires API Key)

- **Free Champion Rotation**: `/lol/platform/v3/champion-rotations`

### Asset URLs

- **Champion Splash**: `/cdn/img/champion/splash/{championId}_{skinNum}.jpg`
- **Champion Loading**: `/cdn/img/champion/loading/{championId}_{skinNum}.jpg`
- **Champion Square**: `/cdn/{version}/img/champion/{championId}.png`
- **Champion Abilities**: `/cdn/{version}/img/spell/{spellImage}`
- **Items**: `/cdn/{version}/img/item/{itemImage}`

## Setup Instructions

### 1. Environment Variables

Create a `.env.local` file in the project root:

```env
# Optional: Riot Games API Key for live data
RIOT_API_KEY=your_riot_api_key_here

# Optional: API Configuration
LEAGUE_API_REGION=na1
LEAGUE_API_LOCALE=en_US

# Optional: Cache TTL Configuration (in milliseconds)
CACHE_TTL_CHAMPIONS=********    # 24 hours
CACHE_TTL_ITEMS=********        # 24 hours
CACHE_TTL_VERSIONS=3600000      # 1 hour
CACHE_TTL_FREE_ROTATION=3600000 # 1 hour
```

### 2. Getting a Riot API Key

1. Visit [Riot Developer Portal](https://developer.riotgames.com/)
2. Sign in with your Riot account
3. Create a new application
4. Copy your API key to the `.env.local` file

**Note**: The application works without an API key, but some features (like live free champion rotation) will use mock data.

## API Routes

### Server-Side API Routes

- `GET /api/champions` - Get all champions with caching
- `GET /api/champions/[championId]` - Get detailed champion information
- `GET /api/items` - Get all items with filtering and caching
- `GET /api/free-rotation` - Get current free champion rotation
- `GET /api/game-version` - Get current and previous game versions

### Query Parameters

- `?refresh=true` - Force refresh cache
- `?category=Support` - Filter items by category
- `?search=jinx` - Search items by name

## React Hooks

### Champion Hooks

```typescript
import { useChampions, useChampionDetail, useFreeRotation } from '@/hooks/use-champions'

// Get all champions
const { champions, loading, error, refresh } = useChampions()

// Get specific champion
const { champion, loading, error } = useChampionDetail('Jinx')

// Get free rotation
const { freeChampions, newPlayerChampions, loading, error } = useFreeRotation()
```

### Item Hooks

```typescript
import { useItems, useItemFilters } from '@/hooks/use-items'

// Get all items
const { items, categories, loading, error } = useItems()

// Filter items
const { filteredItems, filters, updateFilters } = useItemFilters(items)
```

## Caching Strategy

### Cache TTL (Time To Live)

- **Champions**: 24 hours (static data, changes rarely)
- **Items**: 24 hours (static data, changes rarely)
- **Game Versions**: 1 hour (changes with patches)
- **Free Rotation**: 1 hour (changes weekly)

### Cache Keys

```typescript
const CACHE_KEYS = {
  CHAMPIONS: 'champions',
  CHAMPION_DETAIL: 'champion_detail',
  ITEMS: 'items',
  VERSIONS: 'versions',
  FREE_ROTATION: 'free_rotation'
}
```

### Cache Management

```typescript
import CacheManager from '@/lib/cache/cache-manager'

// Get cached data
const data = await CacheManager.get('champions')

// Set cached data
await CacheManager.set('champions', data, ********) // 24 hours

// Clear all cache
await CacheManager.clear()

// Get cache statistics
const stats = CacheManager.getStats()
```

## Error Handling

### Retry Logic

The API client implements exponential backoff retry logic:

- **Max Retries**: 3 attempts
- **Backoff**: 2^attempt * 1000ms (1s, 2s, 4s)
- **Error Types**: Network errors, HTTP 5xx errors

### Graceful Degradation

- Falls back to cached data when API is unavailable
- Shows mock data when no cache is available
- Displays user-friendly error messages

## Performance Optimizations

### 1. Efficient Data Fetching

- Parallel requests for independent data
- Conditional requests based on cache status
- Minimal data transformation on the client

### 2. Image Optimization

- Next.js Image component for automatic optimization
- Lazy loading for off-screen images
- Proper fallback handling

### 3. Bundle Optimization

- Tree-shaking for unused API methods
- Code splitting for large data transformations
- Minimal runtime dependencies

## Data Transformation

### Champion Data

```typescript
// Raw API response → UI-friendly format
const transformedChampion = transformChampionForList(rawChampion)
// Result: { id, name, role, difficulty, image, slug, tags }
```

### Item Data

```typescript
// Raw API response → UI-friendly format
const transformedItem = transformItem(rawItem, itemId)
// Result: { id, name, category, formattedPrice, image, ... }
```

## Monitoring and Debugging

### Cache Statistics

```typescript
// Get cache performance metrics
const stats = CacheManager.getStats()


```

### API Client Debugging

```typescript
// Enable debug logging
const client = new LeagueApiClient({ debug: true })

// Get cache statistics
const cacheStats = client.getCacheStats()
```

## Best Practices

### 1. Cache Invalidation

- Use versioned cache keys for data that changes with patches
- Implement cache warming for critical data
- Monitor cache hit rates

### 2. Error Handling

- Always provide fallback data
- Log errors for monitoring
- Show user-friendly error messages

### 3. Performance

- Use React.memo for expensive components
- Implement proper loading states
- Debounce search inputs

## Troubleshooting

### Common Issues

1. **API Key Issues**
   - Verify key is valid and not expired
   - Check rate limits (100 requests per 2 minutes for development keys)
   - Ensure correct region configuration

2. **Cache Issues**
   - Clear cache directory: `rm -rf .cache`
   - Check file permissions
   - Monitor disk space

3. **Network Issues**
   - Check internet connectivity
   - Verify API endpoints are accessible
   - Monitor for API downtime

### Debug Commands

```bash
# Clear all cache
rm -rf .cache

# Check API connectivity
curl "https://ddragon.leagueoflegends.com/api/versions.json"

# Monitor cache directory
ls -la .cache/
```

## Future Enhancements

1. **Redis Integration** - For distributed caching
2. **GraphQL Layer** - For more efficient data fetching
3. **Real-time Updates** - WebSocket integration for live data
4. **Advanced Analytics** - Cache performance monitoring
5. **CDN Integration** - Asset optimization and delivery
