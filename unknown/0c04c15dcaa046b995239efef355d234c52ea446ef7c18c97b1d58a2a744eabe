import AllChampionsClient from "@/app/champions/client";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { leagueApi } from "@/lib/api/league-client";
import { calculateChampionStats, getStoreChampions } from "@/lib/services/champion-service";
import { AlertCircle } from "lucide-react";
import { generateSEOMetadata } from "@/components/SEO";
import type { Metadata } from 'next';

export const revalidate = 3600; // Revalidate every hour

export async function generateMetadata(): Promise<Metadata> {
  return generateSEOMetadata({
    title: 'All League of Legends Champions',
    description: 'Browse all League of Legends champions. View champion stats, abilities, roles, classes, and more. Find your perfect champion on LoLDB.',
    keywords: [
      'League of Legends champions',
      'LoL champions',
      'champion list',
      'champion stats',
      'champion abilities',
      'champion roles',
      'champion difficulty',
      'champion database'
    ],
    url: '/champions',
    canonical: 'https://loldb.info/champions',
    type: 'website',
    section: 'Champions'
  });
}

export default async function AllChampionsPage() {
  try {
    // Get comprehensive champion data from store (includes store data, images, positions, etc.)
    const storeChampions = await getStoreChampions();
    
    // Get detailed champion data from Riot API for additional enrichment
    const riotChampions = await leagueApi.getChampions();

    if (!storeChampions || storeChampions.length === 0) {
      throw new Error("No champions returned from service");
    }

    // Create a map of Riot champions for efficient lookup by championId (key)
    const riotChampionsMap = new Map(
      Object.values(riotChampions).map(champion => [champion.key, champion])
    );

    // Intelligently merge store champions with Riot API data
    const enhancedChampions = storeChampions.map(storeChampion => {
      const riotChampion = riotChampionsMap.get(storeChampion.championId.toString());
      
      if (!riotChampion) {
        // If no match found in Riot API, return store champion as-is
        console.warn(`No Riot API data found for champion ID: ${storeChampion.championId} (${storeChampion.name})`);
        return storeChampion;
      }

      // Carefully merge data, preserving critical store fields while enhancing with Riot data
      const enhanced = {
        // Start with store champion data (has store prices, positions, images, etc.)
        ...storeChampion,
        
        // Override with specific Riot API data we want to preserve
        id: riotChampion.id, // Use Riot's champion ID for consistency
        title: riotChampion.title || storeChampion.title, // Riot's title is usually more accurate
        difficulty: riotChampion.info?.difficulty || storeChampion.difficulty || 5,
        
        // Enhance tags by combining both sources (store tags + riot tags)
        tags: [
          ...new Set([
            ...storeChampion.tags,
            ...(riotChampion.tags || [])
          ])
        ].filter(tag => 
          tag !== "requirement_owned" && 
          !tag.includes("<<") && 
          !tag.includes(">>")
        ),
        
        // Add Riot API specific data as additional fields (don't override core fields)
        riotKey: riotChampion.key,
        riotSkins: riotChampion.skins || [],
        riotTags: riotChampion.tags || []
      };

      return enhanced;
    });

    const stats = calculateChampionStats(enhancedChampions);
    
    return (
      <AllChampionsClient
        initialChampions={enhancedChampions}
        initialStats={stats}
      />
    );
  } catch (error) {
    console.error("Failed to fetch initial champions data:", error);
    return (
      <div className="container mx-auto flex h-screen items-center justify-center px-4 py-8">
        <Alert variant="destructive" className="max-w-lg">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error Loading Champions</AlertTitle>
          <AlertDescription>
            There was a problem loading the champion data. This might be a temporary issue. Please try again later.
          </AlertDescription>
        </Alert>
      </div>
    );
  }
}
