"use client"

import { useState, useEffect, useMemo, useRef, useCallback } from 'react'
import UniversalChampionCard from "@/components/champion/universal-champion-card"
import { UniversalChampionData } from "@/lib/utils/champion-data-utils"

interface LazyChampionGridProps {
  champions: UniversalChampionData[]
  isClient: boolean
  onRoleClick?: (role: string) => void
  onClassClick?: (className: string) => void
  borderColor?: string
  hoverColor?: string
  variant?: 'default' | 'loading-image'
}

const INITIAL_LOAD = 24 // Load 24 champions initially (4 rows of 6 on desktop)
const LOAD_INCREMENT = 18 // Load 18 more champions each time (3 rows of 6)

export default function LazyChampionGrid({
  champions,
  isClient,
  onRoleClick,
  onClassClick,
  borderColor = "orange-700/20",
  hoverColor = "orange-400/50",
  variant = "loading-image"
}: LazyChampionGridProps) {
  const [displayCount, setDisplayCount] = useState(INITIAL_LOAD)
  const [isLoading, setIsLoading] = useState(false)
  const loadingTriggerRef = useRef<HTMLDivElement>(null)
  const observerRef = useRef<IntersectionObserver | null>(null)
  const isLoadingRef = useRef(false)

  // Get champions to display
  const displayedChampions = useMemo(() => {
    return champions.slice(0, displayCount)
  }, [champions, displayCount])

  const hasMore = displayCount < champions.length

  // Load more champions with race condition protection
  const loadMore = useCallback(() => {
    // Use ref to prevent race conditions
    if (isLoadingRef.current || displayCount >= champions.length) {
      return
    }

    isLoadingRef.current = true
    setIsLoading(true)

    // Use requestAnimationFrame for better timing
    requestAnimationFrame(() => {
      setTimeout(() => {
        setDisplayCount(prev => {
          const newCount = Math.min(prev + LOAD_INCREMENT, champions.length)
          return newCount
        })
        setIsLoading(false)
        isLoadingRef.current = false
      }, 150) // Slightly longer delay for stability
    })
  }, [displayCount, champions.length])

  // Intersection Observer for lazy loading with improved stability
  useEffect(() => {
    // Clean up existing observer
    if (observerRef.current) {
      observerRef.current.disconnect()
      observerRef.current = null
    }

    // Don't create observer if no more items or not client-side
    if (!hasMore || !isClient) {
      return
    }

    // Create new observer with debouncing
    let timeoutId: NodeJS.Timeout | null = null

    observerRef.current = new IntersectionObserver(
      (entries) => {
        const [entry] = entries

        if (entry.isIntersecting && !isLoadingRef.current) {
          // Debounce the loading to prevent multiple rapid calls
          if (timeoutId) {
            clearTimeout(timeoutId)
          }

          timeoutId = setTimeout(() => {
            loadMore()
          }, 100)
        }
      },
      {
        rootMargin: '300px', // Increased margin for better UX
        threshold: 0.1
      }
    )

    // Observe the trigger element
    const currentTrigger = loadingTriggerRef.current
    if (currentTrigger && observerRef.current) {
      observerRef.current.observe(currentTrigger)
    }

    // Cleanup function
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId)
      }
      if (observerRef.current) {
        observerRef.current.disconnect()
        observerRef.current = null
      }
    }
  }, [hasMore, isClient, loadMore])

  // Reset display count when champions change (due to filtering)
  useEffect(() => {
    setDisplayCount(INITIAL_LOAD)
    setIsLoading(false)
    isLoadingRef.current = false
  }, [champions])

  // Sync loading state with ref
  useEffect(() => {
    isLoadingRef.current = isLoading
  }, [isLoading])

  // Fallback scroll listener for additional reliability
  useEffect(() => {
    if (!hasMore || !isClient) return

    const handleScroll = () => {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop
      const windowHeight = window.innerHeight
      const documentHeight = document.documentElement.scrollHeight

      // If user is within 400px of bottom and not loading
      if (scrollTop + windowHeight >= documentHeight - 400 && !isLoadingRef.current) {
        loadMore()
      }
    }

    // Throttle scroll events
    let ticking = false
    const throttledScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          handleScroll()
          ticking = false
        })
        ticking = true
      }
    }

    window.addEventListener('scroll', throttledScroll, { passive: true })

    return () => {
      window.removeEventListener('scroll', throttledScroll)
    }
  }, [hasMore, isClient, loadMore])

  if (!isClient) {
    return null
  }

  return (
    <>
      {/* Champions Grid */}
      <div className={isClient ? 'champion-grid' : 'grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 2xl:grid-cols-6 gap-3 sm:gap-6 justify-items-start'}>
        {displayedChampions.map((champion, index) => (
          <UniversalChampionCard
            key={`${champion.id}-${index}`}
            champion={champion}
            variant={variant}
            borderColor={borderColor}
            hoverColor={hoverColor}
            onRoleClick={onRoleClick}
            onClassClick={onClassClick}
          />
        ))}
      </div>

      {/* Intersection Observer Trigger */}
      {hasMore && (
        <div
          ref={loadingTriggerRef}
          className="w-full h-20 flex items-center justify-center mt-8"
        >
          {isLoading && (
            <div className="inline-flex items-center space-x-2">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-orange-400"></div>
              <span className="text-gray-300">Loading more champions...</span>
            </div>
          )}
        </div>
      )}

      {/* End message */}
      {!hasMore && displayedChampions.length > INITIAL_LOAD && (
        <div className="text-center py-8">
          <p className="text-gray-400">
            You've reached the end! Showing all {displayedChampions.length} champions.
          </p>
        </div>
      )}
    </>
  )
}
