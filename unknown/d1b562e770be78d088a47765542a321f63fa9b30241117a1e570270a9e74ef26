import SharedLayout from "@/components/shared-layout"
import { Skeleton } from "@/components/ui/skeleton"
import { Card, CardContent } from "@/components/ui/card"

export function ChromasPageSkeleton() {
  return (
    <SharedLayout>
      <div className="container mx-auto px-4 sm:px-6 md:px-8 py-8 max-w-full overflow-hidden">
        {/* Header Skeleton */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <Skeleton className="h-8 w-8 rounded" />
              <Skeleton className="h-10 w-48" />
            </div>
            {/* Mobile Search Button - Next to Title */}
            <div className="lg:hidden">
              <Skeleton className="h-8 w-8 rounded" />
            </div>
          </div>
          <Skeleton className="h-6 w-96" />
        </div>

        {/* Desktop Filters Skeleton - Hidden on mobile, shown on lg+ */}
        <div className="hidden lg:block mb-8 space-y-4">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search Bar */}
            <div className="relative lg:flex-1 lg:max-w-md">
              <Skeleton className="h-10 w-full" />
            </div>
            {/* Champion Filter */}
            <Skeleton className="h-10 w-full lg:w-48" />
            {/* Chroma Rarities Filter */}
            <Skeleton className="h-10 w-full lg:w-48" />
            {/* Chroma Colors Filter */}
            <Skeleton className="h-10 w-full lg:w-48" />
            {/* Reset Filters Button */}
            <Skeleton className="h-10 w-full lg:w-32" />
          </div>
        </div>

        {/* Results Count Skeleton */}
        <div className="mb-8">
          <Skeleton className="h-5 w-64" />
        </div>

        {/* Grid Skeleton - Match mythic shop layout */}
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
          {Array.from({ length: 30 }).map((_, i) => (
            <div
              key={i}
              className="bg-gradient-to-br from-gray-900/60 to-gray-800/40 border border-purple-700/30 rounded-xl overflow-hidden relative h-48"
            >
              {/* Centered image skeleton like mythic shop */}
              <div className="h-48 flex items-center justify-center p-6">
                <Skeleton className="w-[170px] h-[170px]" />
              </div>

              {/* Color indicators skeleton - Top Left */}
              <div className="absolute top-3 left-3 flex space-x-1">
                <Skeleton className="w-4 h-4 rounded-full" />
                <Skeleton className="w-4 h-4 rounded-full" />
                <Skeleton className="w-4 h-4 rounded-full" />
              </div>

              {/* Content overlay skeleton */}
              <div className="p-4 bg-gradient-to-t from-black/90 via-black/70 to-transparent">
                <Skeleton className="w-3/4 h-4 mb-2" />
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-1">
                    <Skeleton className="w-4 h-4 rounded" />
                    <Skeleton className="w-8 h-3" />
                  </div>
                  <Skeleton className="w-4 h-4 rounded" />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </SharedLayout>
  )
}
