"use client"

import { Card, CardContent } from "@/components/ui/card"
import Image from "next/image"
import Link from "next/link"

import { ChampionWithStoreData } from "@/lib/types/league-api"
import {
  getClassIconUrl,
  getPositionIconUrl
} from "@/lib/utils/champion-data-utils"

export default function UniversalChampionCard({
  champion,
  variant = 'loading-image',
  borderColor = 'gray-700/20',
  hoverColor = 'blue-500/50',
  onRoleClick,
  onClassClick,
  className = ""
}: {
  champion: ChampionWithStoreData;
  variant?: string;
  borderColor?: string;
  hoverColor?: string;
  onRoleClick?: (role: string) => void;
  onClassClick?: (className: string) => void;
  className?: string;
}) {

  // Always use the champion.image from API (which now contains Community Dragon URLs)
  // The variant prop is kept for backward compatibility but we always use champion.image
  const imageSource = champion.image

  return (
    <Link prefetch href={`/champions/${champion.id}`}>
      <Card className={`bg-gray-900/40 border-${borderColor} hover:border-${hoverColor} hover:-translate-y-1 sm:hover:-translate-y-2 transition-all duration-300 group cursor-pointer w-full min-w-[140px] sm:min-w-[160px] max-w-[160px] sm:max-w-[280px] aspect-[2/3] flex flex-col ${className}`}>
        <CardContent className="p-0 flex flex-col h-full relative overflow-hidden rounded-lg">
          {/* Champion Image - Full Card Background */}
          <div className="absolute inset-0 w-full h-full">
            <Image
              src={imageSource}
              alt={champion.name}
              width={300}
              height={560}
              className="w-full h-full object-cover object-top scale-110 group-hover:scale-125 transition-transform duration-300"
              onError={(e) => {
                // Fallback to champion square image if loading image fails
                e.currentTarget.src = champion.image || "/placeholder.svg"
              }}
            />

          </div>

          {/* Top Shadow Overlay */}
          <div className="absolute inset-x-0 top-0 h-20 bg-gradient-to-b from-black/40 via-black/20 to-transparent pointer-events-none"></div>

          {/* Role and Class Icons Overlay */}
          <div className="absolute top-2 right-2 sm:top-3 sm:right-3 flex flex-col space-y-1 sm:space-y-2 z-10">
            {/* Position Role Icon */}
            <div
              className={`bg-black/60 backdrop-blur-sm rounded-full p-1.5 sm:p-2 ${onRoleClick ? 'cursor-pointer hover:bg-black/80 transition-colors' : ''}`}
              onClick={(e) => {
                if (onRoleClick) {
                  e.preventDefault()
                  e.stopPropagation()
                  const primaryRole = champion.recommendedPositions && champion.recommendedPositions.length > 0
                    ? champion.recommendedPositions[0]
                    : 'Unknown'
                  onRoleClick(primaryRole)
                }
              }}
            >
              <Image
                src={getPositionIconUrl(champion.recommendedPositions && champion.recommendedPositions.length > 0 ? champion.recommendedPositions[0] : 'Unknown')}
                alt={champion.recommendedPositions && champion.recommendedPositions.length > 0 ? champion.recommendedPositions[0] : 'Unknown'}
                width={20}
                height={20}
                className="w-4 h-4 sm:w-5 sm:h-5"
                style={{ userSelect: 'none', pointerEvents: onRoleClick ? 'auto' : 'none' }}
                draggable={false}
              />
            </div>

            {/* Champion Class Icon */}
            <div className="bg-black/60 backdrop-blur-sm rounded-full p-1.5 sm:p-2">
              <Image
                src={getClassIconUrl(champion.class)}
                alt={champion.class}
                width={20}
                height={20}
                className="w-4 h-4 sm:w-5 sm:h-5"
                style={{ userSelect: 'none', pointerEvents: 'none' }}
                draggable={false}
              />
            </div>
          </div>

          {/* Champion Info */}
          <div className="absolute bottom-0 left-0 right-0 p-3 sm:p-4 md:p-5 z-10 text-center">
            <h3 className="text-white font-semibold text-sm sm:text-base md:text-lg group-hover:text-orange-400 transition-colors" style={{ textShadow: '3px 3px 6px rgba(0,0,0,1), 1px 1px 3px rgba(0,0,0,0.8)' }}>
              {champion.name}
            </h3>
          </div>
        </CardContent>
      </Card>
    </Link>
  )
}
