"use client"

import Image from "next/image"

interface CurrencyIconProps {
  price: string
  size?: number
  className?: string
}

/**
 * Component that displays the appropriate currency icon (RP, Mythic Essence, Ancient Spark, or Blue Essence)
 * based on the price string
 */
export function CurrencyIcon({ price, size = 16, className = "" }: CurrencyIconProps) {
  // Determine currency type based on price string
  const isMythicEssence = price.includes('ME')
  const isAncientSpark = price.includes('AS')
  const isBlueEssence = price.includes('BE')

  // Extract the numeric value and remove currency suffix
  const numericPrice = price.replace(/ (RP|ME|AS|BE)$/, '')

  if (isMythicEssence) {
    return (
      <div className={`flex items-center space-x-1 ${className}`}>
        <Image
          src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/mythic.svg"
          alt="ME"
          width={size}
          height={size}
          style={{
            userSelect: 'none',
            pointerEvents: 'none',
            width: `${size}px`,
            height: `${size}px`
          }}
          draggable={false}
        />
        <span
          className="font-medium"
          style={{
            fontSize: `${size * 0.875}px`,
            color: '#b47bdf'
          }}
        >
          {numericPrice}
        </span>
      </div>
    )
  }

  if (isAncientSpark) {
    return (
      <div className={`flex items-center space-x-1 ${className}`}>
        <Image
          src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/currency/icons/ancient_spark.svg"
          alt="AS"
          width={size}
          height={size}
          style={{
            userSelect: 'none',
            pointerEvents: 'none',
            width: `${size}px`,
            height: `${size}px`
          }}
          draggable={false}
        />
        <span
          className="font-medium"
          style={{
            fontSize: `${size * 0.875}px`,
            color: '#2babf0'
          }}
        >
          {numericPrice}
        </span>
      </div>
    )
  }

  if (isBlueEssence) {
    return (
      <div className={`flex items-center space-x-1 ${className}`}>
        <Image
          src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-navigation/global/default/rewardicons_blueessence.png"
          alt="BE"
          width={size}
          height={size}
          style={{
            userSelect: 'none',
            pointerEvents: 'none',
            width: `${size}px`,
            height: `${size}px`
          }}
          draggable={false}
        />
        <span
          className="font-medium"
          style={{
            fontSize: `${size * 0.875}px`,
            color: '#38c4dc'
          }}
        >
          {numericPrice}
        </span>
      </div>
    )
  }

  return (
    <div className={`flex items-center space-x-1 ${className}`}>
      <Image
        src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg"
        alt="RP"
        width={size}
        height={size}
        style={{
          userSelect: 'none',
          pointerEvents: 'none',
          width: `${size}px`,
          height: `${size}px`
        }}
        draggable={false}
      />
      <span
        className="text-orange-400 font-medium"
        style={{ fontSize: `${size * 0.875}px` }}
      >
        {numericPrice}
      </span>
    </div>
  )
}
