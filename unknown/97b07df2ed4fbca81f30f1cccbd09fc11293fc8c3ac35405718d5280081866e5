import { useState, useMemo, useEffect, useCallback } from 'react'
import { searchWithSuggestions } from '@/lib/utils/search-utils'

export interface SearchConfig<T> {
  /** Array of items to search through */
  items: T[]
  /** Function to extract searchable text from each item */
  getSearchableText: (item: T) => string | string[]
  /** Function to extract available search terms for suggestions */
  getAvailableTerms: (items: T[]) => string[]
  /** Debounce delay in milliseconds (default: 300) */
  debounceMs?: number
  /** Maximum number of suggestions to show (default: 3) */
  maxSuggestions?: number
  /** Minimum similarity threshold for suggestions (default: 0.4) */
  minSimilarity?: number
}

export interface SearchWithSuggestionsReturn<T> {
  /** Current search query */
  searchQuery: string
  /** Debounced search query (used internally) */
  debouncedSearchQuery: string
  /** Filtered results based on search */
  searchResults: T[]
  /** "Did You Mean?" suggestions */
  searchSuggestions: string[]
  /** Whether the search returned any results */
  hasSearchResults: boolean
  /** Whether search is currently being debounced */
  isSearching: boolean
  /** Function to update the search query */
  setSearchQuery: (query: string) => void
  /** Function to clear the search */
  clearSearch: () => void
  /** Function to handle suggestion clicks */
  handleSuggestionClick: (suggestion: string) => void
}

/**
 * Reusable hook for search functionality with "Did You Mean?" suggestions
 * 
 * @param config - Configuration object for search behavior
 * @returns Search state and handlers
 */
export function useSearchWithSuggestions<T>(
  config: SearchConfig<T>
): SearchWithSuggestionsReturn<T> {
  const {
    items,
    getSearchableText,
    getAvailableTerms,
    debounceMs = 300,
    maxSuggestions = 3,
    minSimilarity = 0.4
  } = config

  const [searchQuery, setSearchQuery] = useState('')
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('')

  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery)
    }, debounceMs)

    return () => clearTimeout(timer)
  }, [searchQuery, debounceMs])

  // Generate available search terms
  const availableTerms = useMemo(() => {
    return getAvailableTerms(items)
  }, [items, getAvailableTerms])

  // Perform search with suggestions
  const { searchResults, searchSuggestions, hasSearchResults } = useMemo(() => {
    if (!debouncedSearchQuery.trim()) {
      return {
        searchResults: items,
        searchSuggestions: [],
        hasSearchResults: true
      }
    }

    const result = searchWithSuggestions(
      items,
      debouncedSearchQuery,
      getSearchableText,
      availableTerms
    )

    return {
      searchResults: result.results,
      searchSuggestions: result.suggestions.slice(0, maxSuggestions),
      hasSearchResults: result.hasResults
    }
  }, [items, debouncedSearchQuery, getSearchableText, availableTerms, maxSuggestions])

  // Handlers
  const clearSearch = useCallback(() => {
    setSearchQuery('')
  }, [])

  const handleSuggestionClick = useCallback((suggestion: string) => {
    setSearchQuery(suggestion)
  }, [])

  return {
    searchQuery,
    debouncedSearchQuery,
    searchResults,
    searchSuggestions,
    hasSearchResults,
    isSearching: searchQuery !== debouncedSearchQuery,
    setSearchQuery,
    clearSearch,
    handleSuggestionClick
  }
}
