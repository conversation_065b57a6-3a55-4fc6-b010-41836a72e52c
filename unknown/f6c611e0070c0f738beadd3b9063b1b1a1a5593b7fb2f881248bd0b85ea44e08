import { leagueApi } from "@/lib/api/league-client";
import { transformChampionData } from "@/lib/champion-data-transformer";
import { championStoreService } from "@/lib/services/champion-store-service";
import { recommendedPositionsService } from "@/lib/services/recommended-positions-service";
import { ChampionWithStoreData } from "@/lib/types/league-api";
import { normalizeChampionData } from "@/lib/utils/champion-data-utils";

export interface ChampionStats {
  total: number;
  byRole: Record<string, number>;
  byDifficulty: Record<string, number>;
  averageDifficulty: number;
}

// Helper function to transform Community Dragon loadScreenPath to CDN URL
function transformLoadScreenPathToCDN(loadScreenPath: string): string {
  // Extract the part after /ASSETS/ and convert to lowercase
  const assetsIndex = loadScreenPath.indexOf("/ASSETS/");
  if (assetsIndex === -1) {
    throw new Error("Invalid loadScreenPath format");
  }

  const pathAfterAssets = loadScreenPath.substring(assetsIndex + 1); // +1 to remove the leading slash
  const lowercasePath = pathAfterAssets.toLowerCase();

  // Construct the final Community Dragon URL
  const baseUrl =
    "https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default";
  return `${baseUrl}/${lowercasePath}`;
}

export function calculateChampionStats(
  champions: ChampionWithStoreData[]
): ChampionStats {
  if (champions.length === 0) {
    return {
      total: 0,
      byRole: {},
      byDifficulty: {},
      averageDifficulty: 0,
    };
  }

  const roleCount = champions.reduce((acc, champion) => {
    // Count champion for ALL their recommended positions
    if (
      champion.recommendedPositions &&
      champion.recommendedPositions.length > 0
    ) {
      champion.recommendedPositions.forEach((role) => {
        acc[role] = (acc[role] || 0) + 1;
      });
    }
    return acc;
  }, {} as Record<string, number>);

  const difficultyDistribution = champions.reduce((acc, champion) => {
    const range =
      champion.difficulty <= 3
        ? "Easy"
        : champion.difficulty <= 6
        ? "Medium"
        : "Hard";
    acc[range] = (acc[range] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const averageDifficulty =
    Math.round(
      (champions.reduce((sum, c) => sum + c.difficulty, 0) / champions.length) *
        10
    ) / 10;

  return {
    total: champions.length,
    byRole: roleCount,
    byDifficulty: difficultyDistribution,
    averageDifficulty,
  };
}

export async function getChampionDetails(championId: string) {
  // capitalize first letter
  const capitalizedChampionId =
    championId.charAt(0).toUpperCase() + championId.slice(1);

  const champion = await leagueApi.getChampionDetail(capitalizedChampionId);
  const championKey = champion.key;

  // Fetch Community Dragon data for chroma information
  const cdResponse = await fetch(
    `https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/champions/${championKey}.json`
  );


  if (cdResponse.ok) {
    const cdData = await cdResponse.json();
    if (cdData) {
      champion.skins.forEach((skin: any) => {
        const expectedSkinId = parseInt(championKey) * 1000 + skin.num;
        const cdSkin = cdData.skins.find((s: any) => s.id === expectedSkinId);
        if (cdSkin && cdSkin.chromas && cdSkin.chromas.length > 0) {
          skin.chromas = cdSkin.chromas.map((c: any) => ({
            ...c,
            chromaPath: transformToCDNUrl(c.chromaPath),
          }));
        }
      });
    }
  }

  const transformedData = await transformChampionData(champion);

  const backgroundImage = await leagueApi.getChampionSplashUrlNew(
    champion.id,
    0,
    champion.key
  );

  return {
    champion,
    transformedData,
    backgroundImage,
  };
}

function getChampionIdFromSlug(slug: string): string {
  // Special cases for champions with spaces or special characters
  const slugToIdMap: Record<string, string> = {
    leesin: "LeeSin",
    masteryi: "MasterYi",
    missfortune: "MissFortune",
    twistedfate: "TwistedFate",
    xinzhao: "XinZhao",
    aurelionsol: "AurelionSol",
    jarvaniv: "JarvanIV",
    drmundo: "DrMundo",
    nunu: "Nunu",
    tahmkench: "TahmKench",
    chogath: "Chogath",
    khazix: "Khazix",
    kaisa: "Kaisa",
    kogmaw: "KogMaw",
    reksai: "RekSai",
    velkoz: "Velkoz",
    belveth: "Belveth",
    monkeyking: "MonkeyKing",
    ksante: "KSante",
    renataglasc: "Renata",
  };

  // Check if it's a special case
  if (slugToIdMap[slug.toLowerCase()]) {
    return slugToIdMap[slug.toLowerCase()];
  }

  // Default: capitalize first letter
  return slug.charAt(0).toUpperCase() + slug.slice(1);
}

function transformToCDNUrl(path: string): string {
  if (!path) return "";
  const assetsIndex = path.indexOf("/ASSETS/");
  if (assetsIndex === -1) {
    return path;
  }
  const pathAfterAssets = path.substring(assetsIndex + 1);
  const lowercasePath = pathAfterAssets.toLowerCase();
  return `https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/${lowercasePath}`;
}

export async function getStoreChampions(): Promise<ChampionWithStoreData[]> {
  // Get champions data from loldb.info API
  const response = await fetch("https://api.loldb.info/api/champions");
  const data = await response.json();

  if (!data.success || !data.data || !data.data.catalog) {
    throw new Error("Failed to fetch champions from loldb.info API");
  }

  const championsData = data.data.catalog;

  // Fetch champion store data
  const storeData = await championStoreService.getAllChampionStoreData();
  const storeDataMap = new Map();
  storeData.forEach((storeChampion) => {
    storeDataMap.set(storeChampion.name, {
      releaseDate: storeChampion.releaseDate,
      ip: storeChampion.ip,
      rp: storeChampion.rp,
    });
  });

  // Fetch Community Dragon skins data for images
  const cdSkinsResponse = await fetch(
    "https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/skins.json"
  );
  const cdSkinsData = await cdSkinsResponse.json();

  // Fetch recommended positions data
  const recommendedPositionsData =
    await recommendedPositionsService.getAllRecommendedPositions();

  // Transform data for frontend consumption
  const champions = championsData.map((champion: any) => {
    // Calculate the base skin ID by adding three zeros to the champion itemId
    const baseSkinId = champion.itemId * 1000;

    // Find the corresponding skin data in Community Dragon
    const cdSkin = cdSkinsData[baseSkinId];

    let imageUrl = champion.iconUrl
      ? `https://ddragon.leagueoflegends.com/cdn/img/champion/loading/${champion.iconUrl}`
      : "";

    if (cdSkin && cdSkin.loadScreenPath) {
      try {
        imageUrl = transformLoadScreenPathToCDN(cdSkin.loadScreenPath);
      } catch (error) {
        console.warn(
          `Failed to transform loadScreenPath for ${champion.name} (${champion.itemId}):`,
          error
        );
        // Keep the fallback URL
      }
    }

    // Get recommended positions for this champion
    const championKey = champion.itemId.toString();
    const recommendedPositions =
      recommendedPositionsData[championKey]?.recommendedPositions || [];
    const mappedPositions = recommendedPositions
      .filter((pos) => pos && typeof pos === 'string')
      .map((pos) => recommendedPositionsService.mapPositionName(pos));

    // Filter out invalid tags (like requirement_owned and <<tag>>)
    const validTags = champion.tags.filter(
      (tag: string) =>
        tag !== "requirement_owned" &&
        !tag.includes("<<") &&
        !tag.includes(">>")
    );

    const rawChampionData = {
      id: champion.name.replace(/[^a-zA-Z]/g, ""), // Create ID from name for compatibility
      name: champion.name,
      title: champion.description,
      difficulty: 5, // Default difficulty since not provided by loldb API
      image: imageUrl,
      slug: champion.name.toLowerCase().replace(/[^a-z0-9]/g, ""),
      tags: validTags,
      key: champion.itemId.toString(),
      championId: champion.itemId,
      recommendedPositions: mappedPositions,
    };

    const normalizedChampion = normalizeChampionData(rawChampionData);

    return {
      ...normalizedChampion,
      storeData: storeDataMap.get(normalizedChampion.name),
    };
  });

  // Sort alphabetically by default
  champions.sort((a: ChampionWithStoreData, b: ChampionWithStoreData) =>
    a.name.localeCompare(b.name)
  );

  return champions;
}
