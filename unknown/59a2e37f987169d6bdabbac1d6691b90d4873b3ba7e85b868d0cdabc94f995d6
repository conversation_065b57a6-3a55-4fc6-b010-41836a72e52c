import { getStoreChampions } from '@/lib/services/champion-service';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const champions = await getStoreChampions();

    return NextResponse.json({
      success: true,
      data: champions,
      count: champions.length,
      timestamp: new Date().toISOString()
    }, {
      headers: {
        'Cache-Control': 'public, s-maxage=3600, stale-while-revalidate=86400',
        'CDN-Cache-Control': 'public, s-maxage=3600',
        'Vercel-CDN-Cache-Control': 'public, s-maxage=3600'
      }
    })
    
  } catch (error) {
    console.error('❌ Error fetching champions:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch champions data',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { 
      status: 500,
      headers: {
        'Cache-Control': 'no-cache'
      }
    })
  }
}
