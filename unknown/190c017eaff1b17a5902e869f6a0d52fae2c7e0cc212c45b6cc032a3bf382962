import { useState, useMemo, useCallback, useEffect } from 'react'
import { ProcessedChromaData } from '@/lib/types/league-api'
import { MythicCostData } from '@/lib/services/mythic-cost-service'
import { getColorDisplayName, getActualChromaColorName } from '@/lib/utils/chroma-utils'
import { useSearchWithSuggestions, SearchWithSuggestionsReturn } from './use-search-with-suggestions'

export type SortField = 'name' | 'release' | 'price'
export type SortDirection = 'asc' | 'desc'
export type SortOption = 'name-asc' | 'name-desc' | 'release-asc' | 'release-desc' | 'price-asc' | 'price-desc'

export interface ChromaFilters {
  search: string
  champions: string[] // Array of selected champions
  chromaRarities: string[] // Array of selected chroma rarities (Regular, Epic, Legendary, etc.)
  skinRarities: string[] // Array of selected skin rarities (for skin tier filtering)
  chromaColors: string[] // Array of selected chroma colors
  availability: string[] // Array of availability options (Available, Legacy, etc.)
  priceRange: string[] // Array of price ranges
  sortField: SortField // What to sort by
  sortDirection: SortDirection // Sort direction
}

export interface ChromaFiltersInternal extends ChromaFilters {
  debouncedSearch: string // Internal debounced search term
}

interface UseChromaFiltersReturn {
  filteredChromas: ProcessedChromaData[]
  filters: ChromaFilters
  updateFilters: (newFilters: Partial<ChromaFilters>) => void
  resetFilters: () => void
  availableChampions: string[]
  availableChromaRarities: string[]
  availableSkinRarities: string[]
  availableChromaColors: string[]
  availableAvailability: string[]
  availablePriceRanges: string[]
  isSearching: boolean // Indicates if search is being debounced
  searchSuggestions: string[] // "Did You Mean?" suggestions
  hasSearchResults: boolean // Whether current search has results
  searchHook: SearchWithSuggestionsReturn<ProcessedChromaData> // Direct access to search hook
}

const defaultFilters: ChromaFilters = {
  search: '',
  champions: [],
  chromaRarities: [],
  skinRarities: [],
  chromaColors: [],
  availability: [],
  priceRange: [],
  sortField: 'name',
  sortDirection: 'asc'
}

const defaultInternalFilters: ChromaFiltersInternal = {
  ...defaultFilters,
  debouncedSearch: ''
}

export function useChromaFilters(chromas: ProcessedChromaData[], mythicPrices?: Map<string, MythicCostData | null>): UseChromaFiltersReturn {
  const [filters, setFilters] = useState<ChromaFiltersInternal>(defaultInternalFilters)

  // Use the reusable search hook for search with suggestions
  const searchHook = useSearchWithSuggestions({
    items: chromas,
    getSearchableText: (chroma) => [chroma.name, chroma.champion, chroma.skinName, chroma.chromaRarity, chroma.rarity],
    getAvailableTerms: (chromas) => {
      const terms = new Set<string>()
      chromas.forEach(chroma => {
        terms.add(chroma.name)
        terms.add(chroma.champion)
        terms.add(chroma.skinName)
        terms.add(chroma.chromaRarity)
        terms.add(chroma.rarity)
      })
      return Array.from(terms).filter(term => term && term.trim().length > 0)
    },
    debounceMs: 300,
    maxSuggestions: 3,
    minSimilarity: 0.4
  })

  // Initialize search hook with current filter value
  useEffect(() => {
    if (filters.search && !searchHook.searchQuery) {
      searchHook.setSearchQuery(filters.search)
    }
  }, []) // Only run once on mount

  const updateFilters = useCallback((newFilters: Partial<ChromaFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }))

    // Update search hook if search filter changes
    if (newFilters.search !== undefined) {
      searchHook.setSearchQuery(newFilters.search)
    }
  }, [])

  const resetFilters = useCallback(() => {
    setFilters(defaultInternalFilters)
    searchHook.clearSearch()
  }, [])

  // Get available filter options from the data
  const availableChampions = useMemo(() => {
    const champions = new Set(chromas.map(chroma => chroma.champion))
    return Array.from(champions).sort()
  }, [chromas])

  // Get available chroma rarities
  const availableChromaRarities = useMemo(() => {
    const rarities = new Set(chromas.map(chroma => chroma.chromaRarity))
    const filteredRarities = Array.from(rarities)

    // Always include "Others" and "Legacy" in the available rarities, even if no chromas currently have these rarities
    if (!filteredRarities.includes('Others')) {
      filteredRarities.push('Others')
    }
    if (!filteredRarities.includes('Legacy')) {
      filteredRarities.push('Legacy')
    }

    // Define the order for chroma rarities, with "Others" first, then "Legacy", then regular rarities
    const rarityOrder = ['Others', 'Legacy', 'Regular', 'Epic', 'Legendary', 'Mythic', 'Ultimate', 'Exalted', 'Transcendent']

    return filteredRarities.sort((a, b) => {
      const aIndex = rarityOrder.indexOf(a)
      const bIndex = rarityOrder.indexOf(b)
      if (aIndex === -1 && bIndex === -1) return a.localeCompare(b)
      if (aIndex === -1) return 1
      if (bIndex === -1) return -1
      return aIndex - bIndex
    })
  }, [chromas])

  // Get available skin rarities (for skin tier filtering)
  const availableSkinRarities = useMemo(() => {
    const rarities = new Set(chromas.map(chroma => chroma.skinTier))
    const filteredRarities = Array.from(rarities).filter(rarity => rarity !== 'Transcendent')

    // Define the order for skin rarities
    const rarityOrder = ['Regular', 'Legacy', 'Epic', 'Legendary', 'Mythic', 'Ultimate', 'Exalted']

    return filteredRarities.sort((a, b) => {
      const aIndex = rarityOrder.indexOf(a)
      const bIndex = rarityOrder.indexOf(b)
      if (aIndex === -1 && bIndex === -1) return a.localeCompare(b)
      if (aIndex === -1) return 1
      if (bIndex === -1) return -1
      return aIndex - bIndex
    })
  }, [chromas])



  // Get available chroma colors with actual chroma names only
  const availableChromaColors = useMemo(() => {
    const colorSet = new Set<string>()
    chromas.forEach(chroma => {
      // Only use actual chroma color names from the store API format
      const actualColorName = getActualChromaColorName(chroma.name)
      if (actualColorName) {
        colorSet.add(actualColorName)
      }
      // Remove fallback to hex-based color names
    })

    // Sort colors alphabetically
    const colors = Array.from(colorSet)
    return colors.sort((a, b) => a.localeCompare(b))
  }, [chromas])

  const availableAvailability = useMemo(() => {
    const availability = new Set(chromas.map(chroma => chroma.availability))
    return Array.from(availability).sort()
  }, [chromas])

  const availablePriceRanges = useMemo(() => {
    const prices = new Set(chromas.map(chroma => chroma.price))
    return Array.from(prices).sort()
  }, [chromas])

  // Helper function to sort chromas
  const sortChromas = useCallback((chromasToSort: ProcessedChromaData[], sortField: SortField, sortDirection: SortDirection): ProcessedChromaData[] => {
    return [...chromasToSort].sort((a, b) => {
      let comparison = 0

      switch (sortField) {
        case 'name':
          comparison = a.name.localeCompare(b.name)
          break
        case 'release':
          // Handle missing release dates by putting them at the end regardless of sort direction
          if (!a.releaseDate && !b.releaseDate) {
            comparison = 0
          } else if (!a.releaseDate) {
            return 1  // Always put items with no date at the end (don't apply direction inversion)
          } else if (!b.releaseDate) {
            return -1 // Always put items with no date at the end (don't apply direction inversion)
          } else {
            comparison = new Date(a.releaseDate).getTime() - new Date(b.releaseDate).getTime()
          }
          break
        case 'price':
          // Handle missing prices by putting them at the end regardless of sort direction
          const priceA = getPriceValue(a)
          const priceB = getPriceValue(b)

          if (priceA === 999999 && priceB === 999999) {
            comparison = 0
          } else if (priceA === 999999) {
            return 1  // Always put items with no price at the end (don't apply direction inversion)
          } else if (priceB === 999999) {
            return -1 // Always put items with no price at the end (don't apply direction inversion)
          } else {
            comparison = priceA - priceB
          }
          break
        default:
          comparison = 0
      }

      return sortDirection === 'desc' ? -comparison : comparison
    })
  }, [mythicPrices])

  // Helper function to extract numeric price value for sorting
  // Mythic Essence prices are always treated as higher than RP prices
  const getPriceValue = useCallback((chroma: ProcessedChromaData): number => {
    // Check if this chroma has mythic pricing
    if (chroma.availability !== 'Available' && mythicPrices && chroma.contentId) {
      const mythicPrice = mythicPrices.get(chroma.contentId)
      if (mythicPrice && mythicPrice.cost) {
        // Add a large base value to ensure ME prices are always higher than RP
        return 100000 + mythicPrice.cost
      }
    }

    // Handle regular RP prices
    const price = chroma.price
    // Handle special cases
    if (price === 'Legacy' || price === 'N/A' || price === 'Bundle Only' || price === 'Event Only') {
      return 999999 // Put these at the end
    }

    // Extract numeric value from "290 RP" format
    const match = price.match(/(\d+)/)
    return match ? parseInt(match[1], 10) : 999999
  }, [mythicPrices])

  // Get search results from the reusable search hook
  const searchFilteredChromas = searchHook.searchResults
  const searchSuggestions = searchHook.searchSuggestions
  const hasSearchResults = searchHook.hasSearchResults

  // Apply additional filters to search results
  const filteredChromas = useMemo(() => {
    const filtered = searchFilteredChromas.filter(chroma => {

      // Champion filter
      if (filters.champions.length > 0 && !filters.champions.includes(chroma.champion)) {
        return false
      }

      // Chroma rarity filter with special handling for "Legacy"
      if (filters.chromaRarities.length > 0) {
        const hasLegacyFilter = filters.chromaRarities.includes('Legacy')
        const otherRarities = filters.chromaRarities.filter(r => r !== 'Legacy')

        let matchesRarity = false

        // Check if chroma matches regular rarity filters
        if (otherRarities.length > 0 && otherRarities.includes(chroma.chromaRarity)) {
          matchesRarity = true
        }

        // Check if chroma matches Legacy filter (availability === 'Legacy')
        if (hasLegacyFilter && chroma.availability === 'Legacy') {
          matchesRarity = true
        }

        if (!matchesRarity) {
          return false
        }
      }

      // Skin rarity filter
      if (filters.skinRarities.length > 0 && !filters.skinRarities.includes(chroma.skinTier)) {
        return false
      }



      // Chroma color filter - only use actual chroma color names
      if (filters.chromaColors.length > 0) {
        const actualColorName = getActualChromaColorName(chroma.name)
        if (!actualColorName || !filters.chromaColors.includes(actualColorName)) {
          return false
        }
      }

      // Availability filter
      if (filters.availability.length > 0 && !filters.availability.includes(chroma.availability)) {
        return false
      }

      // Price range filter
      if (filters.priceRange.length > 0 && !filters.priceRange.includes(chroma.price)) {
        return false
      }

      return true
    })

    // Apply sorting to the filtered results
    return sortChromas(filtered, filters.sortField, filters.sortDirection)
  }, [searchFilteredChromas, filters.champions, filters.chromaRarities, filters.skinRarities, filters.chromaColors, filters.availability, filters.priceRange, filters.sortField, filters.sortDirection, sortChromas, mythicPrices])

  // Create public filters interface (using search hook's query)
  const publicFilters: ChromaFilters = useMemo(() => ({
    search: searchHook.searchQuery,
    champions: filters.champions,
    chromaRarities: filters.chromaRarities,
    skinRarities: filters.skinRarities,
    chromaColors: filters.chromaColors,
    availability: filters.availability,
    priceRange: filters.priceRange,
    sortField: filters.sortField,
    sortDirection: filters.sortDirection
  }), [searchHook.searchQuery, filters.champions, filters.chromaRarities, filters.skinRarities, filters.chromaColors, filters.availability, filters.priceRange, filters.sortField, filters.sortDirection])

  return {
    filteredChromas,
    filters: publicFilters,
    updateFilters,
    resetFilters,
    availableChampions,
    availableChromaRarities,
    availableSkinRarities,
    availableChromaColors,
    availableAvailability,
    availablePriceRanges,
    isSearching: searchHook.isSearching,
    searchSuggestions,
    hasSearchResults,
    searchHook
  }
}
