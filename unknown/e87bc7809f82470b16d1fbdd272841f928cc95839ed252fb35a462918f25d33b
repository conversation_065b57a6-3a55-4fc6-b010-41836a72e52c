// Universal champion data utilities for consistent data across all pages

export interface UniversalChampionData {
  id: string
  name: string
  title: string
  image: string
  slug: string
  tags: string[]
  difficulty: number
  role: string
  class: string
  championId: number
  recommendedPositions: string[]
}

// Utility function to get position icon URLs
export function getPositionIconUrl(role: string): string {
  const positionIcons: Record<string, string> = {
    'Top': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-social/global/default/positionicon-top.png',
    'Middle': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-social/global/default/positionicon-mid.png',
    'Jungle': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-social/global/default/positionicon-jungle.png',
    'Bottom': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-social/global/default/positionicon-bottom.png',
    'Support': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-social/global/default/positionicon-support.png'
  }
  return positionIcons[role] || positionIcons['Top'] // Fallback to Top icon
}

// Utility function to get class icon URLs
export function getClassIconUrl(className: string): string {
  if (!className) {
    return 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-fighter.png'
  }

  const classIcons: Record<string, string> = {
    // Standard capitalized names
    'Fighter': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-fighter.png',
    'Tank': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-tank.png',
    'Mage': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-mage.png',
    'Assassin': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-assassin.png',
    'Support': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-support.png',
    'Marksman': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-marksman.png',

    // Lowercase variants from loldb.info API
    'fighter': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-fighter.png',
    'tank': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-tank.png',
    'mage': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-mage.png',
    'assassin': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-assassin.png',
    'support': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-support.png',
    'marksman': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-marksman.png',

    // Additional mappings for loldb.info API tags
    'carry': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-marksman.png',
    'ranged': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-marksman.png',
    'melee': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-fighter.png',
    'jungler': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-fighter.png',
    'recommended': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-fighter.png',
    'pusher': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-fighter.png'
  }

  // Return the icon URL or fallback to Fighter icon
  return classIcons[className] || classIcons['Fighter']
}

// Function to get champion's primary class from tags
export function getChampionClass(tags: string[]): string {
  if (!tags || tags.length === 0) return 'Fighter'

  // Define the priority order for champion classes
  const classPriority = ['fighter', 'tank', 'mage', 'assassin', 'marksman', 'support']

  // Find the first tag that matches a known class
  for (const tag of tags) {
    const lowerTag = tag.toLowerCase()
    if (classPriority.includes(lowerTag)) {
      // Capitalize first letter for display
      return lowerTag.charAt(0).toUpperCase() + lowerTag.slice(1)
    }
  }

  // Fallback: capitalize the first tag
  return tags[0] ? tags[0].charAt(0).toUpperCase() + tags[0].slice(1) : 'Fighter'
}

// Function to normalize champion data from any source
export function normalizeChampionData(champion: any): UniversalChampionData {
  const tags = champion.tags
  const championClass = getChampionClass(tags)

  // Always use recommended positions - no fallbacks
  const role = champion.recommendedPositions && champion.recommendedPositions.length > 0
    ? champion.recommendedPositions[0] // Primary role is first in array
    : 'Unknown' // This should not happen if API data is complete

  return {
    id: champion.id,
    name: champion.name,
    title: champion.title,
    role: role,
    class: championClass,
    difficulty: champion.difficulty,
    image: champion.image,
    slug: champion.slug,
    tags: tags,
    championId: champion.championId,
    recommendedPositions: champion.recommendedPositions || []
  }
}
