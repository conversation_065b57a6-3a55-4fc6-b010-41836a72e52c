#!/usr/bin/env node

// Test script for champion detail API

const https = require('https');

const testChampions = ['<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 'Thresh'];

function testChampionDetail(championId) {
  return new Promise((resolve) => {
    const url = `https://ddragon.leagueoflegends.com/cdn/15.11.1/data/en_US/champion/${championId}.json`;
    
    console.log(`🔍 Testing champion: ${championId}`);
    
    https.get(url, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          const championData = result.data[championId];
          
          if (championData) {
            resolve({
              success: true,
              champion: championId,
              name: championData.name,
              title: championData.title,
              skins: championData.skins.length,
              abilities: championData.spells.length,
              hasPassive: !!championData.passive,
              hasLore: !!championData.lore,
              tags: championData.tags
            });
          } else {
            resolve({
              success: false,
              champion: championId,
              error: 'Champion data not found in response'
            });
          }
        } catch (error) {
          resolve({
            success: false,
            champion: championId,
            error: `Parse error: ${error.message}`
          });
        }
      });
    }).on('error', (error) => {
      resolve({
        success: false,
        champion: championId,
        error: `Network error: ${error.message}`
      });
    });
  });
}

async function runTests() {
  console.log('🎮 Testing Champion Detail API Integration\n');
  
  const results = [];
  
  for (const champion of testChampions) {
    const result = await testChampionDetail(champion);
    results.push(result);
    
    if (result.success) {
      console.log(`✅ ${champion} (${result.name})`);
      console.log(`   Title: ${result.title}`);
      console.log(`   Skins: ${result.skins}`);
      console.log(`   Abilities: ${result.abilities}`);
      console.log(`   Passive: ${result.hasPassive ? 'Yes' : 'No'}`);
      console.log(`   Lore: ${result.hasLore ? 'Yes' : 'No'}`);
      console.log(`   Tags: ${result.tags.join(', ')}`);
    } else {
      console.log(`❌ ${champion}: ${result.error}`);
    }
    console.log('');
  }
  
  // Summary
  const successful = results.filter(r => r.success).length;
  const total = results.length;
  
  console.log('📊 Test Summary:');
  console.log('=' .repeat(50));
  console.log(`Total Champions Tested: ${total}`);
  console.log(`Successful: ${successful}`);
  console.log(`Failed: ${total - successful}`);
  console.log(`Success Rate: ${Math.round((successful / total) * 100)}%`);
  
  if (successful === total) {
    console.log('\n🎉 All champion APIs working! Dynamic pages ready.');
    console.log('\n🚀 Test your dynamic champion pages:');
    console.log('1. Start dev server: npm run dev');
    console.log('2. Visit these URLs:');
    testChampions.forEach(champion => {
      console.log(`   - http://localhost:3000/champions/${champion.toLowerCase()}`);
    });
  } else {
    console.log('\n⚠️  Some champions failed. Check the errors above.');
  }
}

runTests();
