import { skinService } from '@/lib/services/skin-service'
import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const refresh = searchParams.get('refresh') === 'true'

    

    // Clear cache if refresh is requested
    if (refresh) {
      skinService.clearCache()
      
    }

    const allSkins = await skinService.getAllSkins()
    
    
    
    return NextResponse.json({
      success: true,
      data: allSkins,
      count: allSkins.length,
      timestamp: new Date().toISOString()
    }, {
      headers: {
        'Cache-Control': 'public, s-maxage=86400, stale-while-revalidate=172800', // 24h cache, 48h stale
        'CDN-Cache-Control': 'public, s-maxage=86400',
        'Vercel-CDN-Cache-Control': 'public, s-maxage=86400'
      }
    })

  } catch (error) {
    console.error('❌ Failed to fetch skins data:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch skins data',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

// Handle cache management operations
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action } = body

    switch (action) {
      case 'clear-cache':
        skinService.clearCache()
        return NextResponse.json({
          success: true,
          message: 'Skins cache cleared successfully'
        })

      case 'get-stats':
        const stats = skinService.getCacheStats()
        return NextResponse.json({
          success: true,
          data: stats
        })

      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid action. Supported actions: clear-cache, get-stats'
        }, { status: 400 })
    }
  } catch (error) {
    console.error('❌ Failed to handle cache operation:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to handle cache operation'
    }, { status: 500 })
  }
}
