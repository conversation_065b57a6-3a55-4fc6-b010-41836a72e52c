"use client"

import * as React from "react"
import { ChevronDown, X, Search } from "lucide-react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Input } from "@/components/ui/input"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Badge } from "@/components/ui/badge"
import Image from "next/image"

interface MultiSelectClassesProps {
  selectedClasses: string[]
  onSelectionChange: (classes: string[]) => void
  availableClasses: string[]
  placeholder?: string
  className?: string
}

// Helper function to get class icon URL
function getClassIconUrl(className: string): string {
  const baseUrl = 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-'
  const classMap: Record<string, string> = {
    'Assassin': `${baseUrl}assassin.png`,
    'Fighter': `${baseUrl}fighter.png`,
    'Mage': `${baseUrl}mage.png`,
    'Marksman': `${baseUrl}marksman.png`,
    'Support': `${baseUrl}support.png`,
    'Tank': `${baseUrl}tank.png`
  }
  return classMap[className] || `${baseUrl}fighter.png`
}

export function MultiSelectClasses({
  selectedClasses,
  onSelectionChange,
  availableClasses,
  placeholder = "Select classes...",
  className
}: MultiSelectClassesProps) {
  const [open, setOpen] = React.useState(false)
  const [searchTerm, setSearchTerm] = React.useState("")

  // Add "All" option to available classes
  const allClasses = React.useMemo(() => {
    return ['All', ...availableClasses]
  }, [availableClasses])

  const filteredClasses = React.useMemo(() => {
    if (!searchTerm.trim()) return allClasses

    return allClasses.filter(cls =>
      cls.toLowerCase().includes(searchTerm.toLowerCase())
    )
  }, [allClasses, searchTerm])

  const handleClassToggle = (cls: string) => {
    if (cls === 'All') {
      // If "All" is selected, clear all other selections
      onSelectionChange([])
    } else {
      // If a specific class is selected, remove "All" if it was selected and toggle the class
      const newSelection = selectedClasses.includes(cls)
        ? selectedClasses.filter(c => c !== cls)
        : [...selectedClasses, cls]

      onSelectionChange(newSelection)
    }
  }

  const handleClearAll = () => {
    onSelectionChange([])
  }

  const renderSelectedBadges = () => {
    if (selectedClasses.length === 0) {
      return <span className="text-gray-300">All</span>
    }

    return (
      <span className="text-gray-300">
        Classes +{selectedClasses.length}
      </span>
    )
  }

  return (
    <div className={className}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between bg-gray-900/40 border-gray-700/20 text-white hover:bg-gray-800/50 h-[40px] items-center"
          >
            <div className="flex-1 text-left">
              {renderSelectedBadges()}
            </div>
            <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0 bg-gray-900 border-gray-700" align="start">
          <div className="p-3">
            <div className="flex items-center justify-between mb-3">
              <span className="text-sm font-medium text-gray-300">Select Classes</span>
              {selectedClasses.length > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleClearAll}
                  className="text-xs text-gray-400 hover:text-white h-6"
                >
                  Clear All
                </Button>
              )}
            </div>

            <div className="relative mb-3">
              <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-gray-400" />
              <Input
                placeholder="Search classes..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-7 h-8 bg-gray-800/50 border-gray-600 text-white placeholder-gray-400 text-sm"
              />
            </div>

            <div className="space-y-1 max-h-60 overflow-y-auto">
              {filteredClasses.length === 0 ? (
                <div className="text-center py-4 text-gray-400 text-sm">
                  No classes found
                </div>
              ) : (
                filteredClasses.map((cls) => (
                  <div
                    key={cls}
                    className="flex items-center space-x-3 p-1 rounded hover:bg-gray-800/50 cursor-pointer"
                    onClick={() => handleClassToggle(cls)}
                  >
                    <Checkbox
                      checked={cls === 'All' ? selectedClasses.length === 0 : selectedClasses.includes(cls)}
                      onCheckedChange={() => handleClassToggle(cls)}
                      className="border-gray-600 data-[state=checked]:bg-orange-600 data-[state=checked]:border-orange-600"
                    />
                    <div className="flex items-center gap-2 flex-1">
                      {cls !== 'All' && (
                        <Image
                          src={getClassIconUrl(cls)}
                          alt={cls}
                          width={16}
                          height={16}
                          className="w-4 h-4 flex-shrink-0"
                          unoptimized
                        />
                      )}
                      <span className="text-gray-300 capitalize">{cls}</span>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  )
}
