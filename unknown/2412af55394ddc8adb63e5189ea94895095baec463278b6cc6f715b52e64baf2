"use client"

import React, { useEffect } from 'react'

interface ChampionBackgroundProps {
  championName?: string
  customImageUrl?: string
  children: React.ReactNode
  blur?: number
  brightness?: number
  overlay?: string
}

export default function ChampionBackground({
  championName,
  customImageUrl,
  children,
  blur = 10,
  brightness = 0.25,
  overlay = 'bg-gradient-to-b from-black/40 via-black/20 to-black/60'
}: ChampionBackgroundProps) {
  // Use custom URL if provided, otherwise generate from champion name
  const backgroundUrl = customImageUrl ||
    (championName ? `https://ddragon.leagueoflegends.com/cdn/img/champion/splash/Jinx_0.jpg` : '')

  useEffect(() => {
    if (backgroundUrl) {
      // Create background element with blur effect
      const backgroundElement = document.createElement('div')
      backgroundElement.id = 'champion-background-image'
      backgroundElement.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        z-index: 0;
        background-image: url('${backgroundUrl}');
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        filter: blur(${blur}px) brightness(${brightness}) saturate(1.2);
        transform: scale(1.1);
        pointer-events: none;
      `
      document.body.insertBefore(backgroundElement, document.body.firstChild)

      // Add overlay
      const overlay = document.createElement('div')
      overlay.id = 'champion-background-overlay'
      overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        z-index: 1;
        background: linear-gradient(to bottom, rgba(0,0,0,0.4), rgba(0,0,0,0.2), rgba(0,0,0,0.6)),
                    linear-gradient(to right, rgba(0,0,0,0.6), rgba(0,0,0,0.2), rgba(0,0,0,0.6)),
                    linear-gradient(to top, rgba(17, 24, 39, 0.9), transparent, rgba(17, 24, 39, 0.5));
        pointer-events: none;
      `
      document.body.insertBefore(overlay, document.body.firstChild)

      // Header z-index is now handled by CSS media queries
      // No need to manipulate header z-index via JavaScript

      const main = document.querySelector('main')
      if (main instanceof HTMLElement) {
        main.style.position = 'relative'
        main.style.zIndex = '10'
      }

      const footer = document.querySelector('footer')
      if (footer instanceof HTMLElement) {
        footer.style.position = 'relative'
        footer.style.zIndex = '10'
      }

      // Handle sidebar separately to preserve its fixed positioning
      const sidebar = document.querySelector('aside')
      if (sidebar instanceof HTMLElement) {
        sidebar.style.zIndex = '50' // Higher than background but lower than header
        // Don't change position as it's already fixed
      }

      // Cleanup function
      return () => {
        const existingBackground = document.getElementById('champion-background-image')
        if (existingBackground) {
          existingBackground.remove()
        }

        const existingOverlay = document.getElementById('champion-background-overlay')
        if (existingOverlay) {
          existingOverlay.remove()
        }

        // Header z-index is handled by CSS, no need to reset

        const sidebar = document.querySelector('aside')
        if (sidebar instanceof HTMLElement) {
          sidebar.style.zIndex = ''
          // Don't reset position as it should remain fixed
        }

        const main = document.querySelector('main')
        if (main instanceof HTMLElement) {
          main.style.zIndex = ''
          main.style.position = ''
        }

        const footer = document.querySelector('footer')
        if (footer instanceof HTMLElement) {
          footer.style.zIndex = ''
          footer.style.position = ''
        }
      }
    }
  }, [backgroundUrl, blur, brightness])

  return (
    <div className="relative min-h-screen">
      {children}
    </div>
  )
}
