import { NextRequest, NextResponse } from 'next/server'

// Helper function to extract champion ID from item ID
function extractChampionId(itemId: number): number {
  const itemIdStr = itemId.toString()

  if (itemIdStr.length === 4) {
    // 4-digit IDs: use first digit
    return parseInt(itemIdStr.charAt(0))
  } else if (itemIdStr.length === 5) {
    // 5-digit IDs: use first 2 digits if 3rd is 0, otherwise first 3 digits
    const thirdDigit = itemIdStr.charAt(2)
    if (thirdDigit === '0') {
      return parseInt(itemIdStr.substring(0, 2))
    } else {
      return parseInt(itemIdStr.substring(0, 3))
    }
  } else {
    // For other lengths, try first 2-3 digits
    return parseInt(itemIdStr.substring(0, Math.min(3, itemIdStr.length)))
  }
}

// Helper function to get champion data by ID
async function getChampionData(championId: number): Promise<{ name: string; slug: string; image: string } | null> {
  try {
    // Fetch all champions from DDragon to find the one with matching key
    const versionsResponse = await fetch('https://ddragon.leagueoflegends.com/api/versions.json')
    const versions = await versionsResponse.json()
    const currentVersion = versions[0]

    const url = `https://ddragon.leagueoflegends.com/cdn/${currentVersion}/data/en_US/champion.json`
    const response = await fetch(url)
    const data = await response.json()
    const championsData = data.data

    // Find champion by key (numeric ID)
    const champion = Object.values(championsData).find((champ: any) => parseInt(champ.key) === championId)

    if (champion) {
      const championData = champion as any
      return {
        name: championData.name,
        slug: championData.id.toLowerCase(),
        image: `https://ddragon.leagueoflegends.com/cdn/img/champion/loading/${championData.id}_0.jpg`
      }
    }

    // Fallback: return basic data
    console.warn(`⚠️ API: Champion with key ${championId} not found in DDragon`)
    return {
      name: `Champion ${championId}`,
      slug: `champion-${championId}`,
      image: '/placeholder.svg'
    }
  } catch (error) {
    console.warn(`⚠️ API: Failed to get champion data for ID ${championId}:`, error)
    return {
      name: `Champion ${championId}`,
      slug: `champion-${championId}`,
      image: '/placeholder.svg'
    }
  }
}

// Helper function to get skin data from skins API
async function getSkinData(itemId: number): Promise<{ name: string; image?: string; originalPrice?: number } | null> {
  try {
    const response = await fetch('https://api.loldb.info/api/store/skins')
    if (response.ok) {
      const data = await response.json()
      // The skins API returns data under data.catalog array
      const catalog = data.data?.catalog || []
      const skin = catalog.find((s: any) => s.itemId === itemId && s.inventoryType === 'CHAMPION_SKIN')
      if (skin) {
        return {
          name: skin.name,
          image: skin.iconUrl || null,
          originalPrice: skin.rp
        }
      }
    }
    return null
  } catch (error) {
    console.warn(`⚠️ API: Failed to get skin data for item ID ${itemId}:`, error)
    return null
  }
}

// Helper function to get default prices
function getDefaultSkinPrice(salePrice: number): number {
  if (salePrice <= 300) return 520    // Regular skins ~40% off
  if (salePrice <= 500) return 750    // Regular skins ~33% off
  if (salePrice <= 700) return 975    // Epic skins ~30% off
  if (salePrice <= 900) return 1350   // Epic skins ~33% off
  if (salePrice <= 1200) return 1820  // Legendary skins ~33% off
  return Math.round(salePrice * 1.5)  // Default 33% discount assumption
}

function getDefaultChampionPrice(salePrice: number): number {
  if (salePrice <= 300) return 450    // 1350 BE champions
  if (salePrice <= 400) return 585    // 3150 BE champions
  if (salePrice <= 500) return 790    // 4800 BE champions
  return 975  // 6300 BE champions (most expensive)
}

// Process a single discount item
async function processDiscountItem(item: any) {
  try {
    // Extract champion ID from item ID
    const championId = extractChampionId(item.item.itemId)

    // Get champion data
    const championData = await getChampionData(championId)
    if (!championData) {
      console.warn(`⚠️ API: No champion data found for ID ${championId}`)
      return null
    }

    // Get pricing info
    const priceInfo = item.sale.prices[0] // Use first price entry
    if (!priceInfo) {
      console.warn(`⚠️ API: No price info found for item ${item.id}`)
      return null
    }

    let processedItem: any = {
      id: item.id,
      type: item.item.inventoryType,
      itemId: item.item.itemId,
      championId,
      championName: championData.name,
      championSlug: championData.slug,
      image: championData.image,
      originalPrice: 0, // Will be calculated
      salePrice: priceInfo.cost,
      currency: priceInfo.currency,
      discount: 0, // Will be calculated
      endDate: item.sale.endDate,
      active: item.active
    }

    // Handle skin-specific data
    if (item.item.inventoryType === 'CHAMPION_SKIN') {
      const skinData = await getSkinData(item.item.itemId)
      if (skinData) {
        processedItem.skinName = skinData.name
        processedItem.skinId = item.item.itemId
        if (skinData.image) {
          processedItem.image = skinData.image
        }
        processedItem.originalPrice = skinData.originalPrice || getDefaultSkinPrice(priceInfo.cost)
      } else {
        // Fallback if skin data not found
        processedItem.skinName = `${championData.name} Skin`
        processedItem.skinId = item.item.itemId
        processedItem.originalPrice = getDefaultSkinPrice(priceInfo.cost)
      }
    } else {
      // Champion purchase
      processedItem.originalPrice = getDefaultChampionPrice(priceInfo.cost)
    }

    // Calculate discount percentage
    if (processedItem.originalPrice > 0) {
      processedItem.discount = Math.round(((processedItem.originalPrice - processedItem.salePrice) / processedItem.originalPrice) * 100)
    }

    return processedItem

  } catch (error) {
    console.error(`❌ API: Error processing discount item ${item.id}:`, error)
    return null
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const refresh = searchParams.get('refresh') === 'true'

    

    // Fetch raw sales data from external API
    const salesResponse = await fetch('https://api.loldb.info/api/store/sales')
    if (!salesResponse.ok) {
      throw new Error(`HTTP ${salesResponse.status}: Failed to fetch sales data`)
    }

    const salesResult = await salesResponse.json()
    if (!salesResult.success) {
      throw new Error(salesResult.error || 'Failed to fetch sales data')
    }

    const salesData = salesResult.data || []
    

    // Filter only active items
    const activeItems = salesData.filter((item: any) => item.active)
    

    // Process each active item
    const processedItems = []

    for (const item of activeItems) {
      try {
        const processedItem = await processDiscountItem(item)
        if (processedItem) {
          processedItems.push(processedItem)
        }
      } catch (error) {
        console.warn(`⚠️ API: Failed to process item ${item.id}:`, error)
      }
    }

    

    return NextResponse.json({
      success: true,
      data: processedItems,
      count: processedItems.length,
      timestamp: new Date().toISOString()
    }, {
      headers: {
        'Cache-Control': 'public, s-maxage=60, stale-while-revalidate=3600', // 1min cache, 1h stale
        'CDN-Cache-Control': 'public, s-maxage=60',
        'Vercel-CDN-Cache-Control': 'public, s-maxage=60'
      }
    })

  } catch (error) {
    console.error('❌ Failed to fetch sales data:', error)

    return NextResponse.json({
      success: false,
      error: 'Failed to fetch sales data',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

// Handle cache management operations
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action } = body

    switch (action) {
      case 'clear-cache':
        // For now, just return success since we don't have persistent cache
        return NextResponse.json({
          success: true,
          message: 'Sales cache cleared successfully'
        })

      case 'get-stats':
        // Return basic stats
        return NextResponse.json({
          success: true,
          data: {
            cached: false,
            timestamp: null,
            itemCount: 0,
            age: 0,
            ttl: 1800000 // 30 minutes
          }
        })

      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid action. Supported actions: clear-cache, get-stats'
        }, { status: 400 })
    }
  } catch (error) {
    console.error('❌ Failed to handle cache operation:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to handle cache operation'
    }, { status: 500 })
  }
}
