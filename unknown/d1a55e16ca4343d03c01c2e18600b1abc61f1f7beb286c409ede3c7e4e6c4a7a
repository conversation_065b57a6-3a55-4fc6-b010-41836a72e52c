"use client"

import * as React from "react"
import { ChevronDown, X, Search } from "lucide-react"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Input } from "@/components/ui/input"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Badge } from "@/components/ui/badge"
import Image from "next/image"

interface MultiSelectRolesProps {
  selectedRoles: string[]
  onSelectionChange: (roles: string[]) => void
  availableRoles: string[]
  placeholder?: string
  className?: string
}

// Helper function to get position icon URL
function getPositionIconUrl(role: string): string {
  const roleMap: Record<string, string> = {
    'Top': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-parties/global/default/icon-position-top.png',
    'Jungle': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-parties/global/default/icon-position-jungle.png',
    'Middle': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-parties/global/default/icon-position-middle.png',
    'Bottom': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-parties/global/default/icon-position-bottom.png',
    'Support': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-parties/global/default/icon-position-utility.png'
  }
  return roleMap[role] || 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-parties/global/default/icon-position-fill.png'
}

export function MultiSelectRoles({
  selectedRoles,
  onSelectionChange,
  availableRoles,
  placeholder = "Select roles...",
  className
}: MultiSelectRolesProps) {
  const [open, setOpen] = React.useState(false)
  const [searchTerm, setSearchTerm] = React.useState("")

  // Add "Any" option to available roles
  const allRoles = React.useMemo(() => {
    return ['Any', ...availableRoles]
  }, [availableRoles])

  const filteredRoles = React.useMemo(() => {
    if (!searchTerm.trim()) return allRoles

    return allRoles.filter(role =>
      role.toLowerCase().includes(searchTerm.toLowerCase())
    )
  }, [allRoles, searchTerm])

  const handleRoleToggle = (role: string) => {
    if (role === 'Any') {
      // If "Any" is selected, clear all other selections
      onSelectionChange([])
    } else {
      // If a specific role is selected, remove "Any" if it was selected and toggle the role
      const newSelection = selectedRoles.includes(role)
        ? selectedRoles.filter(r => r !== role)
        : [...selectedRoles, role]

      onSelectionChange(newSelection)
    }
  }

  const handleClearAll = () => {
    onSelectionChange([])
  }

  const renderSelectedBadges = () => {
    if (selectedRoles.length === 0) {
      return <span className="text-gray-300">Any</span>
    }

    return (
      <span className="text-gray-300">
        Roles +{selectedRoles.length}
      </span>
    )
  }

  return (
    <div className={className}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between bg-gray-900/40 border-gray-700/20 text-white hover:bg-gray-800/50 h-[40px] items-center"
          >
            <div className="flex-1 text-left">
              {renderSelectedBadges()}
            </div>
            <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0 bg-gray-900 border-gray-700" align="start">
          <div className="p-3">
            <div className="flex items-center justify-between mb-3">
              <span className="text-sm font-medium text-gray-300">Select Roles</span>
              {selectedRoles.length > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleClearAll}
                  className="text-xs text-gray-400 hover:text-white h-6"
                >
                  Clear All
                </Button>
              )}
            </div>

            <div className="relative mb-3">
              <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-gray-400" />
              <Input
                placeholder="Search roles..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-7 h-8 bg-gray-800/50 border-gray-600 text-white placeholder-gray-400 text-sm"
              />
            </div>

            <div className="space-y-1 max-h-60 overflow-y-auto">
              {filteredRoles.length === 0 ? (
                <div className="text-center py-4 text-gray-400 text-sm">
                  No roles found
                </div>
              ) : (
                filteredRoles.map((role) => (
                  <div
                    key={role}
                    className="flex items-center space-x-3 p-1 rounded hover:bg-gray-800/50 cursor-pointer"
                    onClick={() => handleRoleToggle(role)}
                  >
                    <Checkbox
                      checked={role === 'Any' ? selectedRoles.length === 0 : selectedRoles.includes(role)}
                      onCheckedChange={() => handleRoleToggle(role)}
                      className="border-gray-600 data-[state=checked]:bg-orange-600 data-[state=checked]:border-orange-600"
                    />
                    <div className="flex items-center gap-2 flex-1">
                      <Image
                        src={role === 'Any' ? 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-parties/global/default/icon-position-fill.png' : getPositionIconUrl(role)}
                        alt={role}
                        width={16}
                        height={16}
                        className="w-4 h-4 flex-shrink-0"
                        unoptimized
                      />
                      <span className="text-gray-300">{role}</span>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  )
}
