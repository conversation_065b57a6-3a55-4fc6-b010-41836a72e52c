"use client"

import { useState, useEffect, useRef } from "react"
import type { Metada<PERSON> } from 'next'
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import SharedLayout from "@/components/shared-layout"
import SkinBackground from "@/components/skin-background"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { ExternalLink, Bell, Calendar, Sparkles, ArrowLeft } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { ProcessedSkinData } from "@/lib/services/skin-service"
import { ProcessedChromaData, CommunityDragonSkinTier } from "@/lib/types/league-api"
import { SkinRarityIcons, SkinRarityIconsSimple } from "@/components/ui/skin-rarity-icons"
import { SkinDetailSkeleton } from "@/components/skeletons/skin-detail-skeleton"
import { mythicCostService, MythicCostData } from "@/lib/services/mythic-cost-service"
import { useMythicPrices } from "@/hooks/use-mythic-prices"
import { getChampionSlug } from "@/lib/utils/champion-slug"
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import CountdownTimer from "@/components/ui/countdown-timer"

// Interface for chroma data
interface ChromaData {
  id: string
  name: string
  color: string
  colors?: string[]
  chromaPath?: string
  skinId?: number
  isDefault?: boolean
}

// Function to create URL-friendly skin names
function createSkinSlug(skinName: string): string {
  return skinName
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters except spaces and hyphens
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .replace(/^-|-$/g, '') // Remove leading/trailing hyphens
}

// Rarity color mapping
const rarityColors: Record<string, string> = {
  'Regular': '#299645',
  'Legacy': '#c1a26a', 
  'Epic': '#27d7e5',
  'Legendary': '#e81b23',
  'Mythic': '#b47bdf',
  'Ultimate': '#d7861f',
  'Exalted': '#d3ce5a', // emerald-500 equivalent
  'Transcendent': '#cda4de'
}

function getRarityStyle(tier: string, isLegacy: boolean) {
  const color = isLegacy ? rarityColors['Legacy'] : rarityColors[tier] || rarityColors['Regular']
  return {
    borderColor: `${color}40`,
    color: color,
    backgroundColor: `${color}20`
  }
}

interface SlugPageProps {
  params: Promise<{
    slug: string
  }>
}

export default function SlugPage({ params }: SlugPageProps) {
  const router = useRouter()
  const [slug, setSlug] = useState<string | null>(null)
  const [skin, setSkin] = useState<ProcessedSkinData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [relatedSkins, setRelatedSkins] = useState<ProcessedSkinData[]>([])
  const [hasActualChromas, setHasActualChromas] = useState<boolean>(false)
  const [isRarityRedirect, setIsRarityRedirect] = useState(false)
  const [lastDiscount, setLastDiscount] = useState<string>('Loading...')
  const [mythicCost, setMythicCost] = useState<MythicCostData | null>(null)
  const [mythicCostLoading, setMythicCostLoading] = useState(false)

  // New state for image mode toggle and chroma selection - Following champion page logic
  const [showSplashArt, setShowSplashArt] = useState(true) // Start with splash art (like champion page)
  const [userPrefersInGame, setUserPrefersInGame] = useState(false) // Remember user preference
  const [selectedChromaId, setSelectedChromaId] = useState<string | null>(null) // No chroma selected initially
  const [showChromas, setShowChromas] = useState(false)
  const [chromaData, setChromaData] = useState<ChromaData[]>([])
  const [chromasAnimating, setChromasAnimating] = useState(false)
  const chromasRefDesktop = useRef<HTMLDivElement>(null)
  const chromasRefMobile = useRef<HTMLDivElement>(null)

  // Quest tier states
  const [showQuestTiers, setShowQuestTiers] = useState(false)
  const [selectedTier, setSelectedTier] = useState<CommunityDragonSkinTier | null>(null)
  const [questTiersAnimating, setQuestTiersAnimating] = useState(false)
  const questTiersRefDesktop = useRef<HTMLDivElement>(null)
  const questTiersRefMobile = useRef<HTMLDivElement>(null)

  // Fetch mythic prices for related skins
  const { mythicPrices } = useMythicPrices(relatedSkins)

  // Function to fetch mythic cost data
  const fetchMythicCost = async (contentId: string) => {
    try {
      setMythicCostLoading(true)

      const mythicResponse = await mythicCostService.getMythicCost(contentId)

      if (mythicResponse.success && mythicResponse.data) {
        setMythicCost(mythicResponse.data)
      } else {
        setMythicCost(null)
      }

    } catch (error) {
      console.error(`❌ Error fetching mythic cost for contentId ${contentId}:`, error)
      setMythicCost(null)
    } finally {
      setMythicCostLoading(false)
    }
  }

  // Function to fetch last discount data
  const fetchLastDiscount = async (itemId: number) => {
    try {

      const response = await fetch(`/api/skin-sales/${itemId}`)

      if (!response.ok) {
        console.warn(`⚠️ Failed to fetch sales data for item ID ${itemId}: HTTP ${response.status}`)
        setLastDiscount('N/A')
        return
      }

      const salesData = await response.json()

      if (!salesData.success || !salesData.data) {
        setLastDiscount('N/A')
        return
      }

      const { cost, beforeSale, currency, percentOff, endDate } = salesData.data

      // Format the discount string
      // Example: "540 RP ~~1350~~ - 60% off - June 23, 2025"
      const formattedEndDate = new Date(endDate).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })

      const discountText = `${cost} ~~${beforeSale}~~ — ${formattedEndDate}`
      setLastDiscount(discountText)

    } catch (error) {
      console.error(`❌ Error fetching last discount for item ID ${itemId}:`, error)
      setLastDiscount('N/A')
    }
  }

  // Helper function to construct proper Community Dragon chroma URL
  const constructChromaImageUrl = (chromaPath: string): string => {
    if (!chromaPath) return "/placeholder.svg"

    try {
      // Extract the path after "/v1/" and convert to lowercase
      const v1Index = chromaPath.indexOf('/v1/')
      if (v1Index !== -1) {
        const pathAfterV1 = chromaPath.substring(v1Index + 1) // +1 to remove the leading slash
        const lowercasePath = pathAfterV1.toLowerCase()

        // Construct the final Community Dragon URL
        const baseUrl = 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default'
        const finalUrl = `${baseUrl}/${lowercasePath}`

        return finalUrl
      }
    } catch (error) {
      console.error('Error constructing chroma URL:', error)
    }

    return "/placeholder.svg"
  }

  // Function to fetch chromas for the specific skin
  const fetchChromasForSkin = async (skinData: ProcessedSkinData) => {
    try {
      // First, check if skin has chromas using the all chromas API
      const chromaResponse = await fetch('/api/chromas/all')
      if (!chromaResponse.ok) {
        console.warn('Failed to fetch chromas data')
        setHasActualChromas(false)
        setChromaData([])
        return
      }

      const chromaResult = await chromaResponse.json()
      if (!chromaResult.success) {
        console.warn('Chromas API returned error')
        setHasActualChromas(false)
        setChromaData([])
        return
      }

      const allChromas: ProcessedChromaData[] = chromaResult.data
      const skinChromas = allChromas.filter((chroma: ProcessedChromaData) =>
        chroma.skinName === skinData.name
      )

      if (skinChromas.length > 0) {
        setHasActualChromas(true)

        // Convert ProcessedChromaData to ChromaData format with proper URL construction
        const convertedChromas: ChromaData[] = skinChromas.map((chroma: ProcessedChromaData) => ({
          id: chroma.id.toString(),
          name: chroma.name,
          color: chroma.color,
          colors: chroma.colors,
          chromaPath: constructChromaImageUrl(chroma.chromaPath),
          skinId: chroma.skinId,
          isDefault: chroma.isDefault
        }))

        // Following champion page logic - no default chroma in dropdown, just actual chromas
        setChromaData(convertedChromas.filter(c => !c.isDefault))
      } else {
        setHasActualChromas(false)
        setChromaData([])
      }

    } catch (error) {
      console.error('Error fetching chromas for skin:', error)
      setHasActualChromas(false)
      setChromaData([])
    }
  }

  // Handle chroma selection - Following champion page logic
  const handleChromaSelect = (chromaId: string) => {
    // If clicking on already selected chroma and already in chroma view, do nothing
    if (selectedChromaId === chromaId && !showSplashArt) {
      return
    }

    // Select the chroma (even if it's the same one)
    setSelectedChromaId(chromaId)

    // Auto-switch to In-Game mode when selecting a chroma (always switch when selecting any chroma)
    setShowSplashArt(false)
    setUserPrefersInGame(true)
  }

  // Handle view mode toggle - Auto-select first chroma when switching to chroma mode
  const handleViewModeToggle = () => {
    if (!canShowInGame) return

    const newShowSplashArt = !showSplashArt
    setShowSplashArt(newShowSplashArt)

    // Remember user preference for in-game mode
    setUserPrefersInGame(!newShowSplashArt)

    // When switching to chroma mode, automatically select the first chroma
    if (newShowSplashArt === false && chromaData.length > 0 && !selectedChromaId) {
      setSelectedChromaId(chromaData[0].id)
    }
  }

  // Check if in-game mode is available (only when chromas exist)
  const canShowInGame = chromaData.length > 0

  // Handle chroma toggle - Following champion page logic
  const handleChromaToggle = () => {
    if (!chromaData.length || chromasAnimating) return

    setChromasAnimating(true)
    setShowChromas(!showChromas)

    // Reset animation state after duration
    setTimeout(() => {
      setChromasAnimating(false)
    }, 300)
  }

  // Quest tier functions
  const handleQuestTierToggle = () => {
    if (!skin?.questSkinInfo?.tiers?.length || questTiersAnimating) return

    setQuestTiersAnimating(true)
    setShowQuestTiers(!showQuestTiers)

    // Reset animation state after duration
    setTimeout(() => {
      setQuestTiersAnimating(false)
    }, 300)
  }

  const handleTierSelect = (tier: CommunityDragonSkinTier) => {
    setSelectedTier(tier)
    setShowQuestTiers(false)
  }

  // Generate video URL from tier data
  const generateTierVideoUrl = (tier: CommunityDragonSkinTier): string | null => {
    // Only use collectionSplashVideoPath for video content
    if (!tier.collectionSplashVideoPath) return null

    // Extract path after /ASSETS/ and convert to lowercase
    const assetsIndex = tier.collectionSplashVideoPath.indexOf('/ASSETS/')
    if (assetsIndex === -1) return null

    const pathAfterAssets = tier.collectionSplashVideoPath.substring(assetsIndex)
    return `https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default${pathAfterAssets.toLowerCase()}`
  }

  // Generate image URL from tier data (fallback when no video)
  const generateTierImageUrl = (tier: CommunityDragonSkinTier): string | null => {
    // Use uncenteredSplashPath for image content
    if (!tier.uncenteredSplashPath) return null

    // Extract path after /ASSETS/ and convert to lowercase
    const assetsIndex = tier.uncenteredSplashPath.indexOf('/ASSETS/')
    if (assetsIndex === -1) return null

    const pathAfterAssets = tier.uncenteredSplashPath.substring(assetsIndex)
    return `https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default${pathAfterAssets.toLowerCase()}`
  }

  // Check if selected tier has video content
  const selectedTierHasVideo = (): boolean => {
    if (!selectedTier) return false
    return !!selectedTier.collectionSplashVideoPath
  }

  // Get current video URL based on selected tier
  const getCurrentVideoUrl = () => {
    if (!skin) return null

    // If a tier is selected, use its video
    if (selectedTier) {
      return generateTierVideoUrl(selectedTier)
    }

    // Fallback to original skin video
    return skin.videoUrl || null
  }

  // Get current page title based on selected tier
  const getCurrentTitle = () => {
    if (!skin) return ""

    // If a tier is selected, use its name
    if (selectedTier) {
      return selectedTier.name
    }

    // Fallback to original skin name
    return skin.name
  }

  // Get current image URL based on mode and chroma selection - Following champion page logic
  const getCurrentImageUrl = () => {
    if (!skin) return "/placeholder.svg"

    // If in splash art mode or in-game not available, show splash art
    if (showSplashArt || !canShowInGame) {
      return skin.splashArt || skin.image || "/placeholder.svg"
    }

    // In-Game mode - show selected chroma or default skin image
    if (selectedChromaId) {
      const selectedChroma = chromaData.find(c => c.id === selectedChromaId)
      if (selectedChroma && selectedChroma.chromaPath) {
        return selectedChroma.chromaPath
      }
    }

    // Fallback to default skin image for in-game mode
    return skin.image || skin.splashArt || "/placeholder.svg"
  }

  useEffect(() => {
    const resolveParams = async () => {
      const resolvedParams = await params
      const slugValue = resolvedParams.slug
      setSlug(slugValue)

      // Check if this is a rarity redirect
      const rarityMap: Record<string, string> = {
        'legacy': '/skins?legacy=legacy',
        'regular': '/skins?tier=Regular',
        'epic': '/skins?tier=Epic',
        'legendary': '/skins?tier=Legendary',
        'ultimate': '/skins?tier=Ultimate',
        'mythic': '/skins?tier=Mythic',
        'exalted': '/skins?tier=Exalted',
        'transcendent': '/skins?tier=Transcendent'
      }

      if (rarityMap[slugValue.toLowerCase()]) {
        // This is a rarity redirect
        setIsRarityRedirect(true)
        const redirectUrl = rarityMap[slugValue.toLowerCase()]
        router.replace(redirectUrl)
        return
      }

      // This is a skin page, fetch skin data
      try {
        setLoading(true)
        setError(null)

        // Fetch all skins to find the matching one
        const response = await fetch('/api/skins/all')
        if (!response.ok) {
          throw new Error('Failed to fetch skins data')
        }

        const result = await response.json()
        if (!result.success) {
          throw new Error(result.error || 'Failed to fetch skins data')
        }

        const allSkins: ProcessedSkinData[] = result.data

        // Find the skin that matches the slug
        // Handle upcoming skins with "-soon" suffix
        let foundSkin = allSkins.find(s => createSkinSlug(s.name) === slugValue)

        // If not found, check for upcoming skins with "-soon" suffix
        if (!foundSkin && slugValue.endsWith('-soon')) {
          const baseSlug = slugValue.replace('-soon', '')
          foundSkin = allSkins.find(s => createSkinSlug(s.name) === baseSlug && s.isUpcoming)
        }

        if (!foundSkin) {
          throw new Error('Skin not found')
        }

        setSkin(foundSkin)

        // Fetch last discount data if itemId is available
        if (foundSkin.itemId) {
          fetchLastDiscount(foundSkin.itemId)
        } else {
          setLastDiscount('N/A')
        }

        // Fetch mythic cost data if contentId is available (for Mythic skins only)
        if (foundSkin.contentId && foundSkin.tier === 'Mythic') {
          fetchMythicCost(foundSkin.contentId)
        }

        // Find related skins (same champion)
        const championSkins = allSkins
          .filter(s => s.champion === foundSkin.champion && s.id !== foundSkin.id)
          .slice(0, 6) // Limit to 6 related skins

        setRelatedSkins(championSkins)

        // Fetch chromas for this specific skin
        await fetchChromasForSkin(foundSkin)

      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
        setError(errorMessage)
        console.error('❌ Failed to fetch skin data:', errorMessage)
      } finally {
        setLoading(false)
      }
    }

    resolveParams()
  }, [params, router])

  // Smart mode switching with user preference memory - Following champion page logic
  useEffect(() => {
    if (!skin) return

    if (!canShowInGame) {
      // Force splash art when in-game not available (no chromas)
      setShowSplashArt(true)
    } else if (userPrefersInGame) {
      // Auto-switch back to in-game when available and user prefers it
      setShowSplashArt(false)
    }
  }, [skin, canShowInGame, userPrefersInGame])

  // Reset chroma selection when skin changes - Following champion page logic
  useEffect(() => {
    setSelectedChromaId(null) // No chroma selected initially
    setShowChromas(false)
  }, [skin])

  // Initialize quest tier selection to first tier when skin loads
  useEffect(() => {
    if (skin?.questSkinInfo?.tiers?.length) {
      // Set to first tier (tier one) by default
      setSelectedTier(skin.questSkinInfo.tiers[0])
    } else {
      // Reset if no quest tiers
      setSelectedTier(null)
    }
  }, [skin])

  // Close chromas panel when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const isOutsideDesktop = chromasRefDesktop.current && !chromasRefDesktop.current.contains(event.target as Node)
      const isOutsideMobile = chromasRefMobile.current && !chromasRefMobile.current.contains(event.target as Node)

      if (isOutsideDesktop && isOutsideMobile) {
        setShowChromas(false)
      }
    }

    if (showChromas) {
      document.addEventListener('mousedown', handleClickOutside)
      document.addEventListener('click', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
      document.removeEventListener('click', handleClickOutside)
    }
  }, [showChromas])

  // Close quest tiers panel when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const isOutsideDesktop = questTiersRefDesktop.current && !questTiersRefDesktop.current.contains(event.target as Node)
      const isOutsideMobile = questTiersRefMobile.current && !questTiersRefMobile.current.contains(event.target as Node)

      if (isOutsideDesktop && isOutsideMobile) {
        setShowQuestTiers(false)
      }
    }

    if (showQuestTiers) {
      document.addEventListener('mousedown', handleClickOutside)
      document.addEventListener('click', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
      document.removeEventListener('click', handleClickOutside)
    }
  }, [showQuestTiers])

  // Show loading while redirecting for rarity pages
  if (isRarityRedirect) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-400 mx-auto mb-4"></div>
          <p className="text-gray-300">Redirecting...</p>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <SharedLayout>
        <SkinDetailSkeleton />
      </SharedLayout>
    )
  }

  if (error || !skin) {
    return (
      <SharedLayout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="relative mx-auto mb-4 w-32 h-32">
              <Image
                src="https://raw.communitydragon.org/latest/game/assets/loadouts/summoneremotes/champions/amumu/amumu_sad_crying_glow.png"
                alt="Failed to load glow"
                width={128}
                height={128}
                className="absolute inset-0 opacity-60"
                unoptimized
              />
              <Image
                src="https://raw.communitydragon.org/latest/game/assets/loadouts/summoneremotes/champions/amumu/amumu_sad_crying_vfx.png"
                alt="Failed to load"
                width={128}
                height={128}
                className="relative z-10"
                unoptimized
              />
            </div>
            <h1 className="text-2xl font-bold text-white mb-4">Skin Not Found</h1>
            <p className="text-gray-400 mb-6">{error || 'The requested skin could not be found.'}</p>
            <Link href="/skins">
              <Button variant="outline" className="border-gray-700/20 text-gray-300">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Skins
              </Button>
            </Link>
          </div>
        </div>
      </SharedLayout>
    )
  }

  const rarityStyle = getRarityStyle(skin.tier, skin.isLegacy)

  // Get the skin's splash art for background (prefer splashArt over image, never use video)
  const backgroundImage = skin.splashArt || skin.image || "/placeholder.svg"

  return (
    <SkinBackground
      customImageUrl={backgroundImage}
      blur={10}
      brightness={0.25}
    >
      <SharedLayout>
        <div className="container mx-auto px-8 py-2 pb-8">


        {/* Title Section - Always First */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-white mb-2">{getCurrentTitle()}</h1>

          {/* Split into two grids: Tags on left, Controls on right */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-6">
            {/* Left Grid: Tags */}
            <div className="flex flex-wrap items-center gap-2">
              <Badge
                variant="secondary"
                className="text-xs px-2 py-1"
                style={rarityStyle}
              >
                {skin.isLegacy ? 'Legacy' : skin.tier}
              </Badge>

              {/* Feature Tags - Always show with Yes/No/N/A status */}
              <Badge variant="secondary" className={`text-xs px-2 py-1 ${
                skin.hasNewEffects === true ? 'bg-violet-600/20 text-violet-300 border-violet-600/30' :
                skin.hasNewEffects === false ? 'bg-gray-600/20 text-gray-400 border-gray-600/30' :
                'bg-gray-600/20 text-gray-400 border-gray-600/30'
              }`}>
                New Effects: {skin.hasNewEffects === true ? 'Yes' : skin.hasNewEffects === false ? 'No' : 'N/A'}
              </Badge>
              <Badge variant="secondary" className={`text-xs px-2 py-1 ${
                skin.hasNewAnimations === true ? 'bg-violet-600/20 text-violet-300 border-violet-600/30' :
                skin.hasNewAnimations === false ? 'bg-gray-600/20 text-gray-400 border-gray-600/30' :
                'bg-gray-600/20 text-gray-400 border-gray-600/30'
              }`}>
                New Animations: {skin.hasNewAnimations === true ? 'Yes' : skin.hasNewAnimations === false ? 'No' : 'N/A'}
              </Badge>
              <Badge variant="secondary" className={`text-xs px-2 py-1 ${
                skin.hasNewRecall === true ? 'bg-violet-600/20 text-violet-300 border-violet-600/30' :
                skin.hasNewRecall === false ? 'bg-gray-600/20 text-gray-400 border-gray-600/30' :
                'bg-gray-600/20 text-gray-400 border-gray-600/30'
              }`}>
                New Recall: {skin.hasNewRecall === true ? 'Yes' : skin.hasNewRecall === false ? 'No' : 'N/A'}
              </Badge>
              <Badge variant="secondary" className={`text-xs px-2 py-1 ${
                skin.newVoiceLines === true ? 'bg-violet-600/20 text-violet-300 border-violet-600/30' :
                skin.newVoiceLines === false ? 'bg-gray-600/20 text-gray-400 border-gray-600/30' :
                'bg-gray-600/20 text-gray-400 border-gray-600/30'
              }`}>
                New Voice Lines: {skin.newVoiceLines === true ? 'Yes' : skin.newVoiceLines === false ? 'No' : 'N/A'}
              </Badge>
            </div>

            {/* Right Grid: Controls (Desktop only) */}
            <div className="hidden lg:flex justify-between items-center">
              {/* Left Side Controls */}
              <div className="flex items-center space-x-2">
                {/* Chroma Selector */}
                <div className="relative" ref={chromasRefDesktop}>
                <button
                  onClick={handleChromaToggle}
                  disabled={!chromaData.length || chromasAnimating}
                  className={`w-8 h-8 rounded-full border-2 flex items-center justify-center transition-all duration-300 ${
                    chromaData.length > 0
                      ? `border-purple-500 bg-purple-500/20 hover:bg-purple-500/30 cursor-pointer ${showChromas ? 'ring-2 ring-purple-400/50 scale-105' : ''}`
                      : 'border-gray-600 bg-gray-600/20 cursor-not-allowed opacity-50'
                  } ${chromasAnimating ? 'pointer-events-none' : ''}`}
                >
                  <Image
                    src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/icons/chroma-icon.png"
                    alt="Chromas"
                    width={16}
                    height={16}
                    className="w-4 h-4"
                    style={{ userSelect: 'none', pointerEvents: 'none' }}
                  />
                </button>

                {/* Horizontal Chromas Dropdown */}
                {showChromas && chromaData.length > 0 && (
                  <div className="absolute left-full top-0 ml-2 bg-black/90 backdrop-blur-md rounded-lg p-2 border border-purple-500/40 z-10 flex items-center h-8 animate-in slide-in-from-left-2 duration-300">
                    <div className="flex items-center space-x-2">
                      {chromaData.map((chroma, index) => {
                        // Determine if this is a single color or dual color chroma
                        const isSingleColor = chroma.colors && chroma.colors.length >= 2 && chroma.colors[0] === chroma.colors[1]
                        const isDualColor = chroma.colors && chroma.colors.length >= 2 && chroma.colors[0] !== chroma.colors[1]

                        return (
                          <button
                            key={chroma.id || index}
                            onClick={() => handleChromaSelect(chroma.id?.toString() || index.toString())}
                            className={`w-5 h-5 rounded-full border-2 transition-all ${
                              selectedChromaId === (chroma.id?.toString() || index.toString())
                                ? 'border-purple-500 scale-110'
                                : 'border-gray-500 hover:border-gray-400'
                            }`}
                            style={{
                              backgroundColor: isSingleColor ? chroma.colors?.[0] : undefined,
                              background: isDualColor
                                ? `linear-gradient(45deg, ${chroma.colors?.[0]} 50%, ${chroma.colors?.[1]} 50%)`
                                : isSingleColor
                                ? chroma.colors?.[0]
                                : '#888888', // fallback color
                              userSelect: 'none'
                            }}
                          />
                        )
                      })}
                    </div>
                  </div>
                )}
                </div>

                {/* Quest Skin Button */}
                {skin.questSkinInfo && skin.questSkinInfo.tiers && (
                  <div className="relative" ref={questTiersRefDesktop}>
                    <button
                      onClick={handleQuestTierToggle}
                      disabled={!skin.questSkinInfo.tiers.length || questTiersAnimating}
                      className={`w-8 h-8 rounded-full border-2 flex items-center justify-center transition-all duration-300 ${
                        skin.questSkinInfo.tiers.length > 0
                          ? `border-purple-600 bg-purple-600/20 hover:bg-purple-600/30 cursor-pointer ${showQuestTiers ? 'ring-2 ring-purple-400/50 scale-105' : ''}`
                          : 'border-gray-600 bg-gray-600/20 cursor-not-allowed opacity-50'
                      } ${questTiersAnimating ? 'pointer-events-none' : ''}`}
                      title="Quest Skin Tiers"
                    >
                      <Image
                        src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/skin-viewer/icon-three-masks-default.svg"
                        alt="Quest Skin"
                        width={16}
                        height={16}
                        className="w-4 h-4"
                        style={{ userSelect: 'none', pointerEvents: 'none' }}
                      />
                    </button>

                    {/* Quest Tiers Dropdown */}
                    {showQuestTiers && skin.questSkinInfo.tiers.length > 0 && (
                      <div className="absolute left-full top-0 ml-2 bg-black/90 backdrop-blur-md rounded-lg p-2 border border-purple-500/40 z-10 min-w-48 animate-in slide-in-from-left-2 duration-300">
                        <div className="space-y-1">
                          {skin.questSkinInfo.tiers.map((tier) => (
                            <button
                              key={tier.id}
                              onClick={() => handleTierSelect(tier)}
                              className={`w-full text-left px-3 py-2 rounded text-sm transition-colors duration-200 ${
                                selectedTier?.id === tier.id
                                  ? 'bg-purple-600/30 text-purple-300'
                                  : 'text-gray-300 hover:bg-purple-600/20 hover:text-purple-300'
                              }`}
                            >
                              {tier.name}
                            </button>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* View Mode Toggle (Right Side) */}
              <div className="flex items-center space-x-3">
                <span className={`text-sm ${
                  !canShowInGame ? 'text-gray-400' : (!showSplashArt ? 'text-purple-400' : 'text-gray-300')
                }`}>
                  Chroma
                </span>
                <button
                  onClick={handleViewModeToggle}
                  disabled={!canShowInGame}
                  className={`relative w-12 h-6 rounded-full transition-all duration-300 ease-in-out ${
                    canShowInGame
                      ? (!showSplashArt ? 'bg-purple-600' : 'bg-gray-600')
                      : 'bg-gray-600 opacity-50 cursor-not-allowed'
                  }`}
                >
                  <div
                    className={`absolute top-0.5 left-0.5 w-5 h-5 bg-white rounded-full transition-all duration-300 ease-in-out ${
                      !showSplashArt ? 'translate-x-0' : ''
                    }`}
                    style={{
                      transform: !showSplashArt ? 'translateX(0)' : 'translateX(22px)'
                    }}
                  />
                </button>
                <span className="text-sm text-gray-300">Splash Art</span>
              </div>
            </div>
          </div>
        </div>

        {/* Mobile: Image Section - Second */}
        <div className="lg:hidden mb-6">
          {/* Controls above the skin image */}
          <div className="flex justify-between items-center mb-4">
            {/* Left Side Controls */}
            <div className="flex items-center space-x-2">
              {/* Chroma Selector */}
              <div className="relative" ref={chromasRefMobile}>
              <button
                onClick={handleChromaToggle}
                disabled={!chromaData.length || chromasAnimating}
                className={`w-8 h-8 rounded-full border-2 flex items-center justify-center transition-all duration-300 ${
                  chromaData.length > 0
                    ? `border-purple-500 bg-purple-500/20 hover:bg-purple-500/30 cursor-pointer ${showChromas ? 'ring-2 ring-purple-400/50 scale-105' : ''}`
                    : 'border-gray-600 bg-gray-600/20 cursor-not-allowed opacity-50'
                } ${chromasAnimating ? 'pointer-events-none' : ''}`}
              >
                <Image
                  src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/icons/chroma-icon.png"
                  alt="Chromas"
                  width={16}
                  height={16}
                  className="w-4 h-4"
                  style={{ userSelect: 'none', pointerEvents: 'none' }}
                />
              </button>

              {/* Horizontal Chromas Dropdown */}
              {showChromas && chromaData.length > 0 && (
                <div className="absolute left-full top-0 ml-2 bg-black/90 backdrop-blur-md rounded-lg p-2 border border-purple-500/40 z-10 flex items-center h-8 animate-in slide-in-from-left-2 duration-300">
                  <div className="flex items-center space-x-2">
                    {chromaData.map((chroma, index) => {
                      // Determine if this is a single color or dual color chroma
                      const isSingleColor = chroma.colors && chroma.colors.length >= 2 && chroma.colors[0] === chroma.colors[1]
                      const isDualColor = chroma.colors && chroma.colors.length >= 2 && chroma.colors[0] !== chroma.colors[1]

                      return (
                        <button
                          key={chroma.id || index}
                          onClick={() => handleChromaSelect(chroma.id?.toString() || index.toString())}
                          className={`w-5 h-5 rounded-full border-2 transition-all ${
                            selectedChromaId === (chroma.id?.toString() || index.toString())
                              ? 'border-purple-500 scale-110'
                              : 'border-gray-500 hover:border-gray-400'
                          }`}
                          style={{
                            backgroundColor: isSingleColor ? chroma.colors?.[0] : undefined,
                            background: isDualColor
                              ? `linear-gradient(45deg, ${chroma.colors?.[0]} 50%, ${chroma.colors?.[1]} 50%)`
                              : isSingleColor
                              ? chroma.colors?.[0]
                              : '#888888', // fallback color
                            userSelect: 'none'
                          }}
                        />
                      )
                    })}
                  </div>
                </div>
              )}
              </div>

              {/* Quest Skin Button */}
              {skin.questSkinInfo && skin.questSkinInfo.tiers && (
                <div className="relative" ref={questTiersRefMobile}>
                  <button
                    onClick={handleQuestTierToggle}
                    disabled={!skin.questSkinInfo.tiers.length || questTiersAnimating}
                    className={`w-8 h-8 rounded-full border-2 flex items-center justify-center transition-all duration-300 ${
                      skin.questSkinInfo.tiers.length > 0
                        ? `border-purple-600 bg-purple-600/20 hover:bg-purple-600/30 cursor-pointer ${showQuestTiers ? 'ring-2 ring-purple-400/50 scale-105' : ''}`
                        : 'border-gray-600 bg-gray-600/20 cursor-not-allowed opacity-50'
                    } ${questTiersAnimating ? 'pointer-events-none' : ''}`}
                    title="Quest Skin Tiers"
                  >
                    <Image
                      src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/skin-viewer/icon-three-masks-default.svg"
                      alt="Quest Skin"
                      width={16}
                      height={16}
                      className="w-4 h-4"
                      style={{ userSelect: 'none', pointerEvents: 'none' }}
                    />
                  </button>

                  {/* Quest Tiers Dropdown */}
                  {showQuestTiers && skin.questSkinInfo.tiers.length > 0 && (
                    <div className="absolute left-full top-0 ml-2 bg-black/90 backdrop-blur-md rounded-lg p-2 border border-purple-500/40 z-10 min-w-48 animate-in slide-in-from-left-2 duration-300">
                      <div className="space-y-1">
                        {skin.questSkinInfo.tiers.map((tier) => (
                          <button
                            key={tier.id}
                            onClick={() => handleTierSelect(tier)}
                            className={`w-full text-left px-3 py-2 rounded text-sm transition-colors duration-200 ${
                              selectedTier?.id === tier.id
                                ? 'bg-purple-600/30 text-purple-300'
                                : 'text-gray-300 hover:bg-purple-600/20 hover:text-purple-300'
                            }`}
                          >
                            {tier.name}
                          </button>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* View Mode Toggle (Right Side) - Custom colors matching View Champion Page button */}
            <div className="flex items-center space-x-3">
              <span className={`text-sm ${
                !canShowInGame ? 'text-gray-400' : (!showSplashArt ? 'text-purple-400' : 'text-gray-300')
              }`}>
                Chroma
              </span>
              <button
                onClick={handleViewModeToggle}
                disabled={!canShowInGame}
                className={`relative w-12 h-6 rounded-full transition-all duration-300 ease-in-out ${
                  canShowInGame
                    ? (!showSplashArt ? 'bg-purple-600' : 'bg-gray-600')
                    : 'bg-gray-600 opacity-50 cursor-not-allowed'
                }`}
              >
                <div
                  className={`absolute top-0.5 left-0.5 w-5 h-5 bg-white rounded-full transition-all duration-300 ease-in-out ${
                    !showSplashArt ? 'translate-x-0' : ''
                  }`}
                  style={{
                    transform: !showSplashArt ? 'translateX(0)' : 'translateX(22px)'
                  }}
                />
              </button>
              <span className="text-sm text-gray-300">Splash Art</span>
            </div>
          </div>

          <Card className="bg-gray-900/40 border-purple-700/20 overflow-hidden">
            <CardContent className="p-0">
              <div className="aspect-video relative">
                {/* Show video for high-tier skins or quest skins when in splash art mode and no chroma selected */}
                {((['Exalted', 'Ultimate', 'Transcendent'].includes(skin.tier) || (skin.questSkinInfo && selectedTierHasVideo())) &&
                  getCurrentVideoUrl() &&
                  showSplashArt &&
                  (!selectedChromaId || selectedChromaId === 'default')) ? (
                  <video
                    src={getCurrentVideoUrl()!}
                    autoPlay
                    loop
                    muted
                    playsInline
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      console.error('❌ Video failed to load for:', getCurrentTitle(), 'URL:', getCurrentVideoUrl())
                      // Fallback to image if video fails to load
                      const target = e.target as HTMLVideoElement
                      const parent = target.parentElement
                      if (parent) {
                        parent.innerHTML = `
                          <img
                            src="${getCurrentImageUrl()}"
                            alt="${getCurrentTitle()}"
                            class="w-full h-full object-cover"
                            oncontextmenu="return false"
                            draggable="false"
                          />
                        `
                      }
                    }}
                  />
                ) : (selectedTier && !selectedTierHasVideo() && generateTierImageUrl(selectedTier) && showSplashArt && (!selectedChromaId || selectedChromaId === 'default')) ? (
                  /* Show tier image when no video is available */
                  <Image
                    src={generateTierImageUrl(selectedTier)!}
                    alt={getCurrentTitle()}
                    fill
                    className="object-cover"
                    onContextMenu={(e) => e.preventDefault()}
                    draggable={false}
                  />
                ) : showSplashArt ? (
                  <Image
                    key={`mobile-splash-${showSplashArt}-${selectedChromaId || 'none'}`}
                    src={getCurrentImageUrl()}
                    alt={skin.name}
                    fill
                    className="object-cover"
                    priority
                    onContextMenu={(e) => e.preventDefault()}
                    draggable={false}
                  />
                ) : (
                  <div className="relative w-full h-full">
                    <Image
                      src="https://i.ibb.co/z91Cdg3/image.png"
                      alt="In-game background"
                      fill
                      className="object-cover"
                      style={{ userSelect: 'none', pointerEvents: 'none' }}
                      draggable={false}
                    />
                    <div className="absolute inset-0 flex items-center justify-center">
                      <Image
                        key={`mobile-chroma-${showSplashArt}-${selectedChromaId || 'none'}`}
                        src={getCurrentImageUrl()}
                        alt={skin.name}
                        width={300}
                        height={300}
                        className="max-w-[75%] max-h-[75%] object-contain"
                        style={{ userSelect: 'none', pointerEvents: 'none' }}
                        draggable={false}
                      />
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons - Under the skin image */}
          <div className="space-y-3 mt-4">
            <Button
              className={`w-full ${
                skin.isUpcoming
                  ? 'bg-gray-600 text-gray-400 cursor-not-allowed opacity-50'
                  : 'bg-purple-600 hover:bg-purple-700 text-white'
              }`}
              disabled={skin.isUpcoming}
              onClick={async () => {
                if (!skin.isUpcoming) {
                  const championSlug = await getChampionSlug(skin.champion)
                  router.push(`/champions/${championSlug}`)
                }
              }}
            >
              <ExternalLink className="h-4 w-4 mr-2" />
              View Champion Page
            </Button>
            <Button variant="outline" className="w-full border-gray-700/20 text-gray-500 cursor-not-allowed opacity-50" disabled>
              <Bell className="h-4 w-4 mr-2" />
              Add to Watchlist (Coming Soon)
            </Button>
          </div>
        </div>

        {/* Main Content - Desktop: 2-column layout (Skin Details - Splash Art), Mobile: Details Only */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-6 lg:mb-12">
          {/* Skin Information - Desktop: LEFT, Mobile: THIRD */}
          <div className="space-y-6">
            {/* Enhanced Skin Details */}

            {/* Enhanced Skin Details */}
            <Card className="bg-gray-900/40 border-purple-700/20">
              <CardContent className="p-6">
                <h3 className="text-xl font-semibold text-white mb-4">Skin Details</h3>

                {/* Countdown Timer for Upcoming Skins */}
                {skin.isUpcoming && skin.releaseCountdown && (
                  <div className="mb-6 p-4 bg-purple-900/20 border border-purple-700/30 rounded-lg">
                    <CountdownTimer
                      targetDate={skin.releaseCountdown}
                      className="w-full"
                      showIcon={true}
                      compact={false}
                    />
                  </div>
                )}

                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Rarity:</span>
                    <div className="flex items-center space-x-2">
                      <SkinRarityIcons
                        rarity={skin.tier}
                        isLegacy={skin.isLegacy}
                        isBase={skin.isBase}
                        size={20}
                        showTooltip={false}
                      />
                      <span className="text-white">{skin.tier}</span>
                    </div>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">
                      {skin.tier === 'Mythic' && mythicCost ? 'Last Recorded Price:' : 'Cost:'}
                    </span>
                    {skin.tier === 'Mythic' && mythicCostLoading ? (
                      <span className="animate-pulse text-gray-400">Loading...</span>
                    ) : skin.tier === 'Mythic' && mythicCost ? (
                      <div className="flex items-center space-x-1 justify-end">
                        <Image
                          src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/mythic.svg"
                          alt="ME"
                          width={16}
                          height={16}
                          className="w-4 h-4"
                          style={{ userSelect: 'none', pointerEvents: 'none' }}
                          draggable={false}
                        />
                        <span className="font-medium" style={{ color: '#b47bdf' }}>
                          {mythicCost.cost}
                        </span>
                        <span className="text-white">
                          — {new Date(mythicCost.endTime).toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric'
                          })}
                        </span>
                      </div>
                    ) : skin.price === 'N/A' ? (
                      <span className="text-gray-400 font-medium">N/A</span>
                    ) : skin.price === 'Free' ? (
                      <span className="text-green-400 font-medium">Free</span>
                    ) : (
                      <div className="flex items-center space-x-1">
                        {skin.price.includes('ME') ? (
                          <Image
                            src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/mythic.svg"
                            alt="ME"
                            width={16}
                            height={16}
                            className="w-4 h-4"
                            style={{ userSelect: 'none', pointerEvents: 'none' }}
                            draggable={false}
                          />
                        ) : skin.price.includes('AS') ? (
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <div className="flex items-center space-x-1 cursor-help">
                                <Image
                                  src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/currency/icons/ancient_spark.svg"
                                  alt="AS"
                                  width={16}
                                  height={16}
                                  className="w-4 h-4"
                                  style={{ userSelect: 'none', pointerEvents: 'none' }}
                                  draggable={false}
                                />
                                <span
                                  className="font-medium"
                                  style={{
                                    color: skin.price.includes('ME') ? '#b47bdf' :
                                           skin.price.includes('AS') ? '#2babf0' : '#fb923c'
                                  }}
                                >
                                  {skin.price.replace(' RP', '').replace(' ME', '').replace(' AS', '').replace(' BE', '')}
                                </span>
                              </div>
                            </TooltipTrigger>
                            <TooltipContent>
                              <div className="flex items-center space-x-2 text-sm">
                                <span className="font-medium">1</span>
                                <Image
                                  src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/currency/icons/ancient_spark.svg"
                                  alt="AS"
                                  width={16}
                                  height={16}
                                  className="w-4 h-4"
                                />
                                <span className="font-medium">= 400</span>
                                <Image
                                  src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg"
                                  alt="RP"
                                  width={16}
                                  height={16}
                                  className="w-4 h-4"
                                />
                              </div>
                            </TooltipContent>
                          </Tooltip>
                        ) : skin.price.includes('BE') ? (
                          <>
                            <Image
                              src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-navigation/global/default/rewardicons_blueessence.png"
                              alt="BE"
                              width={16}
                              height={16}
                              className="w-4 h-4"
                              style={{ userSelect: 'none', pointerEvents: 'none' }}
                              draggable={false}
                            />
                            <span
                              className="font-medium"
                              style={{ color: '#38c4dc' }}
                            >
                              {skin.price.replace(' RP', '').replace(' ME', '').replace(' AS', '').replace(' BE', '')}
                            </span>
                          </>
                        ) : (
                          <>
                            <Image
                              src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg"
                              alt="RP"
                              width={16}
                              height={16}
                              className="w-4 h-4"
                              style={{ userSelect: 'none', pointerEvents: 'none' }}
                              draggable={false}
                            />
                            <span
                              className="font-medium"
                              style={{
                                color: skin.price.includes('ME') ? '#b47bdf' :
                                       skin.price.includes('AS') ? '#6f55dd' : '#fb923c'
                              }}
                            >
                              {skin.price.replace(' RP', '').replace(' ME', '').replace(' AS', '').replace(' BE', '')}
                            </span>
                          </>
                        )}
                      </div>
                    )}
                  </div>
                  {skin.set && (
                    <div className="flex justify-between">
                      <span className="text-gray-400">Skin Set:</span>
                      <button
                        onClick={() => {
                          router.push(`/skins?skinLines=${encodeURIComponent(skin.set || '')}`)
                        }}
                        className="text-white hover:text-purple-300 transition-colors flex items-center gap-1 group"
                      >
                        <span>{skin.set}</span>
                        <ExternalLink className="h-3 w-3 text-gray-400 group-hover:text-purple-300 transition-colors" />
                      </button>
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span className="text-gray-400">Chromas:</span>
                    {hasActualChromas ? (
                      <button
                        onClick={() => {
                          // Navigate to chromas page with skin name filter
                          const encodedSkinName = encodeURIComponent(skin.name)
                          router.push(`/skins/chromas?search=${encodedSkinName}`)
                        }}
                        className="text-white hover:text-purple-300 transition-colors flex items-center gap-1 group"
                      >
                        <span>Yes — ({chromaData.length})</span>
                        <ExternalLink className="h-3 w-3 text-gray-400 group-hover:text-purple-300 transition-colors" />
                      </button>
                    ) : (
                      <span className="text-white">No</span>
                    )}
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Release Date:</span>
                    <span className="text-gray-400">{skin.releaseDate || 'N/A'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Availability:</span>
                    <span className={`font-medium ${
                      skin.availability === 'Available' ? 'text-green-400' :
                      skin.availability === 'Legacy' ? 'text-amber-400' :
                      skin.availability === null ? 'text-gray-400' : 'text-red-400'
                    }`}>
                      {skin.availability || 'N/A'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Loot Eligible:</span>
                    <span className={`font-medium ${
                      skin.lootEligible === true ? 'text-green-400' :
                      skin.lootEligible === false ? 'text-red-400' : 'text-gray-400'
                    }`}>
                      {skin.lootEligible === true ? 'Yes' : skin.lootEligible === false ? 'No' : 'N/A'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Last Discount:</span>
                    <div className="text-right max-w-xs">
                      {lastDiscount === 'Loading...' ? (
                        <span className="animate-pulse text-gray-400">Loading...</span>
                      ) : lastDiscount === 'N/A' ? (
                        <span className="text-gray-400">N/A</span>
                      ) : (
                        <div className="flex items-center space-x-1 justify-end">
                          <Image
                            src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg"
                            alt="RP"
                            width={16}
                            height={16}
                            className="w-4 h-4"
                            style={{ userSelect: 'none', pointerEvents: 'none' }}
                            draggable={false}
                          />
                          <span
                            className="text-sm"
                            dangerouslySetInnerHTML={{
                              __html: lastDiscount
                                .replace(/(\d+)\s/, '<span style="color: #fb923c; font-weight: 500;">$1</span> ')
                                .replace(/~~(.+?)~~/g, '<span style="text-decoration: line-through; color: #9ca3af; font-size: 12px;">$1</span>')
                                .replace(/—\s(.+)$/, '— <span style="color: white;">$1</span>')
                            }}
                          />
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>



          </div>

          {/* Desktop: Splash Art Section - RIGHT */}
          <div className="hidden lg:block space-y-4">
            {/* Skin Image */}
            <Card className="bg-gray-900/40 border-purple-700/20 overflow-hidden">
              <CardContent className="p-0">
                <div className="aspect-video relative">
                  {/* Show video for high-tier skins or quest skins when in splash art mode and no chroma selected */}
                  {((['Exalted', 'Ultimate', 'Transcendent'].includes(skin.tier) || (skin.questSkinInfo && selectedTierHasVideo())) &&
                    getCurrentVideoUrl() &&
                    showSplashArt &&
                    (!selectedChromaId || selectedChromaId === 'default')) ? (
                    <video
                      src={getCurrentVideoUrl()!}
                      autoPlay
                      loop
                      muted
                      playsInline
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        console.error('❌ Video failed to load for:', getCurrentTitle(), 'URL:', getCurrentVideoUrl())
                        // Fallback to image if video fails to load
                        const target = e.target as HTMLVideoElement
                        const parent = target.parentElement
                        if (parent) {
                          parent.innerHTML = `
                            <img
                              src="${getCurrentImageUrl()}"
                              alt="${getCurrentTitle()}"
                              class="w-full h-full object-cover"
                              oncontextmenu="return false"
                              draggable="false"
                            />
                          `
                        }
                      }}
                    />
                  ) : (selectedTier && !selectedTierHasVideo() && generateTierImageUrl(selectedTier) && showSplashArt && (!selectedChromaId || selectedChromaId === 'default')) ? (
                    /* Show tier image when no video is available */
                    <Image
                      src={generateTierImageUrl(selectedTier)!}
                      alt={getCurrentTitle()}
                      fill
                      className="object-cover"
                      onContextMenu={(e) => e.preventDefault()}
                      draggable={false}
                    />
                  ) : showSplashArt ? (
                    <Image
                      key={`desktop-splash-${getCurrentImageUrl()}`}
                      src={getCurrentImageUrl()}
                      alt={skin.name}
                      fill
                      className="object-cover"
                      priority
                      onContextMenu={(e) => e.preventDefault()}
                      draggable={false}
                    />
                  ) : (
                    <div className="relative w-full h-full">
                      <Image
                        src="https://i.ibb.co/z91Cdg3/image.png"
                        alt="In-game background"
                        fill
                        className="object-cover"
                        style={{ userSelect: 'none', pointerEvents: 'none' }}
                        draggable={false}
                      />
                      <div className="absolute inset-0 flex items-center justify-center">
                        <Image
                          key={`desktop-chroma-${getCurrentImageUrl()}`}
                          src={getCurrentImageUrl()}
                          alt={skin.name}
                          width={300}
                          height={300}
                          className="max-w-[75%] max-h-[75%] object-contain"
                          style={{ userSelect: 'none', pointerEvents: 'none' }}
                          draggable={false}
                        />
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Action Buttons - Under the splash art */}
            <div className="space-y-3">
              <Button
                className={`w-full ${
                  skin.isUpcoming
                    ? 'bg-gray-600 text-gray-400 cursor-not-allowed opacity-50'
                    : 'bg-purple-600 hover:bg-purple-700 text-white'
                }`}
                disabled={skin.isUpcoming}
                onClick={async () => {
                  if (!skin.isUpcoming) {
                    const championSlug = await getChampionSlug(skin.champion)
                    router.push(`/champions/${championSlug}`)
                  }
                }}
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                View Champion Page
              </Button>
              <Button variant="outline" className="w-full border-gray-700/20 text-gray-500 cursor-not-allowed opacity-50" disabled>
                <Bell className="h-4 w-4 mr-2" />
                Add to Watchlist (Coming Soon)
              </Button>
            </div>
          </div>

        </div>

        {/* Description - Full Width */}
        {skin.description && (
          <div className="mb-6 lg:mb-12">
            <Card className="bg-gray-900/40 border-purple-700/20">
              <CardContent className="p-6">
                <h3 className="text-xl font-semibold text-white mb-4">Description</h3>
                <p className="text-gray-300 leading-relaxed">{skin.description}</p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Related Skins */}
        {relatedSkins.length > 0 && (
          <div>
            <h2 className="text-2xl font-bold text-white mb-6">
              More {skin.champion} Skins
            </h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
              {relatedSkins.map((relatedSkin) => {
                const relatedRarityStyle = getRarityStyle(relatedSkin.tier, relatedSkin.isLegacy)

                return (
                  <Link
                    key={relatedSkin.id}
                    href={`/skins/${createSkinSlug(relatedSkin.name)}`}
                    className="block"
                  >
                    <div className="bg-gradient-to-br from-gray-900/60 to-gray-800/40 border border-purple-700/30 rounded-xl overflow-hidden relative group transition-all duration-500 transform hover:scale-105 cursor-pointer hover:border-purple-400/70 hover:shadow-lg hover:shadow-purple-500/20">
                      {/* Skin Image - Full card background */}
                      {relatedSkin.image && (
                        <div className="relative h-64 w-full">
                          {(['Exalted', 'Ultimate', 'Transcendent'].includes(relatedSkin.tier) && relatedSkin.videoUrl) ? (
                            <video
                              src={relatedSkin.videoUrl}
                              autoPlay
                              loop
                              muted
                              playsInline
                              className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700 ease-out"
                              onError={(e) => {
                                console.error('❌ Video failed to load for:', relatedSkin.name, 'URL:', relatedSkin.videoUrl)
                                // Fallback to image if video fails to load
                                const target = e.target as HTMLVideoElement
                                const parent = target.parentElement
                                if (parent) {
                                  parent.innerHTML = `
                                    <img
                                      src="${relatedSkin.image || "/placeholder.svg"}"
                                      alt="${relatedSkin.name}"
                                      class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700 ease-out"
                                      loading="lazy"
                                    />
                                  `
                                }
                              }}
                            />
                          ) : (
                            <Image
                              src={relatedSkin.image || "/placeholder.svg"}
                              alt={relatedSkin.name}
                              fill
                              className="object-cover group-hover:scale-110 transition-transform duration-700 ease-out"
                            />
                          )}
                          <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent opacity-60 group-hover:opacity-40 transition-opacity duration-300" />
                        </div>
                      )}

                      {/* Content overlay at bottom */}
                      <div className="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/90 via-black/70 to-transparent">
                        <p
                          className="text-white font-bold text-sm mb-2 drop-shadow-lg truncate"
                          title={relatedSkin.name}
                        >
                          {relatedSkin.name}
                        </p>

                        {/* Price and rarity row */}
                        <div className="flex items-center justify-between">
                          {/* Price on the left */}
                          <div className="flex items-center space-x-1">
                            {(() => {
                              // For mythic skins, show mythic price if available
                              if (relatedSkin.tier === 'Mythic') {
                                const mythicPrice = mythicPrices.get(relatedSkin.contentId)

                                if (mythicPrice) {
                                  return (
                                    <>
                                      <Image
                                        src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/mythic.svg"
                                        alt="ME"
                                        width={16}
                                        height={16}
                                        className="h-4 w-4"
                                      />
                                      <span className="font-semibold" style={{ color: '#b47bdf' }}>
                                        {mythicPrice.cost}
                                      </span>
                                    </>
                                  )
                                } else {
                                  return <span className="text-gray-400 font-semibold">N/A</span>
                                }
                              }

                              // For non-mythic skins, use existing logic
                              if (relatedSkin.price === 'N/A') {
                                return <span className="text-gray-400 font-semibold">N/A</span>
                              } else if (relatedSkin.price === 'Free') {
                                return <span className="text-green-400 font-semibold">Free</span>
                              } else if (relatedSkin.price.includes('AS')) {
                                return (
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <div className="flex items-center space-x-1 cursor-help">
                                        <Image
                                          src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/currency/icons/ancient_spark.svg"
                                          alt="AS"
                                          width={16}
                                          height={16}
                                          className="h-4 w-4"
                                          style={{ pointerEvents: 'none' }}
                                        />
                                        <span className="font-semibold" style={{ color: '#2babf0' }}>
                                          {relatedSkin.price.replace(' RP', '').replace(' ME', '').replace(' AS', '').replace(' BE', '')}
                                        </span>
                                      </div>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      <div className="flex items-center space-x-2 text-sm">
                                        <span className="font-medium">1</span>
                                        <Image
                                          src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/currency/icons/ancient_spark.svg"
                                          alt="AS"
                                          width={16}
                                          height={16}
                                          className="w-4 h-4"
                                        />
                                        <span className="font-medium">= 400</span>
                                        <Image
                                          src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg"
                                          alt="RP"
                                          width={16}
                                          height={16}
                                          className="w-4 h-4"
                                        />
                                      </div>
                                    </TooltipContent>
                                  </Tooltip>
                                )
                              } else if (relatedSkin.price.includes('BE')) {
                                return (
                                  <>
                                    <Image
                                      src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-navigation/global/default/rewardicons_blueessence.png"
                                      alt="BE"
                                      width={16}
                                      height={16}
                                      className="h-4 w-4"
                                    />
                                    <span className="font-semibold" style={{ color: '#38c4dc' }}>
                                      {relatedSkin.price.replace(' RP', '').replace(' ME', '').replace(' AS', '').replace(' BE', '')}
                                    </span>
                                  </>
                                )
                              } else {
                                return (
                                  <>
                                    <Image
                                      src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg"
                                      alt="RP"
                                      width={16}
                                      height={16}
                                      className="h-4 w-4"
                                    />
                                    <span className="font-semibold" style={{ color: '#fb923c' }}>
                                      {relatedSkin.price.replace(' RP', '').replace(' ME', '').replace(' AS', '').replace(' BE', '')}
                                    </span>
                                  </>
                                )
                              }
                            })()}
                          </div>

                          {/* Rarity icon on the right */}
                          <div className="flex items-center gap-1 flex-shrink-0">
                            <SkinRarityIconsSimple
                              rarity={relatedSkin.tier}
                              isLegacy={relatedSkin.isLegacy}
                              isBase={relatedSkin.isBase}
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </Link>
                )
              })}
            </div>
          </div>
        )}
      </div>
      </SharedLayout>
    </SkinBackground>
  )
}
