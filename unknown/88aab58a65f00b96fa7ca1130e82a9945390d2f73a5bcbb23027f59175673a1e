"use client"

import { useState, useEffect } from "react"
import SharedLayout from "@/components/shared-layout"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { SkinRarityIcons } from "@/components/ui/skin-rarity-icons"
import { Button } from "@/components/ui/button"

interface TestSkinData {
  skinId: number
  name: string
  isBase: boolean
  originalRarity: string
  normalizedRarity: string
  isLegacy: boolean
  rarityIconUrl: string | null
  legacyIconUrl: string | null
}

export default function TestSkinRarityPage() {
  const [testResults, setTestResults] = useState<TestSkinData[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchTestData = async () => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await fetch('/api/test-skin-rarity')
      const result = await response.json()
      
      if (result.success) {
        setTestResults(result.data.individualResults)
      } else {
        setError(result.error || 'Failed to fetch test data')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchTestData()
  }, [])

  return (
    <SharedLayout>
      <div className="container mx-auto px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <h1 className="text-4xl font-bold text-white">🎨 Skin Rarity System Test</h1>
            <Button 
              onClick={fetchTestData}
              disabled={loading}
              className="bg-orange-600 hover:bg-orange-700"
            >
              {loading ? 'Testing...' : 'Run Test'}
            </Button>
          </div>
          <p className="text-gray-300 text-lg">
            Testing the Community Dragon API integration for skin rarity detection
          </p>
        </div>

        {/* Error Display */}
        {error && (
          <Card className="bg-red-900/40 border-red-700/20 mb-6">
            <CardContent className="p-4">
              <p className="text-red-300">❌ Error: {error}</p>
            </CardContent>
          </Card>
        )}

        {/* Loading State */}
        {loading && (
          <Card className="bg-gray-900/40 border-gray-700/20 mb-6">
            <CardContent className="p-6">
              <div className="flex items-center space-x-3">
                <div className="w-6 h-6 border-2 border-orange-400 border-t-transparent rounded-full animate-spin"></div>
                <span className="text-orange-400">Testing skin rarity system...</span>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Test Results */}
        {testResults.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {testResults.map((skin) => (
              <Card key={skin.skinId} className="bg-gray-900/40 border-purple-700/20">
                <CardHeader>
                  <CardTitle className="text-white flex items-center justify-between">
                    <span>{skin.name}</span>
                    <Badge variant="outline" className="text-xs">
                      ID: {skin.skinId}
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Skin Type */}
                  <div className="flex items-center justify-between">
                    <span className="text-gray-400">Type:</span>
                    <Badge 
                      variant={skin.isBase ? "default" : "secondary"}
                      className={skin.isBase ? "bg-blue-600" : "bg-gray-600"}
                    >
                      {skin.isBase ? "Base Skin" : "Premium Skin"}
                    </Badge>
                  </div>

                  {/* Original Rarity */}
                  <div className="flex items-center justify-between">
                    <span className="text-gray-400">CD Rarity:</span>
                    <Badge variant="outline" className="text-xs font-mono">
                      {skin.originalRarity}
                    </Badge>
                  </div>

                  {/* Normalized Rarity */}
                  <div className="flex items-center justify-between">
                    <span className="text-gray-400">Normalized:</span>
                    <Badge 
                      variant="outline" 
                      className={`border-${skin.normalizedRarity === 'Epic' ? 'purple' : 'gray'}-600`}
                    >
                      {skin.normalizedRarity}
                    </Badge>
                  </div>

                  {/* Legacy Status */}
                  <div className="flex items-center justify-between">
                    <span className="text-gray-400">Legacy:</span>
                    <Badge 
                      variant={skin.isLegacy ? "destructive" : "outline"}
                      className={skin.isLegacy ? "bg-orange-600" : ""}
                    >
                      {skin.isLegacy ? "Yes" : "No"}
                    </Badge>
                  </div>

                  {/* Rarity Icons Display */}
                  <div className="border-t border-gray-700 pt-4">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-400">Icons:</span>
                      <div className="flex items-center space-x-2">
                        <SkinRarityIcons
                          rarity={skin.normalizedRarity}
                          isLegacy={skin.isLegacy}
                          isBase={skin.isBase}
                          size={20}
                          showTooltip={true}
                        />
                        {skin.isBase && (
                          <span className="text-xs text-gray-500">No icons for base skins</span>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Icon URLs (for debugging) */}
                  <div className="text-xs text-gray-500 space-y-1">
                    {skin.rarityIconUrl && (
                      <div>Rarity Icon: ✅</div>
                    )}
                    {skin.legacyIconUrl && (
                      <div>Legacy Icon: ✅</div>
                    )}
                    {!skin.rarityIconUrl && !skin.legacyIconUrl && !skin.isBase && (
                      <div>No icons available</div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* System Info */}
        <Card className="bg-gray-900/40 border-gray-700/20 mt-8">
          <CardHeader>
            <CardTitle className="text-white">🔧 System Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3 text-sm text-gray-300">
            <div>• <strong>Data Source:</strong> Community Dragon API (raw.communitydragon.org)</div>
            <div>• <strong>Cache TTL:</strong> 24 hours</div>
            <div>• <strong>Base Skins:</strong> No rarity icons displayed (isBase: true)</div>
            <div>• <strong>Regular Skins:</strong> kNoRarity → Regular rarity icon</div>
            <div>• <strong>Epic Skins:</strong> kEpic → Epic rarity icon</div>
            <div>• <strong>Legacy Skins:</strong> isLegacy: true → Both rarity + legacy icons</div>
            <div>• <strong>Icon Sources:</strong> Community Dragon official assets</div>
          </CardContent>
        </Card>
      </div>
    </SharedLayout>
  )
}
