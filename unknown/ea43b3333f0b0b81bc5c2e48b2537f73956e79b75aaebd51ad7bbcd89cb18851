import { ProcessedChromaData } from '@/lib/types/league-api'

/**
 * Create a URL-friendly slug from chroma name in format: skin-chromacolor
 * Example: lunar-eclipse-aatrox-red, drx-aatrox-yellow
 * Updated: 2024-12-14 - New format implementation
 */
export function createChromaSlugNew(chromaName: string, championName: string, skinName: string): string {
  // Clean and normalize inputs
  const cleanSkin = skinName.toLowerCase().trim()
  const cleanChroma = chromaName.toLowerCase().trim()

  // Create the slug: skin-chromacolor
  const skinSlug = cleanSkin
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters except spaces and hyphens
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .replace(/^-|-$/g, '') // Remove leading/trailing hyphens
    .trim()

  const chromaSlug = cleanChroma
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters except spaces and hyphens
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .replace(/^-|-$/g, '') // Remove leading/trailing hyphens
    .trim()

  const result = `${skinSlug}-${chromaSlug}`

  return result
}

/**
 * Parse chroma slug back to components
 * Expected format: skin-chromacolor (e.g., lunar-eclipse-aatrox-red)
 */
export function parseChromaSlug(slug: string): { chromaName: string; championName: string; skinName: string } | null {
  const parts = slug.split('-')
  if (parts.length < 2) return null

  // For the new format: skin-chromacolor
  // The last part is the chroma color, everything before is the skin name
  const chromaName = parts[parts.length - 1] // Last part is the chroma color
  const skinName = parts.slice(0, -1).join('-') // Everything before the last part is the skin name

  // We'll need to extract the champion name from the skin name during matching
  // For now, return empty champion name since we'll match differently
  return {
    chromaName,
    championName: '', // Will be determined during matching
    skinName
  }
}

/**
 * Get champion slug for navigation
 */
export function getChampionSlug(championName: string): string {
  return championName
    .toLowerCase()
    .replace(/[^a-z0-9\s]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim()
}

/**
 * Get skin slug for navigation
 */
export function getSkinSlug(skinName: string): string {
  return skinName
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters except spaces and hyphens
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .replace(/^-|-$/g, '') // Remove leading/trailing hyphens
    .trim()
}

/**
 * Format chroma price for display
 */
export function formatChromaPrice(price: string, availability?: string): string {
  // Handle unavailable items
  if (availability === 'Legacy' || availability === 'Unavailable' || !price || price === 'N/A') {
    return 'N/A'
  }

  if (price === 'Included with skin') return 'Included'
  if (price === 'Bundle Only') return 'Bundle'
  if (price === 'Event Only') return 'Event'
  return price
}

/**
 * Extract the actual chroma color name from the chroma name
 * Format: "Skin Name (Chroma Color)" -> "Chroma Color"
 * Example: "Lunar Eclipse Aatrox (Rose Quartz)" -> "Rose Quartz"
 */
export function getActualChromaColorName(chromaName: string): string | null {
  const match = chromaName.match(/\(([^)]+)\)/)
  return match ? match[1] : null
}

/**
 * Map chroma rarity number to rarity name
 * Based on Community Dragon rarity system:
 * 0 = Others, 1 = Regular, 2 = Epic, 3 = Legendary, 4 = Mythic, 5 = Ultimate, 6 = Exalted, 7 = Transcendent
 */
export function getChromaRarityName(rarityNumber: number): string {
  const rarityMap: Record<number, string> = {
    0: 'Others',
    1: 'Regular',
    2: 'Epic',
    3: 'Legendary',
    4: 'Mythic',
    5: 'Ultimate',
    6: 'Exalted',
    7: 'Transcendent'
  }

  return rarityMap[rarityNumber] || 'Others'
}

/**
 * Extract chroma rarity from Community Dragon chroma data
 * The rarity information can be stored in different formats
 */
export function extractChromaRarity(chroma: any): string {
  // Method 1: Check if there's a direct rarity property (number)
  if (typeof chroma.rarity === 'number') {
    return getChromaRarityName(chroma.rarity)
  }

  // Method 2: Check if rarities array exists and has data
  if (chroma.rarities && chroma.rarities.length > 0) {
    // Look for 'riot' region first, fallback to first available
    const riotRarity = chroma.rarities.find((r: any) => r.region === 'riot')
    const rarity = riotRarity || chroma.rarities[0]

    if (rarity && typeof rarity.rarity === 'number') {
      return getChromaRarityName(rarity.rarity)
    }
  }

  // Default to Others if no rarity information is available
  return 'Others'
}

/**
 * Get color display name from hex code
 */
export function getColorDisplayName(hexColor: string): string {
  const hex = hexColor.toLowerCase()

  // Convert hex to RGB for better color matching
  const hexToRgb = (hex: string) => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null
  }

  const rgb = hexToRgb(hex)
  if (!rgb) return hex

  // Define color ranges for better matching
  const { r, g, b } = rgb

  // Calculate color properties
  const max = Math.max(r, g, b)
  const min = Math.min(r, g, b)
  const diff = max - min
  const sum = max + min
  const lightness = sum / 2

  // Handle grayscale colors
  if (diff < 30) {
    if (lightness < 50) return 'Black'
    if (lightness < 120) return 'Gray'
    if (lightness < 200) return 'Silver'
    return 'White'
  }

  // Handle colored pixels
  let hue = 0
  if (diff > 0) {
    switch (max) {
      case r: hue = ((g - b) / diff + (g < b ? 6 : 0)) / 6; break
      case g: hue = ((b - r) / diff + 2) / 6; break
      case b: hue = ((r - g) / diff + 4) / 6; break
    }
  }

  // Convert hue to degrees
  hue *= 360

  // Determine color based on hue ranges
  if (hue >= 345 || hue < 15) return 'Red'
  if (hue >= 15 && hue < 45) return 'Orange'
  if (hue >= 45 && hue < 75) return 'Yellow'
  if (hue >= 75 && hue < 150) return 'Green'
  if (hue >= 150 && hue < 210) return 'Cyan'
  if (hue >= 210 && hue < 270) return 'Blue'
  if (hue >= 270 && hue < 315) return 'Purple'
  if (hue >= 315 && hue < 345) return 'Pink'

  // Fallback for edge cases
  return 'Unknown'
}

/**
 * Sort chromas by various criteria
 */
export function sortChromas(chromas: ProcessedChromaData[], sortBy: 'name' | 'champion' | 'skin' | 'tier' | 'price' | 'color'): ProcessedChromaData[] {
  return [...chromas].sort((a, b) => {
    switch (sortBy) {
      case 'name':
        return a.name.localeCompare(b.name)
      case 'champion':
        return a.champion.localeCompare(b.champion)
      case 'skin':
        return a.skinName.localeCompare(b.skinName)
      case 'tier':
        const tierOrder = ['Regular', 'Epic', 'Legendary', 'Mythic', 'Ultimate', 'Exalted', 'Transcendent']
        const aIndex = tierOrder.indexOf(a.skinTier)
        const bIndex = tierOrder.indexOf(b.skinTier)
        return (aIndex === -1 ? 999 : aIndex) - (bIndex === -1 ? 999 : bIndex)
      case 'price':
        // Sort by price, with special handling for non-RP prices
        const extractPrice = (price: string): number => {
          const match = price.match(/(\d+)/)
          return match ? parseInt(match[1]) : 0
        }
        return extractPrice(a.price) - extractPrice(b.price)
      case 'color':
        return a.color.localeCompare(b.color)
      default:
        return 0
    }
  })
}

/**
 * Group chromas by various criteria
 */
export function groupChromas(chromas: ProcessedChromaData[], groupBy: 'champion' | 'skin' | 'tier' | 'set'): Record<string, ProcessedChromaData[]> {
  return chromas.reduce((groups, chroma) => {
    let key: string
    
    switch (groupBy) {
      case 'champion':
        key = chroma.champion
        break
      case 'skin':
        key = chroma.skinName
        break
      case 'tier':
        key = chroma.skinTier
        break
      case 'set':
        key = chroma.set || 'Other'
        break
      default:
        key = 'All'
    }

    if (!groups[key]) {
      groups[key] = []
    }
    groups[key].push(chroma)
    
    return groups
  }, {} as Record<string, ProcessedChromaData[]>)
}

/**
 * Filter chromas by color similarity
 */
export function filterChromasByColorSimilarity(chromas: ProcessedChromaData[], targetColor: string, threshold: number = 50): ProcessedChromaData[] {
  // Convert hex to RGB for color distance calculation
  const hexToRgb = (hex: string): { r: number; g: number; b: number } | null => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null
  }

  const colorDistance = (color1: { r: number; g: number; b: number }, color2: { r: number; g: number; b: number }): number => {
    return Math.sqrt(
      Math.pow(color1.r - color2.r, 2) +
      Math.pow(color1.g - color2.g, 2) +
      Math.pow(color1.b - color2.b, 2)
    )
  }

  const targetRgb = hexToRgb(targetColor)
  if (!targetRgb) return chromas

  return chromas.filter(chroma => {
    return chroma.colors.some(color => {
      const chromaRgb = hexToRgb(color)
      if (!chromaRgb) return false
      return colorDistance(targetRgb, chromaRgb) <= threshold
    })
  })
}

/**
 * Get related chromas (same champion or skin line)
 */
export function getRelatedChromas(chroma: ProcessedChromaData, allChromas: ProcessedChromaData[], limit: number = 6): ProcessedChromaData[] {
  const related = allChromas.filter(c => 
    c.id !== chroma.id && (
      c.champion === chroma.champion ||
      (c.set && chroma.set && c.set === chroma.set)
    )
  )

  // Sort by relevance (same champion first, then same set)
  related.sort((a, b) => {
    if (a.champion === chroma.champion && b.champion !== chroma.champion) return -1
    if (a.champion !== chroma.champion && b.champion === chroma.champion) return 1
    return 0
  })

  return related.slice(0, limit)
}

/**
 * Calculate chroma statistics
 */
export function calculateChromaStats(chromas: ProcessedChromaData[]): {
  totalChromas: number
  championCount: number
  skinCount: number
  tierDistribution: Record<string, number>
  colorDistribution: Record<string, number>
  availabilityDistribution: Record<string, number>
} {
  const champions = new Set(chromas.map(c => c.champion))
  const skins = new Set(chromas.map(c => `${c.champion}-${c.skinName}`))
  
  const tierDistribution: Record<string, number> = {}
  const colorDistribution: Record<string, number> = {}
  const availabilityDistribution: Record<string, number> = {}

  chromas.forEach(chroma => {
    // Tier distribution
    tierDistribution[chroma.skinTier] = (tierDistribution[chroma.skinTier] || 0) + 1
    
    // Color distribution
    chroma.colors.forEach(color => {
      const colorName = getColorDisplayName(color)
      colorDistribution[colorName] = (colorDistribution[colorName] || 0) + 1
    })
    
    // Availability distribution
    availabilityDistribution[chroma.availability] = (availabilityDistribution[chroma.availability] || 0) + 1
  })

  return {
    totalChromas: chromas.length,
    championCount: champions.size,
    skinCount: skins.size,
    tierDistribution,
    colorDistribution,
    availabilityDistribution
  }
}
