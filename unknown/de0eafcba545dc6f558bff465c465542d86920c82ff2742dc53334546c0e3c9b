/**
 * Mythic Cost Service - Handles fetching mythic shop cost data for skins and chromas
 */

export interface MythicCostData {
  contentId?: string;
  cost: number
  currency: string
  endTime: string
}

export interface MythicCostResponse {
  success: boolean
  data?: MythicCostData
  error?: string
}

class MythicCostService {
  private cache = new Map<string, { data: MythicCostData; timestamp: number }>()
  private bulkCache = new Map<string, { data: Map<string, MythicCostData | null>; timestamp: number }>()
  private readonly CACHE_TTL = 1000 * 60 * 60 // 1 hour cache
  private readonly MYTHIC_SHOP_API_URL = 'https://api.loldb.info/api/mythic-shop'
  private readonly STORE_SKINS_API_URL = 'https://api.loldb.info/api/store/skins'
  private readonly COMMUNITY_DRAGON_SKINS_URL = 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/skins.json'

  /**
   * Get mythic cost data for a skin or chroma by itemId
   */
  async getMythicCost(itemId: string): Promise<MythicCostResponse> {
    try {
      // Check cache first
      const cached = this.cache.get(itemId)
      if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
        return {
          success: true,
          data: cached.data
        }
      }


      // Fetch mythic shop data for this specific item
      const response = await fetch(`${this.MYTHIC_SHOP_API_URL}/${itemId}`)
      
      if (!response.ok) {
        console.warn(`⚠️ Mythic shop API returned ${response.status} for item ID ${itemId}`)
        return {
          success: false,
          error: `Failed to fetch mythic cost data: HTTP ${response.status}`
        }
      }

      const mythicData = await response.json()

      if (!mythicData.success) {
        return {
          success: false,
          error: 'Mythic shop API returned unsuccessful response'
        }
      }

      // Extract cost data
      const costData: MythicCostData = {
        cost: mythicData.data.cost,
        currency: mythicData.data.currency,
        endTime: mythicData.data.endTime
      }

      // Cache the result
      this.cache.set(itemId, {
        data: costData,
        timestamp: Date.now()
      })


      return {
        success: true,
        data: costData
      }

    } catch (error) {
      console.error(`❌ Error fetching mythic cost for item ID ${itemId}:`, error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      }
    }
  }

  /**
   * Get itemId from store/skins API using contentId
   */
  async getItemIdFromContentId(contentId: string): Promise<string | null> {
    try {

      const response = await fetch(this.STORE_SKINS_API_URL)
      if (!response.ok) {
        console.error(`❌ Failed to fetch store skins data: HTTP ${response.status}`)
        return null
      }

      const storeData = await response.json()
      if (!storeData.success || !storeData.data?.catalog) {
        console.error('❌ Store skins API returned unsuccessful response')
        return null
      }

      // Find the item with matching contentId
      const item = storeData.data.catalog.find((item: any) => item.contentId === contentId)
      
      if (!item) {
        return null
      }

      return item.id

    } catch (error) {
      console.error(`❌ Error looking up itemId for contentId ${contentId}:`, error)
      return null
    }
  }

  /**
   * Bulk fetch mythic prices for multiple skins (for skins page)
   * Only fetches prices for mythic skins
   */
  async getBulkMythicPrices(skins: Array<{ contentId: string; tier: string }>): Promise<Map<string, MythicCostData | null>> {
    try {
      const cacheKey = 'bulk_mythic_prices'
      const cached = this.bulkCache.get(cacheKey)

      // Check cache first
      if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
        return cached.data
      }


      // Filter only mythic skins
      const mythicSkins = skins.filter(skin => skin.tier === 'Mythic')

      if (mythicSkins.length === 0) {
        const emptyResult = new Map<string, MythicCostData | null>()
        this.bulkCache.set(cacheKey, { data: emptyResult, timestamp: Date.now() })
        return emptyResult
      }

      // Step 1: Fetch mythic shop data to get available itemIds
      const mythicShopResponse = await fetch(this.MYTHIC_SHOP_API_URL)
      if (!mythicShopResponse.ok) {
        console.error(`❌ Failed to fetch mythic shop data: HTTP ${mythicShopResponse.status}`)
        return new Map()
      }

      const mythicShopData = await mythicShopResponse.json()
      if (!mythicShopData.success || !mythicShopData.data) {
        console.error('❌ Mythic shop API returned unsuccessful response')
        return new Map()
      }

      const availableItemIds = new Set(mythicShopData.data.map((item: any) => item.itemId))

      // Step 2: Fetch Community Dragon skins data to map contentId to itemId
      const skinsResponse = await fetch(this.COMMUNITY_DRAGON_SKINS_URL)
      if (!skinsResponse.ok) {
        console.error(`❌ Failed to fetch Community Dragon skins data: HTTP ${skinsResponse.status}`)
        return new Map()
      }

      const skinsData = await skinsResponse.json()

      // Step 3: Create mapping from contentId to itemId for mythic skins
      const contentIdToItemId = new Map<string, string>()

      for (const [skinId, skinInfo] of Object.entries(skinsData)) {
        const skin = skinInfo as any
        if (skin.contentId && mythicSkins.some(ms => ms.contentId === skin.contentId)) {
          // Check if this skin's itemId is available in mythic shop
          if (availableItemIds.has(skin.contentId)) {
            contentIdToItemId.set(skin.contentId, skin.contentId)
          }
        }
      }


      // Step 4: Fetch individual prices for mapped skins
      const priceResults = new Map<string, MythicCostData | null>()

      // Initialize all mythic skins with null (N/A)
      mythicSkins.forEach(skin => {
        priceResults.set(skin.contentId, null)
      })

      // Fetch prices for available skins
      const pricePromises = Array.from(contentIdToItemId.entries()).map(async ([contentId, itemId]) => {
        try {
          const priceResponse = await fetch(`${this.MYTHIC_SHOP_API_URL}/${itemId}`)
          if (!priceResponse.ok) {
            console.warn(`⚠️ Failed to fetch price for itemId ${itemId}: HTTP ${priceResponse.status}`)
            return { contentId, data: null }
          }

          const priceData = await priceResponse.json()
          if (priceData.success && priceData.data) {
            const costData: MythicCostData = {
              cost: priceData.data.cost,
              currency: priceData.data.currency,
              endTime: priceData.data.endTime
            }
            return { contentId, data: costData }
          } else {
            return { contentId, data: null }
          }
        } catch (error) {
          console.error(`❌ Error fetching price for contentId ${contentId}:`, error)
          return { contentId, data: null }
        }
      })

      const priceResponses = await Promise.all(pricePromises)

      // Update results with fetched prices
      priceResponses.forEach(({ contentId, data }) => {
        priceResults.set(contentId, data)
      })

      // Cache the results
      this.bulkCache.set(cacheKey, {
        data: priceResults,
        timestamp: Date.now()
      })

      return priceResults

    } catch (error) {
      console.error('❌ Error in bulk mythic price fetching:', error)
      return new Map()
    }
  }

  /**
   * Bulk fetch mythic prices for multiple chromas
   * Only fetches prices for chromas with contentId
   */
  async getBulkChromaMythicPrices(chromas: Array<{ contentId?: string; availability: string }>): Promise<Map<string, MythicCostData | null>> {
    try {
      const cacheKey = 'bulk_chroma_mythic_prices'
      const cached = this.bulkCache.get(cacheKey)

      // Check cache first
      if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
        return cached.data
      }

      // Filter chromas that have contentId and are not available (Legacy, etc.)
      const chromasWithContentId = chromas.filter(chroma =>
        chroma.contentId && chroma.availability !== 'Available'
      )

      if (chromasWithContentId.length === 0) {
        const emptyResult = new Map<string, MythicCostData | null>()
        this.bulkCache.set(cacheKey, { data: emptyResult, timestamp: Date.now() })
        return emptyResult
      }

      // Step 1: Get available items from mythic shop
      const mythicShopResponse = await fetch(this.MYTHIC_SHOP_API_URL, {
        headers: {
          'User-Agent': 'LoLDB-Website/1.0',
          'Accept': 'application/json',
        },
        // Add timeout for production
        signal: AbortSignal.timeout(10000) // 10 second timeout
      })

      if (!mythicShopResponse.ok) {
        console.warn(`⚠️ Failed to fetch mythic shop data: HTTP ${mythicShopResponse.status}`)
        return new Map()
      }

      const mythicShopData = await mythicShopResponse.json()

      if (!mythicShopData.success || !mythicShopData.data) {
        console.warn('⚠️ Mythic shop API returned unsuccessful response')
        return new Map()
      }

      // Get available item IDs
      const availableItemIds = new Set(mythicShopData.data.map((item: any) => item.itemId))

      // Step 2: Initialize results map
      const priceResults = new Map<string, MythicCostData | null>()

      // Initialize all chromas with null (N/A)
      chromasWithContentId.forEach(chroma => {
        if (chroma.contentId) {
          priceResults.set(chroma.contentId, null)
        }
      })

      // Step 3: Fetch prices for available chromas
      const pricePromises = chromasWithContentId
        .filter(chroma => chroma.contentId && availableItemIds.has(chroma.contentId))
        .map(async (chroma) => {
          try {
            const priceResponse = await fetch(`${this.MYTHIC_SHOP_API_URL}/${chroma.contentId}`)
            if (!priceResponse.ok) {
              console.warn(`⚠️ Failed to fetch price for chroma contentId ${chroma.contentId}: HTTP ${priceResponse.status}`)
              return { contentId: chroma.contentId!, data: null }
            }

            const priceData = await priceResponse.json()
            if (priceData.success && priceData.data) {
              const costData: MythicCostData = {
                cost: priceData.data.cost,
                currency: priceData.data.currency,
                endTime: priceData.data.endTime
              }
              return { contentId: chroma.contentId!, data: costData }
            } else {
              return { contentId: chroma.contentId!, data: null }
            }
          } catch (error) {
            console.error(`❌ Error fetching price for chroma contentId ${chroma.contentId}:`, error)
            return { contentId: chroma.contentId!, data: null }
          }
        })

      const priceResponses = await Promise.all(pricePromises)

      // Update results with fetched prices
      priceResponses.forEach(({ contentId, data }) => {
        priceResults.set(contentId, data)
      })

      // Cache the results
      this.bulkCache.set(cacheKey, {
        data: priceResults,
        timestamp: Date.now()
      })

      return priceResults

    } catch (error) {
      console.error('❌ Error in bulk chroma mythic price fetching:', error)
      return new Map()
    }
  }

  /**
   * Clear cache for testing purposes
   */
  clearCache(): void {
    this.cache.clear()
    this.bulkCache.clear()
  }

  async getAllMythicItems(): Promise<MythicCostData[]> {
    try {
        const response = await fetch(this.MYTHIC_SHOP_API_URL);
        if (!response.ok) {
            console.error(`❌ Failed to fetch mythic shop data: HTTP ${response.status}`);
            return [];
        }
        const data = await response.json();
        if (!data.success) {
            console.error('❌ Mythic shop API returned unsuccessful response');
            return [];
        }
        return data.data;
    } catch (error) {
        console.error('❌ Error fetching all mythic items:', error);
        return [];
    }
  }

  async getMythicItemDetails(contentId: string): Promise<MythicCostData | null> {
    const allMythicItems = await this.getAllMythicItems();
    return allMythicItems.find((item: MythicCostData) => item.contentId === contentId) || null;
  }
}

// Export singleton instance
export const mythicCostService = new MythicCostService()
