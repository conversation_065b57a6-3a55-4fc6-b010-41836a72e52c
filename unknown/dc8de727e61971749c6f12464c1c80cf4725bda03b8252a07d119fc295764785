"use client"

import * as React from "react"
import { X } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import Image from "next/image"

interface ChampionFilters {
  search: string
  roles: string[]
  classes: string[]
  difficulty: [number, number]
  sortField: 'name' | 'release' | 'price'
  sortDirection: 'asc' | 'desc'
}

interface SelectedChampionFiltersDisplayProps {
  filters: ChampionFilters
  onRemoveFilter: (filterType: keyof ChampionFilters, value: string) => void
  onClearCategory: (filterType: keyof ChampionFilters) => void
}

// Helper function to get position icon URL
function getPositionIconUrl(role: string): string {
  const roleMap: Record<string, string> = {
    'Top': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-parties/global/default/icon-position-top.png',
    'Jungle': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-parties/global/default/icon-position-jungle.png',
    'Middle': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-parties/global/default/icon-position-middle.png',
    'Bottom': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-parties/global/default/icon-position-bottom.png',
    'Support': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-parties/global/default/icon-position-utility.png'
  }
  return roleMap[role] || 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-parties/global/default/icon-position-fill.png'
}

// Helper function to get class icon URL
function getClassIconUrl(className: string): string {
  if (!className) {
    return 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-fighter.png'
  }

  const classIcons: Record<string, string> = {
    // Standard capitalized names
    'Fighter': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-fighter.png',
    'Tank': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-tank.png',
    'Mage': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-mage.png',
    'Assassin': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-assassin.png',
    'Support': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-support.png',
    'Marksman': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-marksman.png',

    // Lowercase variants from loldb.info API
    'fighter': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-fighter.png',
    'tank': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-tank.png',
    'mage': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-mage.png',
    'assassin': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-assassin.png',
    'support': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-support.png',
    'marksman': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-marksman.png',

    // Additional mappings for loldb.info API tags
    'carry': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-marksman.png',
    'ranged': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-marksman.png',
    'melee': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-fighter.png',
    'jungler': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-fighter.png',
    'recommended': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-fighter.png',
    'pusher': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-champion-details/global/default/role-icon-fighter.png'
  }

  // Return the icon URL or fallback to Fighter icon
  return classIcons[className] || classIcons['Fighter']
}

export function SelectedChampionFiltersDisplay({
  filters,
  onRemoveFilter,
  onClearCategory
}: SelectedChampionFiltersDisplayProps) {
  const hasFilters = filters.roles.length > 0 || filters.classes.length > 0

  if (!hasFilters) {
    return null
  }

  const renderFilterGroup = (
    title: string,
    items: string[],
    filterType: keyof ChampionFilters,
    color: string,
    showIcons = false,
    iconType?: 'role' | 'class'
  ) => {
    if (items.length === 0) return null

    return (
      <div className="flex items-center space-x-2">
        <span className="text-gray-400 text-sm font-medium">{title}:</span>
        <div className="flex items-center space-x-1 flex-wrap">
          {items.length <= 3 ? (
            items.map(item => (
              <Badge
                key={item}
                variant="secondary"
                className={`${color} text-xs flex items-center space-x-1 cursor-pointer hover:opacity-80 transition-opacity`}
                onClick={() => onRemoveFilter(filterType, item)}
              >
                {showIcons && iconType === 'role' && (
                  <div className="w-3 h-3 flex items-center justify-center">
                    <Image
                      src={getPositionIconUrl(item)}
                      alt={item}
                      width={12}
                      height={12}
                      className="w-3 h-3"
                      unoptimized
                    />
                  </div>
                )}
                {showIcons && iconType === 'class' && (
                  <div className="w-3 h-3 flex items-center justify-center">
                    <Image
                      src={getClassIconUrl(item)}
                      alt={item}
                      width={12}
                      height={12}
                      className="w-3 h-3"
                      unoptimized
                    />
                  </div>
                )}
                <span>{item}</span>
                <X className="h-3 w-3" />
              </Badge>
            ))
          ) : (
            <>
              {items.slice(0, 2).map(item => (
                <Badge
                  key={item}
                  variant="secondary"
                  className={`${color} text-xs flex items-center space-x-1 cursor-pointer hover:opacity-80 transition-opacity`}
                  onClick={() => onRemoveFilter(filterType, item)}
                >
                  {showIcons && iconType === 'role' && (
                    <div className="w-3 h-3 flex items-center justify-center">
                      <Image
                        src={getPositionIconUrl(item)}
                        alt={item}
                        width={12}
                        height={12}
                        className="w-3 h-3"
                        unoptimized
                      />
                    </div>
                  )}
                  {showIcons && iconType === 'class' && (
                    <div className="w-3 h-3 flex items-center justify-center">
                      <Image
                        src={getClassIconUrl(item)}
                        alt={item}
                        width={12}
                        height={12}
                        className="w-3 h-3"
                        unoptimized
                      />
                    </div>
                  )}
                  <span>{item}</span>
                  <X className="h-3 w-3" />
                </Badge>
              ))}
              <Badge
                variant="secondary"
                className={`${color} text-xs cursor-pointer hover:opacity-80 transition-opacity`}
                onClick={() => onClearCategory(filterType)}
              >
                +{items.length - 2} more
              </Badge>
            </>
          )}
        </div>
      </div>
    )
  }

  return (
    <div className="mb-4 p-3 bg-gray-800/30 rounded-lg border border-gray-700/20">
      <div className="flex flex-wrap items-center gap-4">
        {renderFilterGroup(
          "Roles",
          filters.roles,
          "roles",
          "bg-orange-600/20 text-orange-300 border-orange-600/30",
          true,
          'role'
        )}
        
        {renderFilterGroup(
          "Classes",
          filters.classes,
          "classes",
          "bg-blue-600/20 text-blue-300 border-blue-600/30",
          false
        )}
      </div>
    </div>
  )
}
