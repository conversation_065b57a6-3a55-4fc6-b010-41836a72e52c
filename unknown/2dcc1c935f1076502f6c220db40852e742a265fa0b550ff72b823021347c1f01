import { Card, CardContent } from "@/components/ui/card"

interface ChampionLoreServerProps {
  championData: any
}

export default function ChampionLoreServer({ championData }: ChampionLoreServerProps) {
  const { lore } = championData

  return (
    <Card className="bg-black/70 backdrop-blur-md border-orange-700/40 shadow-xl">
      <CardContent className="p-6">
        <h3 className="text-xl font-semibold text-white mb-6">Lore</h3>
        <div className="text-gray-300 leading-relaxed">
          {lore.split('\n').map((paragraph: string, index: number) => (
            <p key={index} className="mb-4 last:mb-0">
              {paragraph}
            </p>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
