// Chroma Service - Fetches and processes chroma data from Community Dragon API and Store API

import { CommunityDragonSkin, CommunityDragonSkinsResponse, ProcessedChromaData } from '@/lib/types/league-api';
import { createChromaURL } from "@/lib/utils/chroma-url-utils";
import { extractChromaRarity, getColorDisplayName } from '@/lib/utils/chroma-utils';
import { MythicCostData, mythicCostService } from "./mythic-cost-service";

// Store API interfaces for chromas
export interface ChromaStoreData {
  itemId: number
  inventoryType: 'CHAMPION_SKIN'
  subInventoryType: 'RECOLOR'
  iconUrl: string
  maxQuantity: number
  rp: number
  releaseDate: number
  name: string
}

export interface ChromaStoreApiResponse {
  success: boolean
  data: {
    catalog: ChromaStoreData[]
  }
}

export interface ChromaServiceCache {
  data: ProcessedChromaData[]
  timestamp: number
}

export interface ChromaDetails {
  chroma: ProcessedChromaData;
  relatedChromas: ProcessedChromaData[];
  totalChromasForSkin: number;
  mythicCost?: MythicCostData | null;
}

class ChromaService {
  private cache = new Map<string, ChromaServiceCache>()
  private championKeyCache = new Map<string, string>() // Cache for champion name -> key mapping
  private storeChromasCache: { data: ChromaStoreData[]; timestamp: number } | null = null
  private readonly CACHE_TTL = 1000 * 60 * 60 * 24 // 24 hours
  private readonly API_URL = 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/skins.json'
  private readonly STORE_API_URL = 'https://api.loldb.info/api/store/skins'
  private readonly DDRAGON_VERSION = '15.11.1' // Current patch version

  /**
   * Get all chromas from all champions
   */
  async getAllChromas(forceFetch: boolean = false): Promise<ProcessedChromaData[]> {
    const cacheKey = 'all_chromas'
    const cached = this.cache.get(cacheKey)

    // Check if cached data is still valid
    if (cached && !forceFetch && Date.now() - cached.timestamp < this.CACHE_TTL) {
      
      return cached.data
    }

    try {
      

      const response = await fetch(this.API_URL)
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: Failed to fetch skins data`)
      }

      const rawData: CommunityDragonSkinsResponse = await response.json()

      // Process the data to extract chromas
      const processedChromas = await this.processChromaData(rawData)

      // Cache the processed data
      this.cache.set(cacheKey, {
        data: processedChromas,
        timestamp: Date.now()
      })

      
      return processedChromas

    } catch (error) {
      console.error('❌ Failed to fetch chromas data:', error)

      // Return cached data if available, even if expired
      if (cached) {
        
        return cached.data
      }

      // Return empty array as fallback
      return []
    }
  }

  /**
   * Get paginated chromas
   */
  async getPaginatedChromas(page: number = 1, limit: number = 50): Promise<{
    chromas: ProcessedChromaData[]
    total: number
    currentPage: number
    hasMore: boolean
  }> {
    const allChromas = await this.getAllChromas()
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedChromas = allChromas.slice(startIndex, endIndex)

    return {
      chromas: paginatedChromas,
      total: allChromas.length,
      currentPage: page,
      hasMore: endIndex < allChromas.length
    }
  }

  /**
   * Get chromas by champion
   */
  async getChromasByChampion(championKey: string): Promise<ProcessedChromaData[]> {
    const allChromas = await this.getAllChromas()
    return allChromas.filter(chroma => chroma.championKey.toLowerCase() === championKey.toLowerCase())
  }

  /**
   * Get chromas by skin
   */
  async getChromasBySkin(skinId: number): Promise<ProcessedChromaData[]> {
    const allChromas = await this.getAllChromas()
    return allChromas.filter(chroma => chroma.skinId === skinId)
  }

  /**
   * Search chromas by name, champion, or skin
   */
  async searchChromas(query: string): Promise<ProcessedChromaData[]> {
    const allChromas = await this.getAllChromas()
    const lowerQuery = query.toLowerCase()
    
    return allChromas.filter(chroma => 
      chroma.name.toLowerCase().includes(lowerQuery) ||
      chroma.champion.toLowerCase().includes(lowerQuery) ||
      chroma.skinName.toLowerCase().includes(lowerQuery)
    )
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.cache.clear()
    this.championKeyCache.clear()
    this.storeChromasCache = null
    
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    }
  }

  /**
   * Fetch chroma data from store API
   */
  private async getStoreChromasData(): Promise<ChromaStoreData[]> {
    // Check cache first
    if (this.storeChromasCache && Date.now() - this.storeChromasCache.timestamp < this.CACHE_TTL) {
      
      return this.storeChromasCache.data
    }

    try {
      
      const response = await fetch(this.STORE_API_URL)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: Failed to fetch store chromas data`)
      }

      const apiResponse: ChromaStoreApiResponse = await response.json()

      if (!apiResponse.success) {
        throw new Error('Store API returned unsuccessful response')
      }

      // Extract the catalog array from the response
      const catalog = apiResponse.data?.catalog || []

      // Filter only chromas (subInventoryType: 'RECOLOR')
      const chromas = catalog.filter(item =>
        item.inventoryType === 'CHAMPION_SKIN' &&
        item.subInventoryType === 'RECOLOR'
      )

      // Cache the data
      this.storeChromasCache = {
        data: chromas,
        timestamp: Date.now()
      }

      
      return chromas

    } catch (error) {
      console.error('❌ Failed to fetch store chromas data:', error)

      // Return cached data if available, even if expired
      if (this.storeChromasCache) {
        
        return this.storeChromasCache.data
      }

      return []
    }
  }

  /**
   * Fetch champion data from Data Dragon API
   */
  private async getChampionData(championName: string): Promise<{ key: string, name: string }> {
    // Check cache first
    const cached = this.championKeyCache.get(championName.toLowerCase())
    if (cached) {
      return { key: cached, name: championName }
    }

    try {
      // Normalize champion name for API call (remove spaces, special characters)
      const normalizedName = championName.replace(/[^a-zA-Z]/g, '')
      const url = `https://ddragon.leagueoflegends.com/cdn/${this.DDRAGON_VERSION}/data/en_US/champion/${normalizedName}.json`

      const response = await fetch(url)
      if (!response.ok) {
        throw new Error(`Failed to fetch champion data for ${championName}`)
      }

      const data = await response.json()
      const championData = Object.values(data.data)[0] as any
      const championKey = championData.key
      const championRealName = championData.name

      // Cache the result
      this.championKeyCache.set(championName.toLowerCase(), championKey)

      return { key: championKey, name: championRealName }
    } catch (error) {
      console.warn(`⚠️ Failed to fetch champion data for ${championName}, using fallback`)
      // Fallback to extracting from skin ID or using normalized name
      return {
        key: this.normalizeChampionKey(championName.toLowerCase()),
        name: championName
      }
    }
  }

  /**
   * Fetch champion key from Data Dragon API (legacy method)
   */
  private async getChampionKey(championName: string): Promise<string> {
    const data = await this.getChampionData(championName)
    return data.key
  }

  /**
   * Process raw Community Dragon skin data to extract chromas
   */
  private async processChromaData(rawData: CommunityDragonSkinsResponse): Promise<ProcessedChromaData[]> {
    const processedChromas: ProcessedChromaData[] = []

    // Fetch store chromas data for pricing and release dates
    const storeChromasData = await this.getStoreChromasData()
    

    // Create a map of champion names from base skins
    const championNameMap = this.createChampionNameMap(rawData)

    // Create multiple maps for matching store data
    // 1. By itemId (in case there's a direct match)
    const storeDataByItemId = new Map<number, ChromaStoreData>()
    // 2. By skin name + chroma color for more flexible matching
    const storeDataByNamePattern = new Map<string, ChromaStoreData>()

    storeChromasData.forEach(storeChroma => {
      storeDataByItemId.set(storeChroma.itemId, storeChroma)

      // Extract skin name and chroma color from store name
      // Format: "Skin Name (Chroma Color)"
      const match = storeChroma.name.match(/^(.+?)\s*\(([^)]+)\)$/)
      if (match) {
        const [, skinName, chromaColor] = match
        const key = `${skinName.toLowerCase().trim()}|${chromaColor.toLowerCase().trim()}`
        storeDataByNamePattern.set(key, storeChroma)
      }
    })



    for (const [skinId, skinData] of Object.entries(rawData)) {
      // Skip base skins and skins without chromas
      if (skinData.isBase || !skinData.chromas || skinData.chromas.length === 0) {
        continue
      }

      const championName = this.getChampionNameFromSkin(skinId, championNameMap)
      // For chroma URLs, we always need the numeric champion ID, not the string key
      const championKey = this.extractChampionKeyFromId(parseInt(skinId))

      // Normalize skin rarity
      const normalizedRarity = this.normalizeSkinRarity(skinData.rarity)

      // Process each chroma for this skin
      skinData.chromas.forEach((chroma, index) => {
        // Only process chromas with kRecolor classification (exclude default chromas)
        if (chroma.skinClassification === 'kRecolor') {
          // Try multiple matching strategies to find store data
          let storeData: ChromaStoreData | undefined

          // Strategy 1: Direct ID match (unlikely to work but worth trying)
          storeData = storeDataByItemId.get(chroma.id)

          // Strategy 2: Match by skin name + chroma color pattern
          if (!storeData && chroma.name) {
            // Try to extract color from chroma name or use color display name
            const chromaColorName = getColorDisplayName(chroma.colors?.[0] || '#ffffff')
            const skinNameKey = `${skinData.name.toLowerCase().trim()}|${chromaColorName.toLowerCase().trim()}`
            storeData = storeDataByNamePattern.get(skinNameKey)
          }

          // Extract chroma rarity from Community Dragon data
          const chromaRarity = extractChromaRarity(chroma)

          // Extract chroma description
          const chromaDescription = this.extractChromaDescription(chroma)

          const processedChroma: ProcessedChromaData = {
            id: chroma.id,
            name: storeData ? storeData.name : (chroma.name || `Chroma ${index + 1}`), // Use store name if available
            champion: championName,
            championKey: championKey,
            skinName: skinData.name,
            skinId: parseInt(skinId),
            skinTier: storeData ? 'N/A' : 'Legacy', // If not in store API, it's legacy
            chromaRarity: chromaRarity, // Use new chroma-specific rarity
            color: chroma.colors?.[0] || '#ffffff',
            colors: chroma.colors || ['#ffffff'],
            image: this.generateChromaImageUrl(chroma, championKey),
            chromaPath: chroma.chromaPath || '',
            isDefault: false,
            price: storeData ? `${storeData.rp} RP` : 'Legacy',
            availability: storeData ? 'Available' : 'Legacy',
            set: this.extractSkinSet(skinData.name),
            contentId: chroma.contentId, // Add contentId for mythic shop integration
            releaseDate: storeData ? this.formatReleaseDate(storeData.releaseDate) : undefined,
            description: chromaDescription // Add chroma description
          }





          processedChromas.push(processedChroma)
        }
      })

      // Don't add default chromas as they aren't considered actual chromas
    }

    // Sort by champion name, then skin name, then chroma name
    processedChromas.sort((a, b) => {
      if (a.champion !== b.champion) {
        return a.champion.localeCompare(b.champion)
      }
      if (a.skinName !== b.skinName) {
        return a.skinName.localeCompare(b.skinName)
      }
      return a.name.localeCompare(b.name)
    })

    return processedChromas
  }

  /**
   * Create a map of champion names from base skins
   */
  private createChampionNameMap(rawData: CommunityDragonSkinsResponse): Record<string, string> {
    const championNameMap: Record<string, string> = {}

    for (const [skinId, skinData] of Object.entries(rawData)) {
      if (skinData.isBase) {
        const championKey = this.extractChampionKeyFromId(parseInt(skinId))
        championNameMap[championKey] = skinData.name
      }
    }

    return championNameMap
  }

  /**
   * Extract champion key from skin ID
   */
  private extractChampionKeyFromId(skinId: number): string {
    const championId = Math.floor(skinId / 1000)
    return championId.toString()
  }

  /**
   * Get champion name from skin data using base skin mapping (similar to skin service)
   */
  private getChampionNameFromSkin(skinId: string, championNameMap: Record<string, string>): string {
    // Extract champion ID from skin ID
    const championId = Math.floor(parseInt(skinId) / 1000)

    // Try to get champion name from the base skin mapping
    if (championNameMap[championId.toString()]) {
      return championNameMap[championId.toString()]
    }

    // This should rarely happen if the base skin mapping is complete
    console.warn(`⚠️ No champion found for ID ${championId} from skin ID ${skinId}`)
    return `Unknown Champion ${championId}`
  }

  /**
   * Extract champion key from skin name and champion map (legacy method)
   */
  private extractChampionKey(skinName: string, championNameMap: Record<string, string>): string {
    // Try to find the champion key by matching the skin name with champion names
    for (const [key, name] of Object.entries(championNameMap)) {
      if (skinName.toLowerCase().includes(name.toLowerCase())) {
        return key
      }
    }

    // Fallback: extract from skin name and normalize
    const firstWord = skinName.split(' ')[0].toLowerCase()
    return this.normalizeChampionKey(firstWord)
  }

  /**
   * Normalize champion key for consistency
   */
  private normalizeChampionKey(key: string): string {
    // Handle special cases for champion keys
    const keyMap: Record<string, string> = {
      'wukong': 'monkeyking',
      'jarvan': 'jarvaniv',
      'kogmaw': 'kogmaw',
      'leblanc': 'leblanc',
      'masteryi': 'masteryi',
      'missfortune': 'missfortune',
      'reksai': 'reksai',
      'tahmkench': 'tahmkench',
      'twistedfate': 'twistedfate',
      'xinzhao': 'xinzhao'
    }

    return keyMap[key] || key
  }

  /**
   * Normalize skin rarity to standard tiers
   */
  private normalizeSkinRarity(rarity: string): string {
    const rarityMap: Record<string, string> = {
      'kNoRarity': 'Regular',
      'kRare': 'Epic',
      'kEpic': 'Legendary',
      'kLegendary': 'Legendary',
      'kMythic': 'Mythic',
      'kUltimate': 'Ultimate',
      'kTranscendent': 'Transcendent',
      'kExalted': 'Exalted'
    }

    return rarityMap[rarity] || 'Regular'
  }

  /**
   * Generate chroma image URL using Community Dragon chromaPath
   */
  private generateChromaImageUrl(chroma: any, championKey: string): string {
    // Use chromaPath from Community Dragon skins.json if available
    if (chroma.chromaPath) {
      // Extract the path after "/v1/" and convert to lowercase
      const v1Index = chroma.chromaPath.indexOf('/v1/')
      if (v1Index !== -1) {
        const pathAfterV1 = chroma.chromaPath.substring(v1Index + 1) // +1 to remove the leading slash
        const lowercasePath = pathAfterV1.toLowerCase()

        // Construct the final Community Dragon URL
        const baseUrl = 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default'
        const finalUrl = `${baseUrl}/${lowercasePath}`

        return finalUrl
      }
    }

    // Fallback to the old method if chromaPath is not available
    console.warn(`No chromaPath found for chroma ${chroma.id}, using fallback URL`)
    return `https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/champion-chroma-images/${championKey}/${chroma.id}.png`
  }

  /**
   * Generate skin image URL for default chromas
   */
  private generateSkinImageUrl(skinData: CommunityDragonSkin): string {
    // For default chromas, use the skin's tile path or splash path
    if (skinData.tilePath) {
      const cleanPath = skinData.tilePath.startsWith('/') ? skinData.tilePath.slice(1) : skinData.tilePath
      return `https://raw.communitydragon.org/latest/game/${cleanPath}`
    }

    if (skinData.splashPath) {
      const cleanPath = skinData.splashPath.startsWith('/') ? skinData.splashPath.slice(1) : skinData.splashPath
      return `https://raw.communitydragon.org/latest/game/${cleanPath}`
    }

    return '/placeholder.svg'
  }

  /**
   * Determine chroma price
   */
  private determineChromaPrice(chroma: any, skinData: CommunityDragonSkin): string {
    // Most chromas cost 290 RP
    return '290 RP'
  }

  /**
   * Determine chroma availability
   */
  private determineChromaAvailability(chroma: any, skinData: CommunityDragonSkin): string {
    if (skinData.isLegacy) {
      return 'Legacy'
    }
    return 'Available'
  }

  /**
   * Extract chroma description from Community Dragon chroma data
   */
  private extractChromaDescription(chroma: any): string | undefined {
    // Method 1: Check for direct description property
    if (chroma.description && typeof chroma.description === 'string' && chroma.description.trim()) {
      return chroma.description.trim()
    }

    // Method 2: Check descriptions array for 'riot' region
    if (chroma.descriptions && Array.isArray(chroma.descriptions) && chroma.descriptions.length > 0) {
      // Look for 'riot' region first, fallback to first available
      const riotDescription = chroma.descriptions.find((d: any) => d.region === 'riot')
      const description = riotDescription || chroma.descriptions[0]

      if (description && description.description && typeof description.description === 'string' && description.description.trim()) {
        return description.description.trim()
      }
    }

    return undefined
  }

  /**
   * Extract skin set/line from skin name
   */
  private extractSkinSet(skinName: string): string | undefined {
    // Common skin line patterns
    const skinLinePatterns = [
      'PROJECT', 'Star Guardian', 'K/DA', 'True Damage', 'Elderwood', 'Spirit Blossom',
      'Battle Academia', 'Pulsefire', 'Cosmic', 'Dark Cosmic', 'High Noon', 'Pool Party',
      'Arcade', 'Battle Boss', 'Mecha', 'Super Galaxy', 'Odyssey', 'Blood Moon',
      'Lunar Revel', 'Snowdown', 'Harrowing', 'Championship', 'Victorious', 'Conqueror'
    ]

    for (const pattern of skinLinePatterns) {
      if (skinName.includes(pattern)) {
        return pattern
      }
    }

    return undefined
  }

  /**
   * Format release date timestamp to readable format
   */
  private formatReleaseDate(timestamp: number): string {
    try {
      const date = new Date(timestamp)
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    } catch (error) {
      console.warn('⚠️ Failed to format release date:', timestamp)
      return 'Unknown'
    }
  }
}

// Export singleton instance
export const chromaService = new ChromaService()

export async function getChromaDetails(slug: string): Promise<ChromaDetails | null> {
  const allChromas = await chromaService.getAllChromas();
  
  const foundChroma = allChromas.find(c => {
    try {
      const generatedUrl = createChromaURL(c);
      return generatedUrl === slug;
    } catch (error) {
      return false;
    }
  });

  if (!foundChroma) {
    return null;
  }

  const related = allChromas
    .filter(c => c.skinName === foundChroma.skinName && c.id !== foundChroma.id)
    .slice(0, 6);

  const totalChromasCount = allChromas.filter(c => c.skinName === foundChroma.skinName).length;

  let mythicCost = null;
  if (foundChroma.contentId) {
    mythicCost = await mythicCostService.getMythicItemDetails(foundChroma.contentId);
  }

  return {
    chroma: foundChroma,
    relatedChromas: related,
    totalChromasForSkin: totalChromasCount,
    mythicCost,
  };
}

export async function getAllChromas(): Promise<ProcessedChromaData[]> {
    return await chromaService.getAllChromas(true);
}
