"use client"

import { useState, useRef, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { ScrollArea } from "@/components/ui/scroll-area"
import { ChevronDown } from "lucide-react"

interface MultiSelectChromaColorsProps {
  selectedColors: string[]
  onSelectionChange: (colors: string[]) => void
  availableColors: string[]
  placeholder?: string
  className?: string
}

export function MultiSelectChromaColors({
  selectedColors,
  onSelectionChange,
  availableColors,
  placeholder = "Select colors...",
  className = ""
}: MultiSelectChromaColorsProps) {
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleColorToggle = (color: string) => {
    const newSelection = selectedColors.includes(color)
      ? selectedColors.filter(c => c !== color)
      : [...selectedColors, color]
    
    onSelectionChange(newSelection)
  }

  const clearAll = () => {
    onSelectionChange([])
  }

  // Get color hex value for display - only actual chroma color names
  const getColorHex = (colorName: string): string => {
    const colorMap: Record<string, string> = {
      // Multi-word chroma colors from store API
      'Rose Quartz': '#f7cac9',
      'Rose Gold': '#e8b4a0',
      'Pearl White': '#f8f6f0',
      'Midnight Blue': '#191970',
      'Forest Green': '#228b22',
      'Royal Blue': '#4169e1',
      'Deep Purple': '#663399',
      'Bright Yellow': '#ffff00',
      'Dark Red': '#8b0000',
      'Light Blue': '#add8e6',
      'Hot Pink': '#ff69b4',
      'Lime Green': '#32cd32',
      'Sky Blue': '#87ceeb',
      'Sea Green': '#2e8b57',
      'Fire Red': '#ff2500',
      'Ice Blue': '#b0e0e6',
      'Sun Yellow': '#ffd700',
      'Moon Silver': '#c0c0c0',
      'Star Gold': '#ffd700',
      'Night Black': '#0c0c0c',

      // Single word chroma colors that appear in actual chroma names
      'Citrine': '#e4d00a',
      'Emerald': '#50c878',
      'Ruby': '#e0115f',
      'Sapphire': '#0f52ba',
      'Amethyst': '#9966cc',
      'Obsidian': '#3c3c3c',
      'Turquoise': '#40e0d0',
      'Aquamarine': '#7fffd4',
      'Peridot': '#e6e200',
      'Tanzanite': '#4b0082'
    }
    return colorMap[colorName] || '#8b5cf6' // Default to purple if not found
  }

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Trigger Button */}
      <Button
        variant="outline"
        onClick={() => setIsOpen(!isOpen)}
        className="w-full justify-between bg-gray-900/40 border-gray-700/20 text-white hover:bg-gray-800/50 h-[40px] items-center"
      >
        <span className="truncate">
          {selectedColors.length > 0
            ? `Chroma Colors +${selectedColors.length}`
            : <span className="text-gray-400">{placeholder}</span>
          }
        </span>
        <ChevronDown className={`h-4 w-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </Button>



      {/* Dropdown */}
      {isOpen && (
        <div className="absolute top-full left-0 right-0 z-50 mt-1 bg-gray-900 border border-gray-700 rounded-md shadow-lg">
          <div className="p-2">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-300">Select Colors</span>
              {selectedColors.length > 0 && (
                <button
                  onClick={clearAll}
                  className="text-xs text-gray-400 hover:text-white"
                >
                  Clear all
                </button>
              )}
            </div>
            
            <ScrollArea className="h-48">
              <div className="space-y-1">
                {availableColors.map((color) => (
                  <div
                    key={color}
                    className="flex items-center space-x-2 p-2 hover:bg-gray-800/50 rounded cursor-pointer"
                    onClick={() => handleColorToggle(color)}
                  >
                    <Checkbox
                      checked={selectedColors.includes(color)}
                      onChange={() => handleColorToggle(color)}
                      className="pointer-events-none border-gray-600 data-[state=checked]:bg-purple-600 data-[state=checked]:border-purple-600"
                    />
                    <div
                      className="w-4 h-4 rounded-full border border-white/30 flex-shrink-0"
                      style={{ backgroundColor: getColorHex(color) }}
                    />
                    <span className="text-sm text-gray-300 flex-1">{color}</span>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>
        </div>
      )}
    </div>
  )
}
