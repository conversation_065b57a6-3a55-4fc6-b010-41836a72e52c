// Google Analytics utility functions

declare global {
  interface Window {
    gtag: (
      command: 'config' | 'event' | 'js' | 'set',
      targetId: string | Date,
      config?: Record<string, any>
    ) => void
  }
}

// Track page views
export const trackPageView = (url: string) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('config', 'G-36HPL7GSEF', {
      page_path: url,
    })
  }
}

// Track custom events
export const trackEvent = (
  action: string,
  category: string,
  label?: string,
  value?: number
) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', action, {
      event_category: category,
      event_label: label,
      value: value,
    })
  }
}

// Common event tracking functions for your LoL database
export const trackChampionView = (championName: string) => {
  trackEvent('view_champion', 'Champions', championName)
}

export const trackSkinView = (skinName: string, championName: string) => {
  trackEvent('view_skin', 'Skins', `${championName} - ${skinName}`)
}

export const trackItemView = (itemName: string) => {
  trackEvent('view_item', 'Items', itemName)
}

export const trackSearch = (searchTerm: string, resultCount: number) => {
  trackEvent('search', 'Search', searchTerm, resultCount)
}

export const trackFilterUsage = (filterType: string, filterValue: string) => {
  trackEvent('filter_usage', 'Filters', `${filterType}: ${filterValue}`)
}
