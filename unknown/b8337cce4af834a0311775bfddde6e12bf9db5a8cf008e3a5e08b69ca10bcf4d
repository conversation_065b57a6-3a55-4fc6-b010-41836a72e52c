import { leagueApiBrowser } from "@/lib/api/league-client-browser"
import { skinRarityService, SkinRarityService } from "@/lib/api/skin-rarity-service"
import { SkinService } from "@/lib/services/skin-service"
import { EnhancedSkinData } from "@/lib/types/league-api"
import { championStoreService } from "./services/champion-store-service"

// Transform API data to match component structure
export async function transformChampionData(champion: any) {
  // Special check for <PERSON><PERSON>'s Transcendent skins
  if (champion.name === 'Ahri') {
    const transcendentSkins = champion.skins?.filter((skin: any) =>
      skin.name?.toLowerCase().includes('risen legend') ||
      skin.name?.toLowerCase().includes('immortalized legend')
    )
  }

  // Fetch real chroma data using the rebuilt system
  const skinsWithChromas = champion.skins;

  // Fetch skin pricing data from the SkinService
  
  const skinService = SkinService.getInstance()
  const allSkinsData = await skinService.getAllSkins()

  // Create a map of skin names to pricing data for quick lookup
  const skinPriceMap = new Map()
  allSkinsData.forEach(skin => {
    skinPriceMap.set(skin.name, {
      price: skin.price,
      releaseDate: skin.releaseDate
    })
  })
  



  // Transform skins data with chroma integration and Community Dragon rarity data
  const transformedSkins = await Promise.all(champion.skins.map(async (skin: any, index: number): Promise<EnhancedSkinData> => {
    const isBase = skin.num === 0
    const skinWithChromas = skinsWithChromas[index]

    // Calculate Community Dragon skin ID
    const skinId = SkinRarityService.calculateSkinId(champion.key, skin.num)

    // Fetch Community Dragon rarity data
    let cdSkinData = null
    let rarityTier = 'Regular'
    let isLegacy = false

    try {
      cdSkinData = await skinRarityService.getSkinRarityData(skinId)
      if (cdSkinData) {
        rarityTier = SkinRarityService.normalizeRarity(cdSkinData.rarity)
        isLegacy = cdSkinData.isLegacy
        
      } else {
        
      }
    } catch (error) {
      console.warn(`⚠️ RARITY: Failed to fetch rarity data for skin ID ${skinId}:`, error)
      // Fall back to name-based tier determination
      rarityTier = determineSkinTier(skin.name)
    }

    // Generate in-game image URL if skin has chromas
    let inGameImageUrl = ''
    if (skinWithChromas.chromas && skinWithChromas.chromas.length > 0) {
      const championKey = champion.key
      inGameImageUrl = `https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/champion-chroma-images/${championKey}/${skinId}.png`
    }

    // Generate circular portrait URL using the enhanced LeagueApiClient method
    const skinName = skin.name === 'default' ? `Classic ${champion.name}` : skin.name

    // Use the LeagueApiClient method which handles all the special cases including Teemo's .asu_teemo.png pattern and Transcendent skins
    const circularPortraitUrl = await leagueApiBrowser.getChampionCircleUrl(champion.id, skin.num, skinName, champion.skins, champion.key)

    // Generate splash art URL using the new Community Dragon method
    const splashArtUrl = await leagueApiBrowser.getChampionSplashUrlNew(champion.id, skin.num, champion.key)

    // Determine price using only real API data - no fallbacks
    let skinPrice = 'Free'
    if (!isBase) {
      // Try to get real pricing from the skin service data
      const skinPriceData = skinPriceMap.get(skinName)
      if (skinPriceData && skinPriceData.price && skinPriceData.price !== 'N/A') {
        skinPrice = skinPriceData.price
      } else {
        // If not found in API, always show N/A
        skinPrice = 'N/A'
      }
    }

    return {
      name: skinName,
      price: skinPrice,
      rarity: rarityTier, // Keep for backward compatibility
      rarityTier: rarityTier, // New normalized rarity
      isLegacy: isLegacy,
      image: circularPortraitUrl,
      splashArt: splashArtUrl, // Community Dragon uncentered splash art
      inGameImage: inGameImageUrl,
      isBase,
      hasInGameImage: skinWithChromas.chromas && skinWithChromas.chromas.length > 0,
      chromas: skinWithChromas.chromas || [],
      skinNum: skin.num,
      communityDragonId: skinId
    }
  }))

  // Get champion resource type for cleaning ability text
  const resourceType = getChampionResourceType(champion)

  // Transform abilities
  const transformedAbilities = [
    {
      name: champion.passive.name,
      icon: null,
      iconImage: leagueApiBrowser.getChampionPassiveUrl(champion.passive.image.full),
      description: cleanAbilityText(champion.passive.description, resourceType),
      cooldown: null,
      cost: null,
      isPassive: true
    },
    ...champion.spells.map((spell: any) => ({
      key: ['Q', 'W', 'E', 'R'][champion.spells.indexOf(spell)],
      name: spell.name,
      icon: null,
      iconImage: leagueApiBrowser.getChampionSpellUrl(spell.image.full),
      description: cleanAbilityText(spell.description, resourceType),
      cooldown: spell.cooldownBurn,
      cost: cleanCostText(spell.costBurn, spell.costType, resourceType),
      isPassive: false
    }))
  ]

  // Fetch real champion pricing data
  let championPricing = {
    costBE: 6300, // Default fallback
    costRP: 975,  // Default fallback
    releaseDate: 'Unknown'
  }

  try {
    championPricing = await championStoreService.getChampionPricing(champion.name)
  } catch (error) {
    console.error(`❌ TRANSFORMER: Failed to fetch store data for ${champion.name}:`, error)
    console.warn(`⚠️ TRANSFORMER: Using default pricing for ${champion.name}`)
  }

  return {
    championInfo: {
      name: champion.name,
      title: champion.title,
      role: 'Unknown', // Role will be determined by recommended positions API
      releaseDate: championPricing.releaseDate,
      costBE: championPricing.costBE,
      costRP: championPricing.costRP
    },
    championStats: {
      difficulty: champion.info.difficulty,
      attack: champion.info.attack,
      defense: champion.info.defense,
      magic: champion.info.magic,
      health: Math.round(champion.stats.hp),
      mana: Math.round(champion.stats.mp),
      attackDamage: Math.round(champion.stats.attackdamage),
      attackSpeed: champion.stats.attackspeed,
      armor: Math.round(champion.stats.armor),
      magicResist: Math.round(champion.stats.spellblock),
      movementSpeed: Math.round(champion.stats.movespeed)
    },
    abilities: transformedAbilities,
    skins: transformedSkins,
    lore: champion.lore || champion.blurb || 'No lore available.',
    tags: champion.tags
  }
}

// Determine skin tier based on name patterns (used for rarity display only)
function determineSkinTier(skinName: string): string {
  const name = skinName.toLowerCase()

  if (name === 'default' || name.includes('classic')) return 'Base'
  if (name.includes('risen legend') || name.includes('immortalized legend')) return 'Transcendent'
  if (name.includes('legendary') || name.includes('god') || name.includes('project')) return 'Legendary'
  if (name.includes('epic') || name.includes('star guardian') || name.includes('arcade')) return 'Epic'
  if (name.includes('ultimate') || name.includes('pulsefire')) return 'Ultimate'
  if (name.includes('mythic') || name.includes('prestige')) return 'Mythic'
  if (name.includes('victorious') || name.includes('championship')) return 'Victorious'

  return 'Regular'
}

// Function to determine champion resource type
function getChampionResourceType(champion: any): string {
  const championId = champion.id.toLowerCase()

  const energyChampions = ['akali', 'kennen', 'shen', 'zed', 'leesin']
  if (energyChampions.includes(championId)) return 'Energy'

  if (championId === 'yasuo' || championId === 'yone') return 'Flow'
  if (championId === 'rumble') return 'Heat'

  const furyChampions = ['gnar', 'shyvana', 'rengar', 'renekton', 'tryndamere']
  if (furyChampions.includes(championId)) return 'Fury'

  if (championId === 'rengar') return 'Ferocity'
  if (championId === 'vladimir' || championId === 'mordekaiser') return 'Health'

  const noCostChampions = ['garen', 'katarina', 'riven', 'yasuo', 'yone']
  if (noCostChampions.includes(championId) || champion.stats.mp === 0) return 'No Cost'

  return 'Mana'
}

// Function to clean up ability descriptions
function cleanAbilityText(text: string, resourceType: string): string {
  if (!text) return text

  return text
    .replace(/\{\{\s*abilityresourcename\s*\}\}/gi, resourceType)
    .replace(/\{\{\s*cost\s*\}\}/gi, 'Cost')
    .replace(/\{\{\s*cooldown\s*\}\}/gi, 'Cooldown')
    .replace(/\{\{\s*range\s*\}\}/gi, 'Range')
    .replace(/\{\{\s*damage\s*\}\}/gi, 'Damage')
    .replace(/\{\{\s*effect\d*amount\d*\s*\}\}/gi, 'Effect')
    .replace(/\{\{\s*spell\.\w+\s*\}\}/gi, '')
    .replace(/\{\{\s*[^}]+\s*\}\}/gi, '') // Remove any remaining placeholders (improved pattern)
    .replace(/<br\s*\/?>/gi, ' ') // Remove <br> tags and replace with space
    .replace(/<\/?[^>]+(>|$)/g, '') // Remove any other HTML tags
    .replace(/\s+/g, ' ') // Replace multiple spaces with single space
    .trim() // Remove leading/trailing whitespace
}

// Function to clean up cost text specifically
function cleanCostText(costBurn: string, costType: string, resourceType: string): string {
  if (!costBurn && !costType) return 'No Cost'

  // Handle "No Cost" champions
  if (resourceType === 'No Cost' || costType === 'No Cost') {
    return 'No Cost'
  }

  // Handle zero cost
  if (costBurn === '0' || !costBurn) {
    return 'No Cost'
  }

  // Build cost string
  let costText = costBurn
  if (costType && costType !== 'No Cost') {
    costText += ` ${costType}`
  }

  return cleanAbilityText(costText, resourceType)
}
