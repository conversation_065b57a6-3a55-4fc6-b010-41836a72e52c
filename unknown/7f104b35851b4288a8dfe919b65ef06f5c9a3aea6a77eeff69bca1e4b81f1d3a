import { chromaService } from '@/lib/services/chroma-service'
import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const query = searchParams.get('q') || ''
    const championKey = searchParams.get('champion')
    const skinId = searchParams.get('skinId')

    

    let chromas

    if (championKey) {
      // Get chromas by champion
      chromas = await chromaService.getChromasByChampion(championKey)
    } else if (skinId) {
      // Get chromas by skin
      chromas = await chromaService.getChromasBySkin(parseInt(skinId))
    } else if (query) {
      // Search chromas by query
      chromas = await chromaService.searchChromas(query)
    } else {
      // Return all chromas if no specific filter
      chromas = await chromaService.getAllChromas()
    }

    
    
    return NextResponse.json({
      success: true,
      data: chromas,
      count: chromas.length,
      query: {
        search: query,
        champion: champion<PERSON><PERSON>,
        skinId: skinId
      },
      timestamp: new Date().toISOString()
    }, {
      headers: {
        'Cache-Control': 'public, s-maxage=1800, stale-while-revalidate=3600', // 30min cache, 1h stale
        'CDN-Cache-Control': 'public, s-maxage=1800',
        'Vercel-CDN-Cache-Control': 'public, s-maxage=1800'
      }
    })

  } catch (error) {
    console.error('❌ Failed to search chromas:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Failed to search chromas',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}
