/**
 * Inventory Type IDs for League of Legends items
 * These IDs are used to identify different types of items in the store APIs
 */

export const INVENTORY_TYPE_IDS = {
  SUMMONER_ICON: "bb69c4a5-35a6-423c-a61a-a1eb4ca1aae7",
  CHAMPION_SKIN: "d620ae7b-cf87-496c-96c5-893c99dc2791",
  CHAMPION: "7fdbb3e4-c36f-4b3c-9088-b794ff127ff3",
  WARD_SKIN: "93b91332-509a-4848-b534-8facbdc94315",
  EMOTE: "5cee03fb-af16-4bc9-89f7-064b49e1233f",
  NEXUS_FINISHER: "2a858302-542f-454c-8bfc-5dbddbc7ff8b"
} as const

/**
 * Type for inventory type ID values
 */
export type InventoryTypeId = typeof INVENTORY_TYPE_IDS[keyof typeof INVENTORY_TYPE_IDS]

/**
 * Reverse mapping from ID to type name
 */
export const INVENTORY_TYPE_NAMES: Record<string, string> = {
  [INVENTORY_TYPE_IDS.SUMMONER_ICON]: "SUMMONER_ICON",
  [INVENTORY_TYPE_IDS.CHAMPION_SKIN]: "CHAMPION_SKIN", 
  [INVENTORY_TYPE_IDS.CHAMPION]: "CHAMPION",
  [INVENTORY_TYPE_IDS.WARD_SKIN]: "WARD_SKIN",
  [INVENTORY_TYPE_IDS.EMOTE]: "EMOTE",
  [INVENTORY_TYPE_IDS.NEXUS_FINISHER]: "NEXUS_FINISHER"
} as const

/**
 * Helper function to get inventory type name from ID
 */
export function getInventoryTypeName(id: string): string {
  return INVENTORY_TYPE_NAMES[id] || "UNKNOWN"
}

/**
 * Helper function to get inventory type ID from name
 */
export function getInventoryTypeId(name: keyof typeof INVENTORY_TYPE_IDS): string {
  return INVENTORY_TYPE_IDS[name]
}
