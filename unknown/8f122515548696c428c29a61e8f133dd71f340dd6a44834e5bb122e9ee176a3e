"use client"

import SharedLayout from "@/components/shared-layout"
import { Card, CardContent } from "@/components/ui/card"
import { FileText } from "lucide-react"

export default function PatchNotesPage() {
  return (
    <SharedLayout>
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 pb-4 sm:pb-6 lg:pb-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-4">
            <FileText className="h-8 w-8 text-yellow-400" />
            <h1 className="text-4xl font-bold text-white">Patch Notes</h1>
          </div>
          <p className="text-gray-300 text-lg">
            Stay updated with the latest game changes, balance updates, and exciting new features
          </p>
        </div>

        {/* No Patch Notes Message */}
        <div className="flex justify-center">
          <Card className="bg-gray-900/40 border-gray-700/20 max-w-md">
            <CardContent className="p-8 text-center">
              <div className="text-gray-400 mb-4">
                <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
              </div>
              <h3 className="text-lg font-semibold text-white mb-2">No Recent Blogs to Show</h3>
              <p className="text-gray-400 text-sm">
                We're working on bringing you the latest League of Legends content. Check back soon!
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </SharedLayout>
  )
}
