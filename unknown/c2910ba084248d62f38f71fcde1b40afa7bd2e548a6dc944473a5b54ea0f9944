"use client"

import * as React from "react"
import { Too<PERSON><PERSON>, Toolt<PERSON>Trigger, TooltipContent } from "./tooltip"
import { useMobileTooltip } from "@/hooks/use-mobile-tooltip"

interface MobileTooltipProps {
  children: React.ReactNode
  content: React.ReactNode
  disabled?: boolean
  side?: "top" | "right" | "bottom" | "left"
  sideOffset?: number
  className?: string
  contentClassName?: string
}

export function MobileTooltip({
  children,
  content,
  disabled = false,
  side = "top",
  sideOffset = 4,
  className,
  contentClassName
}: MobileTooltipProps) {
  const {
    isOpen,
    isMobile,
    handleClick,
    handleMouseEnter,
    handleMouseLeave
  } = useMobileTooltip({ disabled })

  return (
    <Tooltip open={isOpen}>
      <TooltipTrigger asChild>
        <div
          className={`inline-block ${isMobile ? 'cursor-pointer' : ''} ${className || ''}`}
          onClick={handleClick}
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}

          style={{ 
            WebkitTapHighlightColor: 'transparent',
            touchAction: 'manipulation'
          }}
        >
          {children}
        </div>
      </TooltipTrigger>
      <TooltipContent
        side={side}
        sideOffset={sideOffset}
        className={contentClassName}
        onPointerDownOutside={(e) => {
          // Prevent closing when clicking on the tooltip content itself
          if (isMobile) {
            e.preventDefault()
          }
        }}
      >
        {content}
      </TooltipContent>
    </Tooltip>
  )
}

// Enhanced version that works with existing Tooltip components
interface EnhancedTooltipProps {
  children: React.ReactNode
  disabled?: boolean
}

export function EnhancedTooltip({ children, disabled = false }: EnhancedTooltipProps) {
  const {
    isOpen,
    isMobile,
    handleClick,
    handleMouseEnter,
    handleMouseLeave
  } = useMobileTooltip({ disabled })

  // Clone the Tooltip and enhance its trigger
  return React.Children.map(children, (child) => {
    if (React.isValidElement(child) && child.type === Tooltip) {
      return React.cloneElement(child, {
        ...child.props,
        open: isOpen,
        children: React.Children.map(child.props.children, (tooltipChild) => {
          if (React.isValidElement(tooltipChild) && tooltipChild.type === TooltipTrigger) {
            return React.cloneElement(tooltipChild, {
              ...tooltipChild.props,
              asChild: true,
              children: React.cloneElement(tooltipChild.props.children, {
                ...tooltipChild.props.children.props,
                onClick: (e: React.MouseEvent) => {
                  handleClick(e)
                  tooltipChild.props.children.props.onClick?.(e)
                },
                onMouseEnter: (e: React.MouseEvent) => {
                  handleMouseEnter()
                  tooltipChild.props.children.props.onMouseEnter?.(e)
                },
                onMouseLeave: (e: React.MouseEvent) => {
                  handleMouseLeave()
                  tooltipChild.props.children.props.onMouseLeave?.(e)
                },

                style: {
                  ...tooltipChild.props.children.props.style,
                  WebkitTapHighlightColor: 'transparent',
                  touchAction: 'manipulation',
                  cursor: isMobile ? 'pointer' : tooltipChild.props.children.props.style?.cursor
                }
              })
            })
          }
          return tooltipChild
        })
      })
    }
    return child
  })
}
