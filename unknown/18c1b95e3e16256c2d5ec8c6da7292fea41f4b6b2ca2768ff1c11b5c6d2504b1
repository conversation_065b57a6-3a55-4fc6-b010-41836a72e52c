"use client"

import * as React from "react"
import { ArrowU<PERSON>, ArrowDown } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { SortField, SortDirection } from "@/hooks/use-chroma-filters"

interface SortSelectProps {
  sortField: SortField
  sortDirection: SortDirection
  onSortFieldChange: (field: SortField) => void
  onSortDirectionChange: (direction: SortDirection) => void
  className?: string
}

const sortFields: { value: SortField; label: string }[] = [
  { value: 'name', label: 'Alphabetical' },
  { value: 'release', label: 'Release Date' },
  { value: 'price', label: 'Price' }
]

export function SortSelect({
  sortField,
  sortDirection,
  onSortFieldChange,
  onSortDirectionChange,
  className
}: SortSelectProps) {
  const toggleDirection = () => {
    onSortDirectionChange(sortDirection === 'asc' ? 'desc' : 'asc')
  }

  return (
    <div className={cn("flex items-center gap-2", className)}>
      {/* Sort Field Dropdown */}
      <Select value={sortField} onValueChange={onSortFieldChange}>
        <SelectTrigger className="bg-gray-900/80 border-gray-700/50 text-white focus:border-purple-400/60 w-40">
          <div className="flex items-center gap-2">
            <img
              src="https://i.ibb.co/DHGS7WN7/filter-1.png"
              alt="Filter"
              className="h-4 w-4"
              style={{
                filter: 'brightness(0) saturate(100%) invert(61%) sepia(11%) saturate(200%) hue-rotate(200deg) brightness(80%) contrast(84%)'
              }}
            />
            <SelectValue placeholder="Sort by..." />
          </div>
        </SelectTrigger>
        <SelectContent className="bg-gray-900 border-gray-700/50">
          {sortFields.map((field) => (
            <SelectItem
              key={field.value}
              value={field.value}
              className="text-white hover:bg-purple-400/10 focus:bg-purple-400/10"
            >
              {field.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      {/* Direction Toggle Button */}
      <Button
        variant="outline"
        size="sm"
        onClick={toggleDirection}
        className="border-gray-700/50 text-white hover:bg-purple-400/10 hover:border-purple-400/50 p-2 h-10 w-10"
        title={sortDirection === 'asc' ? 'Switch to descending' : 'Switch to ascending'}
      >
        {sortDirection === 'asc' ? (
          <ArrowUp className="h-4 w-4" />
        ) : (
          <ArrowDown className="h-4 w-4" />
        )}
      </Button>
    </div>
  )
}
