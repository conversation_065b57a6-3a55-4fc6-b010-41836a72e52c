"use client"

import { But<PERSON> } from "@/components/ui/button"
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import Image from "next/image"

export function TooltipDemo() {
  return (
    <div className="flex flex-col space-y-6 p-8">
      <h2 className="text-2xl font-bold text-white mb-4">League of Legends Tooltips Demo</h2>
      
      <div className="flex flex-wrap gap-4">
        {/* Blue Essence Tooltip */}
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="flex items-center space-x-2 bg-black/70 backdrop-blur-md rounded-lg px-3 py-2 border border-gray-700/50 shadow-lg cursor-help">
              <Image
                src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-navigation/global/default/rewardicons_blueessence.png"
                alt="Blue Essence"
                width={20}
                height={20}
                className="w-5 h-5"
                style={{ userSelect: 'none', pointerEvents: 'none' }}
                draggable={false}
              />
              <span className="text-cyan-400 font-medium drop-shadow-md">6,300</span>
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <div className="text-center">
              <div className="font-semibold text-cyan-300">Blue Essence</div>
              <div className="text-sm text-gray-300 mt-1">
                Primary in-game currency earned from battle pass
              </div>
            </div>
          </TooltipContent>
        </Tooltip>

        {/* RP Tooltip */}
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="flex items-center space-x-2 bg-black/70 backdrop-blur-md rounded-lg px-3 py-2 border border-gray-700/50 shadow-lg cursor-help">
              <Image
                src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg"
                alt="Riot Points"
                width={20}
                height={20}
                className="w-5 h-5"
                style={{ userSelect: 'none', pointerEvents: 'none' }}
                draggable={false}
              />
              <span className="text-yellow-400 font-medium drop-shadow-md">880</span>
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <div className="text-center">
              <div className="font-semibold text-yellow-300">Riot Points (RP)</div>
              <div className="text-sm text-gray-300 mt-1">
                Premium currency for instant unlocks
              </div>
            </div>
          </TooltipContent>
        </Tooltip>

        {/* Skins Count Tooltip */}
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="flex items-center space-x-2 bg-black/70 backdrop-blur-md rounded-lg px-3 py-2 border border-gray-700/50 shadow-lg cursor-help">
              <Image
                src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/skins_rewards.svg"
                alt="Skins"
                width={20}
                height={20}
                className="w-5 h-5"
                style={{ userSelect: 'none', pointerEvents: 'none' }}
                draggable={false}
              />
              <span style={{ color: '#dfcda8' }} className="font-medium drop-shadow-md">12</span>
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <div className="text-center">
              <div className="font-semibold" style={{ color: '#dfcda8' }}>Skins Available</div>
              <div className="text-sm text-gray-300 mt-1">
                Total number of cosmetic skins for this champion
              </div>
            </div>
          </TooltipContent>
        </Tooltip>
      </div>

      <div className="mt-8">
        <h3 className="text-lg font-semibold text-white mb-4">Skin Rarity Examples</h3>
        <div className="flex flex-wrap gap-4">
          {/* Epic Rarity */}
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="w-4 h-4 rounded-full bg-black/80 backdrop-blur-sm flex items-center justify-center border border-gray-600/50 cursor-help">
                <Image
                  src="https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/cn-gem-3.png"
                  alt="Epic"
                  width={12}
                  height={12}
                  className="w-3 h-3"
                  style={{ userSelect: 'none', pointerEvents: 'none' }}
                  draggable={false}
                />
              </div>
            </TooltipTrigger>
            <TooltipContent>
              <div className="text-center">
                <div className="font-semibold text-orange-300">Epic</div>
                <div className="text-sm text-gray-300 mt-1">
                  High-quality skin with new model, textures, and effects
                </div>
              </div>
            </TooltipContent>
          </Tooltip>

          {/* Legendary Rarity */}
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="w-4 h-4 rounded-full bg-black/80 backdrop-blur-sm flex items-center justify-center border border-gray-600/50 cursor-help">
                <Image
                  src="https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/cn-gem-4.png"
                  alt="Legendary"
                  width={12}
                  height={12}
                  className="w-3 h-3"
                  style={{ userSelect: 'none', pointerEvents: 'none' }}
                  draggable={false}
                />
              </div>
            </TooltipTrigger>
            <TooltipContent>
              <div className="text-center">
                <div className="font-semibold text-orange-300">Legendary</div>
                <div className="text-sm text-gray-300 mt-1">
                  Premium skin with new voice lines and animations
                </div>
              </div>
            </TooltipContent>
          </Tooltip>

          {/* Mythic Rarity */}
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="w-4 h-4 rounded-full bg-black/80 backdrop-blur-sm flex items-center justify-center border border-gray-600/50 cursor-help">
                <Image
                  src="https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/cn-gem-8.png"
                  alt="Mythic"
                  width={12}
                  height={12}
                  className="w-3 h-3"
                  style={{ userSelect: 'none', pointerEvents: 'none' }}
                  draggable={false}
                />
              </div>
            </TooltipTrigger>
            <TooltipContent>
              <div className="text-center">
                <div className="font-semibold text-orange-300">Mythic</div>
                <div className="text-sm text-gray-300 mt-1">
                  Exclusive skin with unique prestige elements
                </div>
              </div>
            </TooltipContent>
          </Tooltip>
        </div>
      </div>

      <div className="mt-8">
        <p className="text-gray-400 text-sm">
          Hover over any of the elements above to see the tooltips in action!
        </p>
      </div>
    </div>
  )
}
