"use client"

import Link from "next/link"
import Image from "next/image"

import { But<PERSON> } from "@/components/ui/button"
import MobileNavWrapper from "./mobile-nav-wrapper"
import { useIsMobile } from "@/hooks/use-mobile"
import { TrendingUp } from "lucide-react"
import GlobalSearch from "./global-search"

interface ResponsiveHeaderProps {
  currentPath?: string
  searchButton?: React.ReactNode
  showGlobalSearch?: boolean
}

export default function ResponsiveHeader({ currentPath, searchButton, showGlobalSearch = false }: ResponsiveHeaderProps) {
  const isMobile = useIsMobile()

  return (
    <header className="border-b border-orange-800/20 bg-gray-950/90 backdrop-blur-md sticky top-0">
      <div className="max-w-full mx-auto px-4 py-4">
        <div className="flex items-center justify-between md:justify-between">
          {/* Mobile Navigation */}
          <div className="md:hidden">
            <MobileNavWrapper currentPath={currentPath} />
          </div>

          {/* Logo - responsive positioning */}
          <div className="flex items-center space-x-4 mobile-logo-container">
            <Link href="/" className="flex items-center space-x-3">
              {/* Mobile: Secondary logo only */}
              <Image
                src="/images/LoLDB-Logo-Secondary.svg"
                alt="LoLDB Logo"
                width={40}
                height={40}
                className="w-10 h-10 md:hidden"
              />
              {/* Desktop: Secondary logo + Text SVG */}
              <div className="hidden md:flex items-center space-x-3">
                <Image
                  src="/images/LoLDB-Logo-Secondary.svg"
                  alt="LoLDB Logo"
                  width={40}
                  height={40}
                  className="w-10 h-10"
                />
                <Image
                  src="/images/LoLDB-Logo-Text.svg"
                  alt="LoLDB Text"
                  width={80}
                  height={24}
                  className="h-6 w-auto"
                />
              </div>
            </Link>
          </div>

          {/* Right side - Global Search, Page Search, Sign In/Join Discord buttons */}
          <div className="flex items-center space-x-3">
            {/* Global Search - Mobile (opposite side of hamburger) */}
            {showGlobalSearch && (
              <div className="md:hidden">
                <GlobalSearch />
              </div>
            )}

            {/* Page-specific Mobile Search Button */}
            {searchButton && (
              <div className="lg:hidden">
                {searchButton}
              </div>
            )}

            {/* Desktop Global Search & Sign In/Join Discord Buttons */}
            <div className="hidden md:flex items-center space-x-3 responsive-header-buttons">
              {/* Global Search - Desktop (next to Join Discord) */}
              {showGlobalSearch && <GlobalSearch />}

              {/* Spacer for homepage to maintain consistent header height */}
              {!showGlobalSearch && (
                <div className="w-10 h-10 flex items-center justify-center">
                  {/* Invisible spacer that matches the visual weight of the search button */}
                </div>
              )}

              <Button
                size="sm"
                className="bg-[#5865f2] hover:bg-[#5865f2]/80 text-white border-[#5865f2] min-w-[100px]"
                onClick={() => window.open('https://discord.gg/NsuuwjPuZk', '_blank')}
              >
                <Image
                  src="/images/discord-icon-svgrepo-com.svg"
                  alt="Join Discord"
                  width={16}
                  height={16}
                  className="w-4 h-4 mr-2"
                />
                Join Discord
              </Button>
              <Button
                size="sm"
                disabled
                className="bg-gray-600 text-gray-400 min-w-[80px] cursor-not-allowed opacity-60"
              >
                <Image
                  src="/images/log-in-01-svgrepo-com.svg"
                  alt="Sign In"
                  width={16}
                  height={16}
                  className="w-4 h-4 mr-2 opacity-60"
                />
                Sign In
              </Button>
            </div>
          </div>
        </div>
      </div>
    </header>
  )
}
