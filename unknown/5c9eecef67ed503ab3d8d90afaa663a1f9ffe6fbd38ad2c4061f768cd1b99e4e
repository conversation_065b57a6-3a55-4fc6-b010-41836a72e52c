"use client"

import React, { useState, useRef, useEffect } from "react"
import Image from "next/image"
import { ChevronDown } from "lucide-react"
import { useIsMobile } from "@/hooks/use-mobile"

import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip"

// Mock data for different ranks with noticeable ups and downs (3-5% variations)
const mockWinRateData = {
  iron: [
    { day: "Mon", winRate: 46.5 },
    { day: "Tue", winRate: 50.2 },
    { day: "Wed", winRate: 47.8 },
    { day: "Thu", winRate: 51.5 },
    { day: "Fri", winRate: 48.1 },
    { day: "Sat", winRate: 52.3 },
    { day: "Sun", winRate: 49.7 }
  ],
  bronze: [
    { day: "Mon", winRate: 47.8 },
    { day: "Tue", winRate: 51.5 },
    { day: "Wed", winRate: 48.9 },
    { day: "Thu", winRate: 52.8 },
    { day: "Fri", winRate: 49.4 },
    { day: "Sat", winRate: 53.6 },
    { day: "Sun", winRate: 50.1 }
  ],
  silver: [
    { day: "Mon", winRate: 49.1 },
    { day: "Tue", winRate: 52.8 },
    { day: "Wed", winRate: 50.2 },
    { day: "Thu", winRate: 54.1 },
    { day: "Fri", winRate: 50.7 },
    { day: "Sat", winRate: 54.9 },
    { day: "Sun", winRate: 51.4 }
  ],
  gold: [
    { day: "Mon", winRate: 50.4 },
    { day: "Tue", winRate: 54.1 },
    { day: "Wed", winRate: 51.5 },
    { day: "Thu", winRate: 55.4 },
    { day: "Fri", winRate: 52.0 },
    { day: "Sat", winRate: 56.2 },
    { day: "Sun", winRate: 52.7 }
  ],
  platinum: [
    { day: "Mon", winRate: 51.7 },
    { day: "Tue", winRate: 55.4 },
    { day: "Wed", winRate: 52.8 },
    { day: "Thu", winRate: 56.7 },
    { day: "Fri", winRate: 53.3 },
    { day: "Sat", winRate: 57.5 },
    { day: "Sun", winRate: 54.0 }
  ],
  emerald: [
    { day: "Mon", winRate: 53.0 },
    { day: "Tue", winRate: 56.7 },
    { day: "Wed", winRate: 54.1 },
    { day: "Thu", winRate: 58.0 },
    { day: "Fri", winRate: 54.6 },
    { day: "Sat", winRate: 58.8 },
    { day: "Sun", winRate: 55.3 }
  ],
  diamond: [
    { day: "Mon", winRate: 54.3 },
    { day: "Tue", winRate: 58.0 },
    { day: "Wed", winRate: 55.4 },
    { day: "Thu", winRate: 59.3 },
    { day: "Fri", winRate: 55.9 },
    { day: "Sat", winRate: 60.0 },
    { day: "Sun", winRate: 56.6 }
  ],
  master: [
    { day: "Mon", winRate: 55.6 },
    { day: "Tue", winRate: 59.3 },
    { day: "Wed", winRate: 56.7 },
    { day: "Thu", winRate: 60.0 },
    { day: "Fri", winRate: 57.2 },
    { day: "Sat", winRate: 59.4 },
    { day: "Sun", winRate: 57.9 }
  ],
  grandmaster: [
    { day: "Mon", winRate: 56.9 },
    { day: "Tue", winRate: 60.0 },
    { day: "Wed", winRate: 58.0 },
    { day: "Thu", winRate: 59.9 },
    { day: "Fri", winRate: 58.5 },
    { day: "Sat", winRate: 59.7 },
    { day: "Sun", winRate: 59.2 }
  ],
  challenger: [
    { day: "Mon", winRate: 56.2 },
    { day: "Tue", winRate: 59.9 },
    { day: "Wed", winRate: 57.3 },
    { day: "Thu", winRate: 60.0 },
    { day: "Fri", winRate: 57.8 },
    { day: "Sat", winRate: 59.5 },
    { day: "Sun", winRate: 58.5 }
  ]
}

const ranks = [
  { name: "Iron", value: "iron" },
  { name: "Bronze", value: "bronze" },
  { name: "Silver", value: "silver" },
  { name: "Gold", value: "gold" },
  { name: "Platinum", value: "platinum" },
  { name: "Emerald", value: "emerald" },
  { name: "Diamond", value: "diamond" },
  { name: "Master", value: "master" },
  { name: "Grandmaster", value: "grandmaster" },
  { name: "Challenger", value: "challenger" }
]

function getRankIconUrl(rank: string): string {
  return `https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/ranked-mini-crests/${rank.toLowerCase()}.svg`
}

// Helper function to get mobile data (last 3 days)
function getMobileData(data: Array<{ day: string; winRate: number }>): Array<{ day: string; winRate: number }> {
  // Return the last 3 days from the 7-day data
  return data.slice(-3)
}

// Custom SVG Chart Component
const CustomLineChart = ({ data, isMobile }: { data: Array<{ day: string; winRate: number }>; isMobile: boolean }) => {
  const [hoveredPoint, setHoveredPoint] = useState<{ x: number; y: number; data: any; chartX: number } | null>(null)
  const [animationProgress, setAnimationProgress] = useState(0)

  // Responsive dimensions - optimized for mobile viewport
  const width = isMobile ? 350 : 600
  const height = isMobile ? 160 : 160 // Reduced height on mobile to fit viewport
  const padding = isMobile ? 50 : 40
  const topPadding = isMobile ? 25 : 20 // Reduced top padding on mobile
  const bottomPadding = isMobile ? 30 : 35 // Adequate space for day labels

  // Fixed Y-axis range - removed 65% to save space
  const minValue = 40
  const maxValue = 60 // Back to 60% max

  const xStep = (width - 2 * padding) / (data.length - 1)
  const yScale = (height - topPadding - bottomPadding) / (maxValue - minValue) // Use proper padding system

  const points = data.map((item, index) => ({
    x: padding + index * xStep,
    y: height - bottomPadding - (item.winRate - minValue) * yScale,
    data: item
  }))

  // Create animated path data based on progress
  const getAnimatedPathData = (progress: number) => {
    const animatedPoints = points.slice(0, Math.ceil(progress * points.length))
    if (animatedPoints.length === 0) return ''

    return animatedPoints.map((point, index) =>
      `${index === 0 ? 'M' : 'L'} ${point.x} ${point.y}`
    ).join(' ')
  }

  const pathData = getAnimatedPathData(animationProgress)

  // Animation effect on data change
  React.useEffect(() => {
    setAnimationProgress(0)
    const duration = 1500 // 1.5 seconds
    const startTime = Date.now()

    const animate = () => {
      const elapsed = Date.now() - startTime
      const progress = Math.min(elapsed / duration, 1)

      // Easing function for smooth animation
      const easeOutCubic = 1 - Math.pow(1 - progress, 3)
      setAnimationProgress(easeOutCubic)

      if (progress < 1) {
        requestAnimationFrame(animate)
      }
    }

    requestAnimationFrame(animate)
  }, [data])

  return (
    <div className="relative w-full h-full" style={{ overflow: 'visible' }}>
      <svg width="100%" height="100%" viewBox={`0 0 ${width} ${height}`} className="overflow-visible" style={{ overflow: 'visible' }}>
        {/* Grid lines */}
        {(isMobile ? [0, 1, 2] : [0, 1, 2, 3, 4]).map(i => {
          const totalLines = isMobile ? 2 : 4
          const availableHeight = isMobile ? (height - topPadding - bottomPadding) * 0.6 : (height - topPadding - bottomPadding)
          const y = topPadding + (i * availableHeight) / totalLines
          return (
            <line
              key={i}
              x1={padding}
              y1={y}
              x2={width - padding}
              y2={y}
              stroke="#334155"
              strokeWidth={1}
              opacity={0.3}
            />
          )
        })}

        {/* Y-axis labels - Properly centered */}
        {(isMobile ? [60, 50, 40] : [60, 55, 50, 45, 40]).map((value, i) => {
          const totalLines = isMobile ? 2 : 4
          const availableHeight = isMobile ? (height - topPadding - bottomPadding) * 0.6 : (height - topPadding - bottomPadding)
          const y = topPadding + (i * availableHeight) / totalLines
          return (
            <text
              key={i}
              x={padding - 10}
              y={y + 4}
              fill="#94a3b8"
              fontSize={isMobile ? "14" : "12"}
              textAnchor="end"
            >
              {value}%
            </text>
          )
        })}

        {/* X-axis labels - Properly positioned inside the card */}
        {data.map((item, index) => {
          const x = padding + index * xStep
          return (
            <text
              key={index}
              x={x}
              y={height - (isMobile ? 12 : 10)} // Positioned to be fully visible on mobile
              fill="#94a3b8"
              fontSize={isMobile ? "16" : "12"}
              textAnchor="middle"
            >
              {item.day}
            </text>
          )
        })}

        {/* Vertical hover line */}
        {hoveredPoint && (
          <line
            x1={hoveredPoint.chartX}
            y1={topPadding} // Match the top of the chart area
            x2={hoveredPoint.chartX}
            y2={height - bottomPadding}
            stroke="#ea580c"
            strokeWidth={2}
            strokeDasharray="4,4"
            opacity={0.7}
          />
        )}

        {/* Animated line path */}
        <path
          d={pathData}
          fill="none"
          stroke="#ea580c"
          strokeWidth={isMobile ? 4 : 3}
          strokeLinecap="round"
          strokeLinejoin="round"
          style={{
            transition: 'all 0.3s ease-out'
          }}
        />

        {/* Animated data points */}
        {points.map((point, index) => {
          const shouldShow = index < Math.ceil(animationProgress * points.length)
          const baseRadius = isMobile ? 6 : 4
          const hoverRadius = isMobile ? 8 : 6
          return (
            <circle
              key={index}
              cx={point.x}
              cy={point.y}
              r={hoveredPoint?.data === point.data ? hoverRadius : baseRadius}
              fill="#ea580c"
              stroke="#c2410c"
              strokeWidth={isMobile ? 3 : 2}
              className="cursor-pointer transition-all duration-200"
              style={{
                opacity: shouldShow ? 1 : 0,
                transform: shouldShow ? 'scale(1)' : 'scale(0)',
                transition: 'all 0.3s ease-out'
              }}
              onMouseEnter={(e) => {
                if (!shouldShow) return

                const svgElement = e.currentTarget.closest('svg')
                const containerElement = svgElement?.parentElement
                if (containerElement) {
                  const containerRect = containerElement.getBoundingClientRect()
                  const pointRect = e.currentTarget.getBoundingClientRect()

                  // Calculate tooltip position - centered horizontally above the point
                  const tooltipX = pointRect.left - containerRect.left + pointRect.width / 2
                  const tooltipY = pointRect.top - containerRect.top - 80 // Position well above the point with clear gap

                  setHoveredPoint({
                    x: tooltipX,
                    y: tooltipY,
                    data: point.data,
                    chartX: point.x
                  })
                }
              }}
              onMouseLeave={() => setHoveredPoint(null)}
            />
          )
        })}
      </svg>

      {/* Custom tooltip */}
      {hoveredPoint && (
        <div
          className={`absolute z-10 bg-gray-800/95 border border-gray-700/50 rounded-lg ${isMobile ? 'px-4 py-3' : 'px-3 py-2'} shadow-lg backdrop-blur-sm pointer-events-none whitespace-nowrap`}
          style={{
            left: hoveredPoint.x,
            top: hoveredPoint.y,
            transform: 'translateX(-50%)'
          }}
        >
          <div className="text-center">
            <p className={`text-gray-100 ${isMobile ? 'text-base' : 'text-sm'} font-medium`}>{hoveredPoint.data.day}</p>
            <p className={`text-orange-400 ${isMobile ? 'text-base' : 'text-sm'}`}>
              Win Rate: {hoveredPoint.data.winRate.toFixed(1)}%
            </p>
          </div>
        </div>
      )}
    </div>
  )
}

interface ChampionWinRateGraphProps {
  championName?: string
}

export function ChampionWinRateGraph({ championName = "Champion" }: ChampionWinRateGraphProps) {
  const [selectedRank, setSelectedRank] = useState("gold")
  const [dropdownOpen, setDropdownOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)
  const isMobile = useIsMobile()

  // Handle click outside to close dropdown
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setDropdownOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  const fullData = mockWinRateData[selectedRank as keyof typeof mockWinRateData]
  const currentData = isMobile ? getMobileData(fullData) : fullData
  const selectedRankData = ranks.find(rank => rank.value === selectedRank)
  const averageWinRate = currentData.reduce((sum, data) => sum + data.winRate, 0) / currentData.length

  return (
    <div className={`bg-black/70 backdrop-blur-md rounded-lg border border-orange-700/40 p-6 shadow-xl ${isMobile ? 'h-96' : 'h-80'} flex flex-col relative win-rate-graph`} style={{ overflow: 'visible' }}>
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        <div>
          <h3 className="text-xl font-bold text-white mb-1">Win Rate Trends</h3>
          <p className="text-gray-400 text-sm">
            {championName} win rates over the past {isMobile ? '3 days' : 'week'}
          </p>
        </div>
        
        {/* Rank Selector */}
        <div className="relative" ref={dropdownRef}>
          <button
            onClick={() => setDropdownOpen(!dropdownOpen)}
            className="flex items-center space-x-2 bg-gray-800/70 hover:bg-gray-700/70 border border-gray-600/50 rounded-lg px-3 py-2 transition-all duration-200 w-40 justify-between"
            style={{
              WebkitTapHighlightColor: 'transparent',
              touchAction: 'manipulation'
            }}
          >
            <div className="flex items-center space-x-2">
              <Image
                src={getRankIconUrl(selectedRank)}
                alt={selectedRankData?.name || "Rank"}
                width={20}
                height={20}
                className="w-5 h-5"
              />
              <span className="text-white text-sm font-medium">
                {selectedRankData?.name}
              </span>
            </div>
            <ChevronDown
              size={16}
              className={`text-gray-400 transition-transform ${dropdownOpen ? 'rotate-180' : ''}`}
            />
          </button>

          {dropdownOpen && (
            <div
              className="absolute top-full left-0 right-0 mt-1 bg-gray-800 border border-gray-700 rounded-lg shadow-lg z-[99999] overflow-hidden"
              style={{
                backgroundColor: 'rgb(31, 41, 55) !important',
                background: 'rgb(31, 41, 55) !important',
                opacity: '1 !important',
                maxHeight: '200px',
                overflowY: 'auto'
              }}
            >
              {ranks.map((rank) => (
                <button
                  key={rank.value}
                  onClick={() => {
                    setSelectedRank(rank.value)
                    setDropdownOpen(false)
                  }}
                  className="w-full flex items-center space-x-2 px-3 py-2 text-white hover:bg-gray-700/50 transition-colors text-left"
                >
                  <Image
                    src={getRankIconUrl(rank.value)}
                    alt={rank.name}
                    width={16}
                    height={16}
                    className="w-4 h-4"
                  />
                  <span className="text-sm">{rank.name}</span>
                </button>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Average Win Rate Display */}
      <div className="mb-2">
        <div className="flex items-center space-x-2">
          <span className="text-gray-400 text-sm">Average Win Rate:</span>
          <span className={`text-lg font-bold ${averageWinRate >= 50 ? 'text-green-400' : 'text-red-400'}`}>
            {averageWinRate.toFixed(1)}%
          </span>
        </div>
      </div>

      {/* Chart */}
      <div className="flex-1 relative" style={{ overflow: 'visible' }}>
        <CustomLineChart key={selectedRank} data={currentData} isMobile={isMobile} />
      </div>
    </div>
  )
}
