import { skinRarityService } from '@/lib/api/skin-rarity-service'
import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const skinIds = searchParams.get('skinIds')
    const skinId = searchParams.get('skinId')
    const refresh = searchParams.get('refresh') === 'true'

    

    // Clear cache if refresh is requested
    if (refresh) {
      skinRarityService.clearCache()
      
    }

    // Handle single skin ID request
    if (skinId) {
      const id = parseInt(skinId)
      if (isNaN(id)) {
        return NextResponse.json({
          success: false,
          error: 'Invalid skin ID provided'
        }, { status: 400 })
      }

      const skinData = await skinRarityService.getSkinRarityData(id)
      
      return NextResponse.json({
        success: true,
        data: skinData,
        timestamp: new Date().toISOString()
      }, {
        headers: {
          'Cache-Control': 'public, s-maxage=86400, stale-while-revalidate=172800', // 24h cache, 48h stale
          'CDN-Cache-Control': 'public, s-maxage=86400',
          'Vercel-CDN-Cache-Control': 'public, s-maxage=86400'
        }
      })
    }

    // Handle multiple skin IDs request
    if (skinIds) {
      const ids = skinIds.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id))
      
      if (ids.length === 0) {
        return NextResponse.json({
          success: false,
          error: 'No valid skin IDs provided'
        }, { status: 400 })
      }

      const skinsData = await skinRarityService.getMultipleSkinRarityData(ids)
      
      return NextResponse.json({
        success: true,
        data: skinsData,
        count: Object.keys(skinsData).length,
        timestamp: new Date().toISOString()
      }, {
        headers: {
          'Cache-Control': 'public, s-maxage=86400, stale-while-revalidate=172800',
          'CDN-Cache-Control': 'public, s-maxage=86400',
          'Vercel-CDN-Cache-Control': 'public, s-maxage=86400'
        }
      })
    }

    // Handle request for all skins rarity data
    const allSkinsData = await skinRarityService.getAllSkinsRarityData()
    
    
    
    return NextResponse.json({
      success: true,
      data: allSkinsData,
      count: Object.keys(allSkinsData).length,
      timestamp: new Date().toISOString()
    }, {
      headers: {
        'Cache-Control': 'public, s-maxage=86400, stale-while-revalidate=172800',
        'CDN-Cache-Control': 'public, s-maxage=86400',
        'Vercel-CDN-Cache-Control': 'public, s-maxage=86400'
      }
    })

  } catch (error) {
    console.error('❌ Error fetching skin rarity data:', error)

    return NextResponse.json({
      success: false,
      error: 'Failed to fetch skin rarity data',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, {
      status: 500,
      headers: {
        'Cache-Control': 'no-cache'
      }
    })
  }
}

// Handle cache management operations
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action } = body

    switch (action) {
      case 'clear-cache':
        skinRarityService.clearCache()
        return NextResponse.json({
          success: true,
          message: 'Skin rarity cache cleared successfully'
        })

      case 'get-stats':
        const stats = skinRarityService.getCacheStats()
        return NextResponse.json({
          success: true,
          data: stats
        })

      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid action. Supported actions: clear-cache, get-stats'
        }, { status: 400 })
    }

  } catch (error) {
    console.error('❌ Error handling skin rarity cache operation:', error)

    return NextResponse.json({
      success: false,
      error: 'Failed to perform cache operation',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
