import { useState, useMemo, useCallback, useEffect } from 'react'
import { ProcessedSkinData } from '@/lib/services/skin-service'
import { MythicCostData } from '@/lib/services/mythic-cost-service'
import { matchesSearch } from '@/lib/utils/search-utils'
import { useSearchWithSuggestions, SearchWithSuggestionsReturn } from './use-search-with-suggestions'

export type SortField = 'name' | 'release' | 'price'
export type SortDirection = 'asc' | 'desc'

export interface SkinFilters {
  search: string
  tiers: string[] // Array of selected tiers including 'Legacy'
  champions: string[] // Array of selected champions
  skinLines: string[] // Array of selected skin lines
  sortField: SortField // What to sort by
  sortDirection: SortDirection // Sort direction
}

export interface SkinFiltersInternal extends SkinFilters {
  debouncedSearch: string // Internal debounced search term
}

interface UseSkinFiltersReturn {
  filteredSkins: ProcessedSkinData[]
  filters: SkinFilters
  updateFilters: (newFilters: Partial<SkinFilters>) => void
  resetFilters: () => void
  availableChampions: string[]
  availableTiers: string[]
  availableSkinLines: string[]
  isSearching: boolean // Indicates if search is being debounced
  searchSuggestions: string[] // "Did You Mean?" suggestions
  hasSearchResults: boolean // Whether current search has results
  searchHook: SearchWithSuggestionsReturn<ProcessedSkinData> // Direct access to search hook
}

const defaultFilters: SkinFilters = {
  search: '',
  tiers: [],
  champions: [],
  skinLines: [],
  sortField: 'name',
  sortDirection: 'asc'
}

const defaultInternalFilters: SkinFiltersInternal = {
  ...defaultFilters,
  debouncedSearch: ''
}

export function useSkinFilters(skins: ProcessedSkinData[], mythicPrices?: Map<string, MythicCostData | null>): UseSkinFiltersReturn {
  const [filters, setFilters] = useState<SkinFiltersInternal>(defaultInternalFilters)

  // Use the reusable search hook for search with suggestions
  const searchHook = useSearchWithSuggestions({
    items: skins,
    getSearchableText: (skin) => [skin.name, skin.champion, skin.tier, ...(skin.skinLines || [])],
    getAvailableTerms: (skins) => {
      const terms = new Set<string>()
      skins.forEach(skin => {
        terms.add(skin.name)
        terms.add(skin.champion)
        terms.add(skin.tier)
        if (skin.skinLines) {
          skin.skinLines.forEach(line => terms.add(line))
        }
      })
      return Array.from(terms).filter(term => term && term.trim().length > 0)
    },
    debounceMs: 300,
    maxSuggestions: 3,
    minSimilarity: 0.4
  })

  // Initialize search hook with current filter value
  useEffect(() => {
    if (filters.search && !searchHook.searchQuery) {
      searchHook.setSearchQuery(filters.search)
    }
  }, []) // Only run once on mount

  // Get available filter options
  const availableChampions = useMemo(() => {
    const champions = Array.from(new Set(skins.map(skin => skin.champion)))
    return champions.sort()
  }, [skins])

  const availableTiers = useMemo(() => {
    const tiers = Array.from(new Set(skins.map(skin => skin.tier)))
    // Define the specific order for rarities
    const tierOrder = ['Legacy', 'Regular', 'Epic', 'Legendary', 'Mythic', 'Ultimate', 'Exalted', 'Transcendent']

    // Filter and sort tiers according to the defined order
    const orderedTiers = tierOrder.filter(tier => tier === 'Legacy' || tiers.includes(tier))

    return orderedTiers
  }, [skins])

  const availableSkinLines = useMemo(() => {
    const skinLinesSet = new Set<string>()
    skins.forEach(skin => {
      if (skin.skinLines && skin.skinLines.length > 0) {
        skin.skinLines.forEach(skinLine => {
          skinLinesSet.add(skinLine)
        })
      }
    })
    return Array.from(skinLinesSet).sort()
  }, [skins])

  // Helper function to get price value for sorting
  // Price (High to Low) hierarchy: 1. Transcendent, 2. Exalted, 3. Mythic (with actual price), 4. 150k BE, 5. Rest of RP skins
  const getPriceValue = useCallback((skin: ProcessedSkinData): number => {
    const price = skin.price
    if (price === 'N/A' || !price) return Infinity // Put items with no price at the end

    // 1. Transcendent Skins - Highest priority
    if (skin.tier === 'Transcendent') {
      return 1000000 // Highest value to ensure they appear first
    }

    // 2. Exalted Skins - Second highest priority
    if (skin.tier === 'Exalted') {
      const numericPrice = parseInt(price.replace(/[^\d]/g, ''))
      return 900000 + (isNaN(numericPrice) ? 0 : numericPrice) // High base value + actual price
    }

    // 3. Mythic Skins (with actual price, not N/A) - Third highest priority
    if (skin.tier === 'Mythic' && mythicPrices && skin.contentId) {
      const mythicPrice = mythicPrices.get(skin.contentId)
      if (mythicPrice && mythicPrice.cost) {
        // Mythic Essence prices are treated as high value but below Transcendent and Exalted
        return 800000 + mythicPrice.cost
      }
    }

    // 4. 150k Blue Essence Skins (URFWick) - Fourth highest priority
    if (price.includes('BE')) {
      const numericPrice = parseInt(price.replace(/[^\d]/g, ''))
      if (!isNaN(numericPrice) && numericPrice >= 150000) {
        // 150k BE skin gets priority above regular RP skins
        return 700000 // High value to ensure it appears before regular RP skins
      }
      // Other BE skins (lower than 150k) get lower priority
      return isNaN(numericPrice) ? Infinity : numericPrice / 100
    }

    // 5. Ultimate Skins - After the special categories above
    if (skin.tier === 'Ultimate') {
      const numericPrice = parseInt(price.replace(/[^\d]/g, ''))
      return isNaN(numericPrice) ? Infinity : numericPrice
    }

    // 6. Regular RP skins - sorted by actual price (high to low)
    const numericPrice = parseInt(price.replace(/[^\d]/g, ''))
    return isNaN(numericPrice) ? Infinity : numericPrice
  }, [mythicPrices])

  // Helper function to sort skins
  const sortSkins = useCallback((skinsToSort: ProcessedSkinData[], sortField: SortField, sortDirection: SortDirection, isDefaultState: boolean = false): ProcessedSkinData[] => {
    return [...skinsToSort].sort((a, b) => {
      // Only keep upcoming skins at the top when in default state (no custom sorting/filtering)
      if (isDefaultState) {
        if (a.isUpcoming && !b.isUpcoming) return -1
        if (!a.isUpcoming && b.isUpcoming) return 1
      }

      let comparison = 0

      switch (sortField) {
        case 'name':
          comparison = a.name.localeCompare(b.name)
          break
        case 'release':
          // Handle missing release dates by putting them at the end regardless of sort direction
          if (!a.releaseDate && !b.releaseDate) {
            comparison = 0
          } else if (!a.releaseDate) {
            return 1  // Always put items with no date at the end (don't apply direction inversion)
          } else if (!b.releaseDate) {
            return -1 // Always put items with no date at the end (don't apply direction inversion)
          } else {
            comparison = new Date(a.releaseDate).getTime() - new Date(b.releaseDate).getTime()
          }
          break
        case 'price':
          // Handle missing prices by putting them at the end regardless of sort direction
          const priceA = getPriceValue(a)
          const priceB = getPriceValue(b)

          if (priceA === Infinity && priceB === Infinity) {
            comparison = 0
          } else if (priceA === Infinity) {
            return 1  // Always put items with no price at the end (don't apply direction inversion)
          } else if (priceB === Infinity) {
            return -1 // Always put items with no price at the end (don't apply direction inversion)
          } else {
            comparison = priceA - priceB
          }
          break
        default:
          comparison = 0
      }

      return sortDirection === 'desc' ? -comparison : comparison
    })
  }, [getPriceValue])

  // Get search results from the reusable search hook
  const searchFilteredSkins = searchHook.searchResults
  const searchSuggestions = searchHook.searchSuggestions
  const hasSearchResults = searchHook.hasSearchResults

  // Apply additional filters to search results
  const filteredSkins = useMemo(() => {
    const filtered = searchFilteredSkins.filter(skin => {
      // Tier filter (multiple selections)
      if (filters.tiers.length > 0) {
        const matchesTier = filters.tiers.some(selectedTier => {
          if (selectedTier === 'Legacy') {
            return skin.isLegacy
          } else {
            return skin.tier === selectedTier
          }
        })

        if (!matchesTier) return false
      }

      // Champion filter (multiple selections)
      if (filters.champions.length > 0) {
        if (!filters.champions.includes(skin.champion)) {
          return false
        }
      }

      // Skin line filter (multiple selections)
      if (filters.skinLines.length > 0) {
        const hasSkinLine = filters.skinLines.some(selectedSkinLine =>
          skin.skinLines && skin.skinLines.includes(selectedSkinLine)
        )
        if (!hasSkinLine) {
          return false
        }
      }

      return true
    })

    // Check if we're in default state (no filters applied, default sorting)
    const isDefaultState =
      filters.debouncedSearch.trim() === '' &&
      filters.tiers.length === 0 &&
      filters.champions.length === 0 &&
      filters.skinLines.length === 0 &&
      filters.sortField === 'name' &&
      filters.sortDirection === 'asc'

    // Apply sorting to the filtered results
    return sortSkins(filtered, filters.sortField, filters.sortDirection, isDefaultState)
  }, [searchFilteredSkins, filters.tiers, filters.champions, filters.skinLines, filters.sortField, filters.sortDirection, sortSkins, filters.debouncedSearch])

  const updateFilters = useCallback((newFilters: Partial<SkinFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }))

    // Update search hook if search filter changes
    if (newFilters.search !== undefined) {
      searchHook.setSearchQuery(newFilters.search)
    }
  }, [searchHook])

  const resetFilters = useCallback(() => {
    setFilters(defaultInternalFilters)
    searchHook.clearSearch()
  }, [searchHook])

  // Create public filters interface (using search hook's query)
  const publicFilters: SkinFilters = {
    search: searchHook.searchQuery,
    tiers: filters.tiers,
    champions: filters.champions,
    skinLines: filters.skinLines,
    sortField: filters.sortField,
    sortDirection: filters.sortDirection
  }

  return {
    filteredSkins,
    filters: publicFilters,
    updateFilters,
    resetFilters,
    availableChampions,
    availableTiers,
    availableSkinLines,
    isSearching: searchHook.isSearching,
    searchSuggestions,
    hasSearchResults,
    // Expose search hook methods for direct use with SearchWithSuggestions component
    searchHook
  }
}
