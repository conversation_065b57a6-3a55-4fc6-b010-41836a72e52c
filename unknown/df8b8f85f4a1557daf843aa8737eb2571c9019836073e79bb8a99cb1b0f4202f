"use client"

import { Badge } from "@/components/ui/badge"
import { X } from "lucide-react"

interface SelectedItemFiltersDisplayProps {
  filters: {
    search: string
    tags: string[]
    priceRange: [number, number]
  }
  onRemoveFilter: (filterType: string, value: string) => void
  onClearCategory: (category: string) => void
}

export function SelectedItemFiltersDisplay({
  filters,
  onRemoveFilter,
  onClearCategory
}: SelectedItemFiltersDisplayProps) {
  const hasFilters = filters.tags.length > 0 ||
                    (filters.priceRange[0] !== 0 || filters.priceRange[1] !== 5000)

  if (!hasFilters) {
    return null
  }

  const formatTag = (tag: string): string => {
    return tag.replace(/([a-z])([A-Z])/g, '$1 $2')
  }

  const renderFilterGroup = (
    title: string,
    items: string[],
    filterType: string,
    color: string,
    showIcons = false
  ) => {
    if (items.length === 0) return null

    return (
      <div className="flex items-center space-x-2">
        <span className="text-gray-400 text-sm font-medium">{title}:</span>
        <div className="flex items-center space-x-1 flex-wrap">
          {items.length <= 3 ? (
            items.map(item => (
              <Badge
                key={item}
                variant="secondary"
                className={`${color} text-xs flex items-center space-x-1 cursor-pointer hover:opacity-80 transition-opacity`}
                onClick={() => onRemoveFilter(filterType, item)}
              >
                <span>{formatTag(item)}</span>
                <X className="h-3 w-3" />
              </Badge>
            ))
          ) : (
            <>
              {items.slice(0, 2).map(item => (
                <Badge
                  key={item}
                  variant="secondary"
                  className={`${color} text-xs flex items-center space-x-1 cursor-pointer hover:opacity-80 transition-opacity`}
                  onClick={() => onRemoveFilter(filterType, item)}
                >
                  <span>{formatTag(item)}</span>
                  <X className="h-3 w-3" />
                </Badge>
              ))}
              <Badge
                variant="secondary"
                className={`${color} text-xs cursor-pointer hover:opacity-80 transition-opacity`}
                onClick={() => onClearCategory(filterType)}
              >
                +{items.length - 2} more
              </Badge>
            </>
          )}
        </div>
      </div>
    )
  }

  return (
    <div className="mb-4 p-3 bg-gray-800/30 rounded-lg border border-gray-700/20">
      <div className="flex flex-wrap items-center gap-4">
        {/* Tags Filter */}
        {renderFilterGroup(
          "Tags",
          filters.tags,
          "tags",
          "bg-blue-600/20 text-blue-300 border-blue-600/30"
        )}
        
        {/* Price Range Filter */}
        {(filters.priceRange[0] !== 0 || filters.priceRange[1] !== 5000) && (
          <div className="flex items-center space-x-2">
            <span className="text-gray-400 text-sm font-medium">Price Range:</span>
            <Badge
              variant="secondary"
              className="bg-orange-600/20 text-orange-300 border-orange-600/30 text-xs flex items-center space-x-1 cursor-pointer hover:opacity-80 transition-opacity"
              onClick={() => onRemoveFilter('priceRange', '')}
            >
              <span>{filters.priceRange[0]}g - {filters.priceRange[1]}g</span>
              <X className="h-3 w-3" />
            </Badge>
          </div>
        )}
      </div>
    </div>
  )
}
