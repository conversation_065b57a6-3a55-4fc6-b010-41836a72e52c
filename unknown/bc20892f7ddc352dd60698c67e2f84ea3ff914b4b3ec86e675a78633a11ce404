"use client"

import * as React from "react"
import { Check, ChevronsUpDown } from "lucide-react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { useChromas } from "@/hooks/use-chromas"
import { getActualChromaColorName } from "@/lib/utils/chroma-utils"

interface ChromaColorsComboboxProps {
  selectedColors: string[]
  onSelectionChange: (colors: string[]) => void
  availableColors: string[]
  placeholder?: string
  className?: string
}

export function ChromaColorsCombobox({
  selectedColors,
  onSelectionChange,
  availableColors,
  placeholder = "All Colors",
  className
}: ChromaColorsComboboxProps) {
  const [open, setOpen] = React.useState(false)
  const { chromas } = useChromas()

  const handleSelect = (colorName: string) => {
    const isSelected = selectedColors.includes(colorName)
    
    if (isSelected) {
      // Remove color if already selected
      onSelectionChange(selectedColors.filter(c => c !== colorName))
    } else {
      // Add color if not selected
      onSelectionChange([...selectedColors, colorName])
    }
  }

  const getDisplayText = () => {
    if (selectedColors.length === 0) {
      return placeholder
    } else if (selectedColors.length === 1) {
      return selectedColors[0]
    } else {
      return `${selectedColors[0]} +${selectedColors.length - 1}`
    }
  }

  // Get color hex value from actual chroma data
  const getColorHex = React.useMemo(() => {
    const colorMap: Record<string, string> = {}

    // Build color mapping from actual chroma data
    chromas.forEach(chroma => {
      const actualColorName = getActualChromaColorName(chroma.name)
      if (actualColorName && chroma.colors && chroma.colors.length > 0) {
        // Use the first color from the colors array
        colorMap[actualColorName] = chroma.colors[0]
      }
    })

    return (colorName: string): string => {
      return colorMap[colorName] || '#8b5cf6' // Default to purple if not found
    }
  }, [chromas])

  return (
    <div className={className}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between bg-gray-900/40 border-gray-700/20 text-white hover:bg-gray-800/50 h-[40px]"
          >
            <span className="truncate text-left">
              {getDisplayText()}
            </span>
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0 bg-gray-900 border-gray-700">
          <Command>
            <CommandInput 
              placeholder="Search colors..." 
              className="h-9 bg-gray-900 border-gray-700 text-white placeholder-gray-400"
            />
            <CommandList className="max-h-[300px]">
              <CommandEmpty className="text-gray-400 py-6 text-center text-sm">
                No color found.
              </CommandEmpty>
              <CommandGroup>
                {availableColors.map((color) => (
                  <CommandItem
                    key={color}
                    value={color}
                    onSelect={() => handleSelect(color)}
                    className="text-white hover:bg-purple-400/10 cursor-pointer"
                  >
                    <div className="flex items-center space-x-2 flex-1">
                      <div
                        className="w-4 h-4 rounded-full border border-white/30 flex-shrink-0"
                        style={{ backgroundColor: getColorHex(color) }}
                      />
                      <span className="flex-1">{color}</span>
                    </div>
                    <Check
                      className={cn(
                        "ml-auto h-4 w-4",
                        selectedColors.includes(color) ? "opacity-100" : "opacity-0"
                      )}
                    />
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  )
}
