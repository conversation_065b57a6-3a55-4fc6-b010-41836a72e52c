"use client"

import { useState, useRef, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { DidYouMean } from "@/components/ui/did-you-mean"
import { Search, X, ChevronRight } from "lucide-react"
import { useMobileSearch } from "@/hooks/use-search"

interface MobileSearchProps {
  onSearch?: (query: string) => void
}

export default function MobileSearch({ onSearch }: MobileSearchProps) {
  const search = useMobileSearch()
  const searchInputRef = useRef<HTMLInputElement>(null)
  const scrollContainerRef = useRef<HTMLDivElement>(null)
  const [canScrollDown, setCanScrollDown] = useState(false)

  // Check if container can scroll down
  const checkScrollability = () => {
    if (scrollContainerRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = scrollContainerRef.current
      // Show indicator only when content overflows AND user hasn't scrolled yet
      setCanScrollDown(scrollHeight > clientHeight && scrollTop === 0)
    }
  }

  // Check scrollability when results change
  useEffect(() => {
    checkScrollability()
  }, [search.currentSuggestions, search.loading])

  const getTagColor = (tag: string) => {
    switch (tag) {
      case "Champions":
        return "bg-blue-600/20 text-blue-300 border-blue-500/30"
      case "Items":
        return "bg-green-600/20 text-green-300 border-green-500/30"
      case "Skins":
        return "bg-purple-600/20 text-purple-300 border-purple-500/30"
      case "Chromas":
        return "bg-pink-600/20 text-pink-300 border-pink-500/30"
      default:
        return "bg-gray-600/20 text-gray-300 border-gray-500/30"
    }
  }

  const handleSearch = () => {
    if (onSearch) {
      onSearch(search.searchQuery)
    }
    search.closeSearch()
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch()
    }
  }

  // Focus input when search opens
  useEffect(() => {
    if (search.isOpen && searchInputRef.current) {
      searchInputRef.current.focus()
    }
  }, [search.isOpen])

  // Handle click outside to close
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Close if clicking outside the search overlay
      const target = event.target as Element
      if (search.isOpen && !target.closest('.mobile-search-overlay')) {
        search.closeSearch()
      }
    }

    if (search.isOpen) {
      // Add a small delay to prevent immediate closing when opening
      const timer = setTimeout(() => {
        document.addEventListener('mousedown', handleClickOutside)
      }, 100)

      return () => {
        clearTimeout(timer)
        document.removeEventListener('mousedown', handleClickOutside)
      }
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [search.isOpen, search.closeSearch])

  return (
    <>
      {/* Search Button with Icon Transformation */}
      <Button
        variant="ghost"
        size="icon"
        className="sm:hidden text-white hover:bg-orange-600/20 transition-all duration-300"
        onClick={search.isOpen ? search.closeSearch : search.openSearch}
      >
        <div className="relative w-5 h-5">
          {/* Search Icon */}
          <Search
            className={`absolute inset-0 h-5 w-5 transition-all duration-300 ${
              search.isOpen
                ? 'opacity-0 rotate-90 scale-75'
                : 'opacity-100 rotate-0 scale-100'
            }`}
          />
          {/* X Icon */}
          <X
            className={`absolute inset-0 h-4 w-4 transition-all duration-300 ${
              search.isOpen
                ? 'opacity-100 rotate-0 scale-100'
                : 'opacity-0 -rotate-90 scale-75'
            }`}
          />
        </div>
        <span className="sr-only">
          {search.isOpen ? 'Close Search' : 'Search'}
        </span>
      </Button>

      {/* Mobile Search Overlay */}
      {search.isOpen && (
        <div className="fixed inset-0 z-50 sm:hidden">
          {/* Backdrop */}
          <div className="absolute inset-0 bg-black/50" />

          {/* Search Panel */}
          <div className="mobile-search-overlay absolute top-0 left-0 right-0 bg-gray-950/95 backdrop-blur-md border-b border-orange-800/20 animate-in slide-in-from-top duration-300">
            <div className="p-6">
              <div className="mb-4">
                <h2 className="text-lg font-semibold text-white">Search</h2>
              </div>

              <div className="space-y-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    ref={searchInputRef}
                    type="text"
                    placeholder="Search champions, items, skins..."
                    value={search.searchQuery}
                    onChange={(e) => search.setSearchQuery(e.target.value)}
                    onKeyPress={handleKeyPress}
                    className="pl-10 pr-4 py-3 bg-gray-800/50 border-gray-600 text-white placeholder-gray-400 focus:border-orange-400"
                  />
                </div>

                {/* Search Results Container - Fixed height to prevent layout shift */}
                <div
                  ref={scrollContainerRef}
                  className="min-h-[350px] max-h-96 overflow-y-auto scrollbar-thin scrollbar-thumb-orange-400/60 scrollbar-track-gray-800/40 relative border-t border-b border-gray-700/30"
                  onScroll={checkScrollability}
                >
                  {(search.loading || search.isSearching) ? (
                    <div className="flex items-center justify-center h-full p-4">
                      <div className="text-center">
                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-orange-400 mx-auto mb-2"></div>
                        <p className="text-gray-400 text-sm">Searching...</p>
                      </div>
                    </div>
                  ) : search.currentSuggestions.length > 0 ? (
                    <div className="space-y-2">
                      {search.currentSuggestions.map((suggestion) => (
                        <div
                          key={`${suggestion.type}-${suggestion.id}`}
                          onClick={() => search.handleResultClick(suggestion.href)}
                          className="flex items-center p-3 hover:bg-gray-800/50 rounded-lg transition-all duration-150 group/mobile-item cursor-pointer"
                        >
                          <div className="w-10 h-10 mr-3 rounded-lg overflow-hidden bg-gray-800/50 flex-shrink-0">
                            <Image
                              src={suggestion.image}
                              alt={suggestion.name}
                              width={40}
                              height={40}
                              className="w-full h-full object-cover group-hover/mobile-item:scale-105 transition-transform duration-200"
                            />
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="text-white font-medium text-sm mb-1 group-hover/mobile-item:text-orange-300 transition-colors">
                              {suggestion.name}
                            </div>
                            <div className="flex items-center">
                              <span
                                className={`inline-flex items-center px-2 py-0.5 rounded-md text-xs font-medium border ${getTagColor(suggestion.tag)}`}
                              >
                                {suggestion.tag}
                              </span>
                            </div>
                          </div>
                          <ChevronRight className="h-4 w-4 text-gray-500 transition-all duration-150 flex-shrink-0 group-hover/mobile-item:text-orange-400 group-hover/mobile-item:translate-x-1" />
                        </div>
                      ))}
                    </div>
                  ) : search.searchQuery.trim() ? (
                    // No results found - show suggestions or sad bee emote
                    <div className="h-full">
                      {search.showSuggestions ? (
                        <div className="p-4">
                          <div className="flex items-center justify-center mb-6">
                            <div className="text-center">
                              <div className="relative mx-auto mb-3 w-16 h-16">
                                <Image
                                  src="https://raw.communitydragon.org/latest/game/assets/loadouts/summoneremotes/flairs/em_bee_sad_glow.png"
                                  alt="No results glow"
                                  width={64}
                                  height={64}
                                  className="absolute inset-0 opacity-60"
                                  unoptimized
                                />
                                <Image
                                  src="https://raw.communitydragon.org/latest/game/assets/loadouts/summoneremotes/flairs/em_bee_sad_vfx.png"
                                  alt="No results"
                                  width={64}
                                  height={64}
                                  className="relative z-10"
                                  unoptimized
                                />
                              </div>
                              <p className="text-gray-400 text-sm">
                                No exact matches found
                              </p>
                            </div>
                          </div>
                          <DidYouMean
                            suggestions={search.searchSuggestions}
                            onSuggestionClick={search.handleSuggestionClick}
                            variant="orange"
                          />
                        </div>
                      ) : (
                        <div className="flex items-center justify-center h-full p-4">
                          <div className="text-center">
                            <div className="relative mx-auto mb-4 w-20 h-20">
                              <Image
                                src="https://raw.communitydragon.org/latest/game/assets/loadouts/summoneremotes/flairs/em_bee_sad_glow.png"
                                alt="No results glow"
                                width={80}
                                height={80}
                                className="absolute inset-0 opacity-60"
                                unoptimized
                              />
                              <Image
                                src="https://raw.communitydragon.org/latest/game/assets/loadouts/summoneremotes/flairs/em_bee_sad_vfx.png"
                                alt="No results"
                                width={80}
                                height={80}
                                className="relative z-10"
                                unoptimized
                              />
                            </div>
                            <h3 className="text-lg font-semibold text-white mb-2">No results found</h3>
                            <p className="text-gray-400 text-sm">
                              Try searching for champions, skins, items, or chromas
                            </p>
                          </div>
                        </div>
                      )}
                    </div>
                  ) : (
                    // Default state - show start typing message
                    <div className="flex items-center justify-center h-full p-4">
                      <div className="text-center">
                        <Search className="h-8 w-8 mx-auto mb-3 text-gray-500" />
                        <p className="text-gray-400 text-sm">Start typing to search everything...</p>
                      </div>
                    </div>
                  )}

                  {/* Scroll indicator */}
                  {canScrollDown && (
                    <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-gray-950/95 to-transparent pointer-events-none flex items-end justify-center pb-1 z-10">
                      <div className="flex items-center text-orange-400/70 text-xs animate-bounce bg-gray-950/90 px-2 py-1 rounded-full border border-orange-400/20">
                        <span className="mr-1">Scroll for more</span>
                        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                        </svg>
                      </div>
                    </div>
                  )}
                </div>

                <Button
                  onClick={handleSearch}
                  className="w-full bg-gradient-to-r from-orange-500 to-amber-600 hover:from-orange-600 hover:to-amber-700"
                >
                  Search
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  )
}
