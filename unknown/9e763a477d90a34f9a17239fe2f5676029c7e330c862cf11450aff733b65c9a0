"use client"

import Image from "next/image"
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip"

interface ChromaRarityIconsProps {
  rarity: string
  className?: string
  size?: number
  showTooltip?: boolean
}

/**
 * Component for displaying chroma rarity icons
 * Based on Community Dragon rarity system: 0 = Others, 1 = Regular, 2 = Epic, 3 = Legendary, 4 = Mythic, 5 = Ultimate, 6 = Exalted
 */
export function ChromaRarityIcons({
  rarity,
  className = "",
  size = 16,
  showTooltip = true
}: ChromaRarityIconsProps) {
  // Don't show icon for "Others" rarity (rarity 0)
  if (rarity === 'Others') {
    return null
  }

  const rarityIconUrl = getChromaRarityIconUrl(rarity)

  // Get rarity description for tooltip
  const getRarityDescription = (rarity: string): string => {
    const descriptions: Record<string, string> = {
      'Legacy': 'No longer available for purchase',
      'Regular': 'Standard chroma with basic color changes',
      'Epic': 'High-quality chroma with enhanced visual effects',
      'Legendary': 'Premium chroma with unique color schemes',
      'Ultimate': 'Exclusive chroma with special visual elements',
      'Mythic': 'Rare chroma with prestige color variations',
      'Exalted': 'Ultra-rare chroma with exceptional quality',
      'Transcendent': 'Transcendent chroma with evolving stages'
    }
    return descriptions[rarity] || `${rarity} rarity chroma`
  }

  // Get rarity-specific color for tooltip (using exact hex colors)
  const getRarityColor = (rarity: string): string => {
    const colorMap: Record<string, string> = {
      'Legacy': '#c89b3c',
      'Regular': '#299645',
      'Epic': '#27d7e5',
      'Legendary': '#e81b23',
      'Mythic': '#b47bdf',
      'Ultimate': '#d7861f',
      'Exalted': '#d3ce5a',
      'Transcendent': '#ff6b35'
    }
    return colorMap[rarity] || '#299645'
  }

  // If no icon URL, return null
  if (!rarityIconUrl) {
    return null
  }

  const iconElement = (
    <div
      className="flex items-center justify-center"
      style={{ width: size, height: size }}
    >
      <Image
        src={rarityIconUrl}
        alt={rarity}
        width={size}
        height={size}
        className="object-contain"
        style={{ userSelect: 'none', pointerEvents: 'none' }}
        draggable={false}
      />
    </div>
  )

  if (showTooltip) {
    return (
      <div className={`flex items-center ${className}`}>
        <Tooltip>
          <TooltipTrigger asChild>
            {iconElement}
          </TooltipTrigger>
          <TooltipContent>
            <div className="text-center">
              <div
                className="font-semibold"
                style={{ color: getRarityColor(rarity) }}
              >
                {rarity}
              </div>
              <div className="text-sm text-gray-300 mt-1">
                {getRarityDescription(rarity)}
              </div>
            </div>
          </TooltipContent>
        </Tooltip>
      </div>
    )
  }

  return (
    <div className={`flex items-center ${className}`}>
      {iconElement}
    </div>
  )
}

/**
 * Simplified version for cases where you just need the icon without tooltip
 */
export function ChromaRarityIconsSimple({
  rarity,
  className = "",
  size = 16
}: Omit<ChromaRarityIconsProps, 'showTooltip'>) {
  return (
    <ChromaRarityIcons
      rarity={rarity}
      className={className}
      size={size}
      showTooltip={false}
    />
  )
}

/**
 * Get chroma rarity icon URL based on rarity tier
 * Uses Community Dragon rarity gem icons
 */
function getChromaRarityIconUrl(rarity: string): string | null {
  const iconMap: Record<string, string> = {
    'Legacy': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/summoner-icon/icon-legacy.png', // Legacy icon
    'Regular': 'https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/summoner-icon/summoner-icon-rare.png', // Updated icon for Regular (rarity 1)
    'Epic': 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/epic.png',
    'Legendary': 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/legendary.png',
    'Ultimate': 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/ultimate.png',
    'Mythic': 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/mythic.png',
    'Exalted': 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/exalted.png',
    'Transcendent': 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/transcendent.png'
  }

  return iconMap[rarity] || null
}
