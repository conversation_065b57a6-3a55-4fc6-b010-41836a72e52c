"use client"

import { useState, useEffect, useCallback } from 'react'

interface UseMobileTooltipProps {
  disabled?: boolean
}

interface UseMobileTooltipReturn {
  isOpen: boolean
  isMobile: boolean
  handleClick: (e: React.MouseEvent) => void
  handleMouseEnter: () => void
  handleMouseLeave: () => void
  closeTooltip: () => void
}

export function useMobileTooltip({ disabled = false }: UseMobileTooltipProps = {}): UseMobileTooltipReturn {
  const [isOpen, setIsOpen] = useState(false)
  const [isMobile, setIsMobile] = useState(false)

  // Detect if device is mobile/touch
  useEffect(() => {
    const checkIsMobile = () => {
      const hasTouchScreen = 'ontouchstart' in window || navigator.maxTouchPoints > 0
      const isSmallScreen = window.innerWidth <= 768
      setIsMobile(hasTouchScreen || isSmallScreen)
    }

    checkIsMobile()
    window.addEventListener('resize', checkIsMobile)
    return () => window.removeEventListener('resize', checkIsMobile)
  }, [])

  // Handle click outside to close tooltip
  useEffect(() => {
    if (!isOpen || disabled) return

    const handleClickOutside = (event: MouseEvent | TouchEvent) => {
      const target = event.target as Element
      
      // Don't close if clicking on tooltip content or trigger
      if (target.closest('[data-slot="tooltip-content"]') || 
          target.closest('[data-slot="tooltip-trigger"]')) {
        return
      }
      
      setIsOpen(false)
    }

    // Add listeners immediately for mobile, with small delay for desktop
    if (isMobile) {
      // For mobile, add listeners immediately
      document.addEventListener('click', handleClickOutside, true)
      document.addEventListener('touchend', handleClickOutside, true)

      return () => {
        document.removeEventListener('click', handleClickOutside, true)
        document.removeEventListener('touchend', handleClickOutside, true)
      }
    } else {
      // For desktop, add small delay to prevent immediate closing
      const timeoutId = setTimeout(() => {
        document.addEventListener('click', handleClickOutside, true)
      }, 100)

      return () => {
        clearTimeout(timeoutId)
        document.removeEventListener('click', handleClickOutside, true)
      }
    }
  }, [isOpen, disabled, isMobile])

  const handleClick = useCallback((e: React.MouseEvent) => {
    if (disabled) return
    
    // On mobile, toggle tooltip on click
    if (isMobile) {
      e.preventDefault()
      e.stopPropagation()
      setIsOpen(prev => !prev)
    }
  }, [isMobile, disabled])

  const handleMouseEnter = useCallback(() => {
    if (disabled || isMobile) return
    // On desktop, show tooltip on hover
    setIsOpen(true)
  }, [isMobile, disabled])

  const handleMouseLeave = useCallback(() => {
    if (disabled || isMobile) return
    // On desktop, hide tooltip on mouse leave
    setIsOpen(false)
  }, [isMobile, disabled])

  const closeTooltip = useCallback(() => {
    setIsOpen(false)
  }, [])

  return {
    isOpen,
    isMobile,
    handleClick,
    handleMouseEnter,
    handleMouseLeave,
    closeTooltip
  }
}
