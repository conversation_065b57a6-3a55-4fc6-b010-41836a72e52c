"use client"

import * as React from "react"
import { Check, ChevronDown, X } from "lucide-react"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Badge } from "@/components/ui/badge"
import { SkinRarityIconsSimple } from "@/components/ui/skin-rarity-icons"

interface MultiSelectTiersProps {
  selectedTiers: string[]
  onSelectionChange: (tiers: string[]) => void
  availableTiers: string[]
  placeholder?: string
  className?: string
}

export function MultiSelectTiers({
  selectedTiers,
  onSelectionChange,
  availableTiers,
  placeholder = "Select tiers...",
  className
}: MultiSelectTiersProps) {
  const [open, setOpen] = React.useState(false)

  const handleTierToggle = (tier: string) => {
    const newSelection = selectedTiers.includes(tier)
      ? selectedTiers.filter(t => t !== tier)
      : [...selectedTiers, tier]
    
    onSelectionChange(newSelection)
  }

  const handleClearAll = () => {
    onSelectionChange([])
  }

  const renderTierOption = (tier: string) => {
    return (
      <div className="flex items-center space-x-2">
        <div className="w-4 h-4 flex items-center justify-center">
          <SkinRarityIconsSimple
            rarity={tier}
            isLegacy={tier === "Legacy"}
            isBase={false}
            size={16}
          />
        </div>
        <span>{tier}</span>
      </div>
    )
  }

  const renderSelectedBadges = () => {
    if (selectedTiers.length === 0) {
      return <span className="text-gray-400">{placeholder}</span>
    }

    return (
      <span className="text-white">
        Rarities +{selectedTiers.length}
      </span>
    )
  }

  return (
    <div className={className}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between bg-gray-900/40 border-gray-700/20 text-white hover:bg-gray-800/50 h-[40px] items-center"
          >
            <div className="flex-1 text-left">
              {renderSelectedBadges()}
            </div>
            <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0 bg-gray-900 border-gray-700" align="start">
          <div className="p-3">
            <div className="flex items-center justify-between mb-3">
              <span className="text-sm font-medium text-white">Select Rarities</span>
              {selectedTiers.length > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleClearAll}
                  className="text-xs text-gray-400 hover:text-white h-6"
                >
                  Clear All
                </Button>
              )}
            </div>
            <div className="space-y-1 max-h-60 overflow-y-auto">
              {availableTiers.map((tier) => (
                <div
                  key={tier}
                  className="flex items-center space-x-3 p-1 rounded hover:bg-gray-800/50 cursor-pointer"
                  onClick={() => handleTierToggle(tier)}
                >
                  <Checkbox
                    checked={selectedTiers.includes(tier)}
                    onCheckedChange={() => handleTierToggle(tier)}
                    className="border-gray-600 data-[state=checked]:bg-purple-600 data-[state=checked]:border-purple-600"
                  />
                  {renderTierOption(tier)}
                </div>
              ))}
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  )
}
