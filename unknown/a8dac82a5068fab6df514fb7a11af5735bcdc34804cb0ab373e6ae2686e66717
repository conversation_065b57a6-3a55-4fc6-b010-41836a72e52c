"use client"

import { useState, useEffect, useRef, useCallback } from "react"
import { createPortal } from "react-dom"
import Image from "next/image"
import { useRouter } from "next/navigation"
import { ChevronLeft, ChevronRight, X, ExternalLink } from "lucide-react"
import {
  <PERSON><PERSON><PERSON>,
  Toolt<PERSON><PERSON>ontent,
  TooltipTrigger,
} from "@/components/ui/tooltip"

interface ChromaData {
  id: string
  name: string
  color: string
  colors?: string[]
  chromaPath?: string
  skinId?: number
  isDefault?: boolean
}

interface SkinData {
  name: string
  price: string
  rarity: string
  rarityTier?: string
  isLegacy?: boolean
  image: string
  splashArt: string
  inGameImage: string
  isBase: boolean
  hasInGameImage: boolean
  chromas: ChromaData[]
  skinNum: number
  communityDragonId?: number
  videoUrl?: string // For animated skins (Exalted, Ultimate, Transcendent)
}

interface ChampionSkinViewerClientProps {
  skins: SkinData[]
  championName: string
  championKey: string
  currentSkinIndex: number
  onSkinChange: (index: number) => void
}

// Function to convert tiered skin names to base tier names
function getBaseTierSkinName(skinName: string): string {
  const name = skinName.toLowerCase()

  // Handle Transcendent skins - convert higher tiers to base tier
  if (name.includes('immortalized legend')) {
    return skinName.replace(/immortalized legend/i, 'Risen Legend')
  }

  // Handle K/DA Seraphine tiers - convert to base tier
  if (name.includes('k/da all out seraphine')) {
    // Convert any tier (Rising Star, Superstar) to base Indie tier
    return skinName.replace(/k\/da all out seraphine.*/i, 'K/DA ALL OUT Seraphine Indie')
  }

  // Handle other potential tiered skins by removing common tier indicators
  // This is a fallback for any other tiered skins that might exist
  const tierPatterns = [
    /\s+(stage\s+\d+|tier\s+\d+|level\s+\d+)$/i,
    /\s+(rising star|superstar)$/i,
    /\s+(evolved|ascended|transcended)$/i
  ]

  let baseName = skinName
  for (const pattern of tierPatterns) {
    baseName = baseName.replace(pattern, '')
  }

  return baseName
}

// Function to create URL-friendly skin names
function createSkinSlug(skinName: string): string {
  // First convert to base tier name for tiered skins
  const baseTierName = getBaseTierSkinName(skinName)

  return baseTierName
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters except spaces and hyphens
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .replace(/^-|-$/g, '') // Remove leading/trailing hyphens
}

export default function ChampionSkinViewerClient({
  skins,
  championName,
  championKey,
  currentSkinIndex,
  onSkinChange
}: ChampionSkinViewerClientProps) {
  const router = useRouter()
  const [showSplashArt, setShowSplashArt] = useState(true)
  const [userPrefersInGame, setUserPrefersInGame] = useState(false)
  const [selectedChromaId, setSelectedChromaId] = useState<string | null>(null)
  const [showChromas, setShowChromas] = useState(false)
  const [chromasAnimating, setChromasAnimating] = useState(false)
  const [isZoomed, setIsZoomed] = useState(false)
  const [showZoomControls, setShowZoomControls] = useState(false)
  const [zoomMode, setZoomMode] = useState<'splash' | 'chroma'>('splash')
  const [zoomedSkinIndex, setZoomedSkinIndex] = useState(0)
  const [zoomedChromaIndex, setZoomedChromaIndex] = useState(0)
  const [isHoveringImage, setIsHoveringImage] = useState(false)
  const hideControlsTimerRef = useRef<NodeJS.Timeout | null>(null)
  const [chromaData, setChromaData] = useState<any[]>([])
  const chromasRef = useRef<HTMLDivElement>(null)

  const currentSkin = skins[currentSkinIndex]

  // Calculate the longest skin name for fixed width
  const longestSkinName = skins.reduce((longest, skin) =>
    skin.name.length > longest.length ? skin.name : longest, ""
  )
  const canShowInGame = currentSkin?.hasInGameImage && currentSkin.chromas.length > 0

  // Fetch chroma data from Community Dragon API
  useEffect(() => {
    const fetchChromaData = async () => {
      if (!championKey || !currentSkin.chromas.length) {
        setChromaData([])
        return
      }

      try {
        const response = await fetch(`https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/champions/${championKey}.json`)
        const data = await response.json()

        // Find the matching skin by ID or index
        const matchingSkin = data.skins?.find((skin: any) => {
          // Try to match by skin ID first, then by index
          const skinId = parseInt(championKey) * 1000 + currentSkin.skinNum
          return skin.id === skinId
        })

        if (matchingSkin && matchingSkin.chromas) {
          setChromaData(matchingSkin.chromas)
        } else {
          setChromaData([])
        }
      } catch (error) {
        console.error('Failed to fetch chroma data:', error)
        setChromaData([])
      }
    }

    fetchChromaData()
  }, [championKey, currentSkin.skinNum, currentSkin.chromas.length])

  // Clear auto-hide timer helper
  const clearHideTimer = useCallback(() => {
    if (hideControlsTimerRef.current) {
      clearTimeout(hideControlsTimerRef.current)
      hideControlsTimerRef.current = null
    }
  }, [])

  // Navigation functions for zoom modal
  const handlePreviousSkin = useCallback(() => {
    // Clear any existing auto-hide timer when using arrows
    clearHideTimer()
    // Keep controls visible when using navigation (they'll hide when mouse leaves image)
    setShowZoomControls(true)

    if (zoomMode === 'splash') {
      // Navigate through all skins in splash mode
      const newIndex = zoomedSkinIndex > 0 ? zoomedSkinIndex - 1 : skins.length - 1
      setZoomedSkinIndex(newIndex)
    } else {
      // Navigate through chromas of current skin (including default)
      const currentZoomedSkin = skins[zoomedSkinIndex]
      const totalChromas = currentZoomedSkin.chromas.length
      if (totalChromas > 0) {
        const newChromaIndex = zoomedChromaIndex > 0 ? zoomedChromaIndex - 1 : totalChromas - 1
        setZoomedChromaIndex(newChromaIndex)
      }
    }
  }, [zoomMode, zoomedSkinIndex, zoomedChromaIndex, skins, clearHideTimer])

  const handleNextSkin = useCallback(() => {
    // Clear any existing auto-hide timer when using arrows
    clearHideTimer()
    // Keep controls visible when using navigation (they'll hide when mouse leaves image)
    setShowZoomControls(true)

    if (zoomMode === 'splash') {
      // Navigate through all skins in splash mode
      const newIndex = zoomedSkinIndex < skins.length - 1 ? zoomedSkinIndex + 1 : 0
      setZoomedSkinIndex(newIndex)
    } else {
      // Navigate through chromas of current skin (including default)
      const currentZoomedSkin = skins[zoomedSkinIndex]
      const totalChromas = currentZoomedSkin.chromas.length
      if (totalChromas > 0) {
        const newChromaIndex = zoomedChromaIndex < totalChromas - 1 ? zoomedChromaIndex + 1 : 0
        setZoomedChromaIndex(newChromaIndex)
      }
    }
  }, [zoomMode, zoomedSkinIndex, zoomedChromaIndex, skins, clearHideTimer])

  // Get current zoomed image URL based on mode
  const getZoomedImageUrl = useCallback(() => {
    if (zoomMode === 'splash') {
      return skins[zoomedSkinIndex].splashArt
    } else {
      // Chroma mode - show all chromas including default
      const currentZoomedSkin = skins[zoomedSkinIndex]
      if (currentZoomedSkin.chromas.length === 0 || zoomedChromaIndex >= currentZoomedSkin.chromas.length) {
        // Fallback to in-game image if no chromas or index out of bounds
        return currentZoomedSkin.inGameImage
      }
      const chroma = currentZoomedSkin.chromas[zoomedChromaIndex]
      return `https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/champion-chroma-images/${championKey}/${chroma.id}.png`
    }
  }, [zoomMode, zoomedSkinIndex, zoomedChromaIndex, skins, championKey])

  // Get current zoomed skin info
  const getZoomedSkinInfo = useCallback(() => {
    const currentZoomedSkin = skins[zoomedSkinIndex]
    if (zoomMode === 'splash') {
      return {
        name: currentZoomedSkin.name,
        current: zoomedSkinIndex + 1,
        total: skins.length
      }
    } else {
      // Chroma mode - count only actual chromas (exclude default)
      const actualChromas = currentZoomedSkin.chromas.filter((chroma: any) => !chroma.isDefault)
      const totalActualChromas = actualChromas.length

      if (totalActualChromas === 0 || zoomedChromaIndex >= currentZoomedSkin.chromas.length) {
        // Fallback to skin name if no chromas or index out of bounds
        return {
          name: currentZoomedSkin.name,
          current: 1,
          total: 1
        }
      }

      const chroma = currentZoomedSkin.chromas[zoomedChromaIndex]
      const chromaName = `${currentZoomedSkin.name} (${chroma.name})`

      // Calculate current position: if it's default, show as 1, otherwise find position among actual chromas
      let currentPosition = 1
      if (!chroma.isDefault) {
        const actualChromaIndex = actualChromas.findIndex((c: any) => c.id === chroma.id)
        currentPosition = actualChromaIndex + 1
      }

      return {
        name: chromaName,
        current: currentPosition,
        total: Math.max(1, totalActualChromas) // Show at least 1 even if no actual chromas
      }
    }
  }, [zoomMode, zoomedSkinIndex, zoomedChromaIndex, skins])

  // Handle clicking on zoomed image to toggle controls
  const handleZoomedImageClick = useCallback(() => {
    // Clear any existing timer
    clearHideTimer()
    // Toggle controls visibility when clicking on image
    setShowZoomControls(prev => !prev)
  }, [clearHideTimer])

  // Handle mouse enter/leave on zoomed image
  const handleImageMouseEnter = useCallback(() => {
    setIsHoveringImage(true)
    setShowZoomControls(true)
  }, [])

  const handleImageMouseLeave = useCallback(() => {
    setIsHoveringImage(false)
    // Hide controls when mouse leaves image area
    setShowZoomControls(false)
  }, [])

  // Handle mouse enter/leave on control buttons
  const handleControlMouseEnter = useCallback(() => {
    setShowZoomControls(true)
  }, [])

  const handleControlMouseLeave = useCallback(() => {
    // Only hide if not hovering over image
    if (!isHoveringImage) {
      setShowZoomControls(false)
    }
  }, [isHoveringImage])

  // Handle closing zoom modal and syncing state
  const handleCloseZoom = useCallback(() => {
    // Sync the main viewer with the current zoom state
    if (zoomedSkinIndex !== currentSkinIndex) {
      onSkinChange(zoomedSkinIndex)
    }

    // If we were in chroma mode, sync the chroma selection
    if (zoomMode === 'chroma') {
      const currentZoomedSkin = skins[zoomedSkinIndex]
      if (currentZoomedSkin.chromas.length > 0 && zoomedChromaIndex < currentZoomedSkin.chromas.length) {
        const selectedChroma = currentZoomedSkin.chromas[zoomedChromaIndex]
        setSelectedChromaId(selectedChroma.id?.toString() || zoomedChromaIndex.toString())
        // Switch to in-game view to show the chroma
        setShowSplashArt(false)
        setUserPrefersInGame(true)
      }
    }

    setIsZoomed(false)
  }, [zoomedSkinIndex, currentSkinIndex, onSkinChange, zoomMode, skins, zoomedChromaIndex])

  // Handle keyboard navigation in zoom modal
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!isZoomed) return

      if (event.key === 'Escape') {
        handleCloseZoom()
      } else if (event.key === 'ArrowLeft') {
        event.preventDefault()
        handlePreviousSkin()
        // Don't show controls when using keyboard navigation
      } else if (event.key === 'ArrowRight') {
        event.preventDefault()
        handleNextSkin()
        // Don't show controls when using keyboard navigation
      }
    }

    if (isZoomed) {
      document.addEventListener('keydown', handleKeyDown)
      return () => document.removeEventListener('keydown', handleKeyDown)
    }
  }, [isZoomed, handlePreviousSkin, handleNextSkin, handleCloseZoom])

  // Track scroll position and mounted state for proper client-side rendering
  const [scrollPosition, setScrollPosition] = useState(0)
  const [isMounted, setIsMounted] = useState(false)

  // Ensure component is mounted on client side
  useEffect(() => {
    setIsMounted(true)
  }, [])

  // Blur page content when zoom modal is active
  useEffect(() => {
    if (!isMounted) return

    if (isZoomed) {
      // Store current scroll position
      const currentScrollY = window.scrollY
      setScrollPosition(currentScrollY)

      // Start with controls hidden - they'll show on hover
      setShowZoomControls(false)
      setIsHoveringImage(false)

      // Prevent body scroll while maintaining scroll position
      document.body.style.overflow = 'hidden'
      document.body.style.position = 'fixed'
      document.body.style.top = `-${currentScrollY}px`
      document.body.style.left = '0'
      document.body.style.right = '0'
      document.body.style.width = '100%'

      // Add blur effect to main content areas only - no backdrop filter
      const elementsToBlur = [
        document.querySelector('aside'), // Sidebar
        document.querySelector('main'), // Main content
        document.querySelector('footer'), // Footer
        document.querySelector('header'), // Header if exists
        document.querySelector('.responsive-header'), // Responsive header
      ].filter(Boolean)

      elementsToBlur.forEach(element => {
        if (element) {
          element.style.filter = 'blur(4px)'
          element.style.transition = 'filter 0.3s ease-in-out'
        }
      })

      return () => {
        clearHideTimer()
        setShowZoomControls(false)
        setIsHoveringImage(false)

        // Restore body scroll and positioning
        document.body.style.overflow = ''
        document.body.style.position = ''
        document.body.style.top = ''
        document.body.style.left = ''
        document.body.style.right = ''
        document.body.style.width = ''

        // Restore scroll position
        window.scrollTo(0, currentScrollY)

        // Remove blur effect
        elementsToBlur.forEach(element => {
          if (element) {
            element.style.filter = ''
            element.style.transition = ''
          }
        })
      }
    }
  }, [isZoomed, isMounted, clearHideTimer])

  // Smart mode switching with user preference memory
  useEffect(() => {
    if (!canShowInGame) {
      setShowSplashArt(true)
    } else if (userPrefersInGame) {
      setShowSplashArt(false)
    }
  }, [currentSkinIndex, canShowInGame, userPrefersInGame])

  // Reset chroma selection when switching skins
  useEffect(() => {
    setSelectedChromaId(null)
    setShowChromas(false)
  }, [currentSkinIndex])

  // Close chromas panel when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (chromasRef.current && !chromasRef.current.contains(event.target as Node)) {
        setShowChromas(false)
      }
    }

    if (showChromas) {
      document.addEventListener('mousedown', handleClickOutside)
      document.addEventListener('click', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
      document.removeEventListener('click', handleClickOutside)
    }
  }, [showChromas])

  const handleViewModeToggle = () => {
    if (!canShowInGame) return

    setShowSplashArt(!showSplashArt)
    setUserPrefersInGame(showSplashArt) // If currently showing splash art, user now prefers in-game
  }

  const handleChromaToggle = () => {
    if (!currentSkin.chromas.length || chromasAnimating) return

    setChromasAnimating(true)
    setShowChromas(!showChromas)

    // Reset animation state after duration
    setTimeout(() => {
      setChromasAnimating(false)
    }, 300)
  }

  const handleChromaSelect = (chromaId: string) => {
    setSelectedChromaId(chromaId)
    if (showSplashArt && canShowInGame) {
      setShowSplashArt(false)
      setUserPrefersInGame(true)
    }
  }

  const getCurrentImageUrl = () => {
    if (showSplashArt || !canShowInGame) {
      return currentSkin.splashArt
    }

    if (selectedChromaId && selectedChromaId !== 'default') {
      return `https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/champion-chroma-images/${championKey}/${selectedChromaId}.png`
    }

    return currentSkin.inGameImage
  }



  return (
    <div className="relative">
      {/* Chromas Button and View Mode Toggle */}
      <div className="flex items-center justify-between mb-4">
        {/* Chromas Button (Left Side) */}
        <div className="relative flex items-center" ref={chromasRef}>
          <button
            onClick={handleChromaToggle}
            disabled={!currentSkin.chromas.length || chromasAnimating}
            className={`w-8 h-8 rounded-full border-2 flex items-center justify-center transition-all duration-300 ${
              currentSkin.chromas.length > 0
                ? `border-orange-500 bg-orange-500/20 hover:bg-orange-500/30 cursor-pointer ${showChromas ? 'ring-2 ring-orange-400/50 scale-105' : ''}`
                : 'border-gray-600 bg-gray-600/20 cursor-not-allowed opacity-50'
            } ${chromasAnimating ? 'pointer-events-none' : ''}`}
          >
            <Image
              src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/icons/chroma-icon.png"
              alt="Chromas"
              width={16}
              height={16}
              className="w-4 h-4"
              style={{ userSelect: 'none', pointerEvents: 'none' }}
            />
          </button>

          {/* Horizontal Chromas Dropdown */}
          {showChromas && currentSkin.chromas.length > 0 && (
            <div className="absolute left-full top-0 ml-2 bg-black/90 backdrop-blur-md rounded-lg p-2 border border-orange-500/40 z-10 flex items-center h-8 animate-in slide-in-from-left-2 duration-300">
              <div className="flex items-center space-x-2">
                {/* Default chroma */}
                <button
                  onClick={() => handleChromaSelect('default')}
                  className={`w-5 h-5 rounded-full border-2 transition-all ${
                    selectedChromaId === 'default' || selectedChromaId === null
                      ? 'border-orange-500 scale-110'
                      : 'border-gray-500 hover:border-gray-400'
                  }`}
                  style={{
                    background: 'linear-gradient(135deg, white 47%, #ff0000 47%, #ff0000 53%, white 53%)',
                    backgroundSize: '100% 100%',
                    userSelect: 'none'
                  }}
                />

                {/* Chroma options */}
                {chromaData.map((chroma, index) => {
                  // Determine if this is a single color or dual color chroma
                  const isSingleColor = chroma.colors && chroma.colors.length >= 2 && chroma.colors[0] === chroma.colors[1]
                  const isDualColor = chroma.colors && chroma.colors.length >= 2 && chroma.colors[0] !== chroma.colors[1]

                  return (
                    <button
                      key={chroma.id || index}
                      onClick={() => handleChromaSelect(chroma.id?.toString() || index.toString())}
                      className={`w-5 h-5 rounded-full border-2 transition-all ${
                        selectedChromaId === (chroma.id?.toString() || index.toString())
                          ? 'border-orange-500 scale-110'
                          : 'border-gray-500 hover:border-gray-400'
                      }`}
                      style={{
                        backgroundColor: isSingleColor ? chroma.colors[0] : undefined,
                        background: isDualColor
                          ? `linear-gradient(45deg, ${chroma.colors[0]} 50%, ${chroma.colors[1]} 50%)`
                          : isSingleColor
                          ? chroma.colors[0]
                          : '#888888', // fallback color
                        userSelect: 'none'
                      }}
                    />
                  )
                })}
              </div>
            </div>
          )}
        </div>

        {/* View Mode Toggle (Right Side) - Fixed positions */}
        <div className="flex items-center space-x-3">
          <span className="text-sm text-gray-300">In-Game</span>
          <button
            onClick={handleViewModeToggle}
            disabled={!canShowInGame}
            className={`relative w-12 h-6 rounded-full transition-all duration-300 ease-in-out ${
              canShowInGame
                ? (!showSplashArt ? 'bg-orange-500' : 'bg-gray-600')
                : 'bg-gray-600 opacity-50 cursor-not-allowed'
            }`}
          >
            <div
              className={`absolute top-0.5 left-0.5 w-5 h-5 bg-white rounded-full transition-all duration-300 ease-in-out ${
                !showSplashArt ? 'translate-x-0' : ''
              }`}
              style={{
                transform: !showSplashArt ? 'translateX(0)' : 'translateX(22px)'
              }}
            />
          </button>
          <span className="text-sm text-gray-300">Splash Art</span>
        </div>
      </div>

      {/* Main Display */}
      <div
        className="w-full h-96 rounded-lg overflow-hidden bg-black/30 backdrop-blur-sm relative border border-orange-500/40 shadow-2xl cursor-pointer group"
        onClick={() => {
          if (showSplashArt || !canShowInGame) {
            // Zooming into splash art mode
            setZoomMode('splash')
            setZoomedSkinIndex(currentSkinIndex)
          } else {
            // Zooming into in-game/chroma mode
            setZoomMode('chroma')
            setZoomedSkinIndex(currentSkinIndex)
            // For chromas, include all chromas (including default)
            if (currentSkin.chromas.length > 0) {
              const chromaIndex = currentSkin.chromas.findIndex(chroma => chroma.id === selectedChromaId)
              setZoomedChromaIndex(Math.max(0, chromaIndex))
            } else {
              setZoomedChromaIndex(0)
            }
          }
          setIsZoomed(true)
        }}
        style={{ userSelect: 'none' }}
      >
        {(showSplashArt || !canShowInGame) ? (
          <Image
            src={getCurrentImageUrl() || "/placeholder.svg"}
            alt={currentSkin.name}
            width={600}
            height={320}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
            style={{ userSelect: 'none', pointerEvents: 'none' }}
            draggable={false}
          />
        ) : (
          <div className="relative w-full h-full">
            <Image
              src="https://i.ibb.co/z91Cdg3/image.png"
              alt="In-game background"
              width={800}
              height={450}
              className="w-full h-full object-cover"
              style={{ userSelect: 'none', pointerEvents: 'none' }}
              draggable={false}
            />
            <div className="absolute inset-0 flex items-center justify-center">
              <Image
                src={getCurrentImageUrl() || "/placeholder.svg"}
                alt={currentSkin.name}
                width={300}
                height={300}
                className="max-w-[75%] max-h-[75%] object-contain"
                style={{ userSelect: 'none', pointerEvents: 'none' }}
                draggable={false}
              />
            </div>
          </div>
        )}
        
        {/* Skin Info Overlay */}
        <div className="absolute bottom-4 left-4 right-4 bg-black/90 backdrop-blur-md rounded-lg p-4 border border-gray-700/40">
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center gap-2">
                <h3 className="text-lg font-semibold text-white">{currentSkin.name}</h3>
                {/* View Skin Page Button - Only show for non-base skins */}
                {!currentSkin.isBase && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation() // Prevent splash art zoom
                      const skinSlug = createSkinSlug(currentSkin.name)
                      router.push(`/skins/${skinSlug}`)
                    }}
                    className="p-1.5 rounded-md hover:bg-white/10 transition-colors group"
                    title="View Skin Page"
                  >
                    <ExternalLink className="h-4 w-4 text-purple-400 group-hover:text-purple-300 transition-colors" />
                  </button>
                )}
              </div>
              <p className="text-sm text-gray-300">{currentSkin.rarity}</p>
            </div>
            <div className="flex items-center space-x-2">
              {currentSkin.price === "Free" ? (
                <span className="text-green-400 font-semibold">Free</span>
              ) : currentSkin.price === "N/A" ? (
                <span className="text-gray-400 font-semibold">N/A</span>
              ) : (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex items-center space-x-1 cursor-help">
                      {currentSkin.price.includes('ME') ? (
                        <Image
                          src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/mythic.svg"
                          alt="ME"
                          width={16}
                          height={16}
                          className="w-4 h-4"
                          style={{ userSelect: 'none', pointerEvents: 'none' }}
                          draggable={false}
                        />
                      ) : (
                        <Image
                          src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg"
                          alt="RP"
                          width={16}
                          height={16}
                          className="w-4 h-4"
                          style={{ userSelect: 'none', pointerEvents: 'none' }}
                          draggable={false}
                        />
                      )}
                      <span
                        className="font-semibold"
                        style={{
                          color: currentSkin.price.includes('ME') ? '#b47bdf' : '#fb923c'
                        }}
                      >
                        {currentSkin.price.replace(' RP', '').replace(' ME', '')}
                      </span>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <div className="text-center">
                      <div className="font-semibold text-yellow-300">
                        {currentSkin.price.includes('ME') ? 'Mythic Essence (ME)' : 'Riot Points (RP)'}
                      </div>
                      <div className="text-sm text-gray-300 mt-1">
                        {currentSkin.price.includes('ME')
                          ? 'Special currency for exclusive content'
                          : 'Premium currency for instant unlocks'
                        }
                      </div>
                    </div>
                  </TooltipContent>
                </Tooltip>
              )}
            </div>
          </div>
        </div>
      </div>



      {/* Zoom Modal - Portal-based for proper rendering */}
      {isMounted && isZoomed && createPortal(
        <div
          className="fixed inset-0 z-[9999] flex items-center justify-center p-4 animate-in fade-in-0 duration-300"
          onClick={handleCloseZoom}
          style={{
            backgroundColor: 'rgba(0, 0, 0, 0.95)',
          }}
        >
          <div
            className="relative rounded-lg overflow-hidden shadow-2xl animate-in zoom-in-95 duration-300"
            onClick={(e) => e.stopPropagation()}
            style={{
              filter: 'none !important',
              backdropFilter: 'none !important',
              WebkitBackdropFilter: 'none !important',
              maxWidth: '90vw',
              maxHeight: '90vh',
              width: 'auto',
              height: 'auto',
            }}
          >
            {/* Close Button - Always visible */}
            <button
              onClick={(e) => {
                e.stopPropagation()
                handleCloseZoom()
              }}
              onMouseEnter={handleControlMouseEnter}
              onMouseLeave={handleControlMouseLeave}
              className={`absolute top-4 right-4 w-12 h-12 bg-black/80 hover:bg-black/95 text-white rounded-full flex items-center justify-center shadow-xl backdrop-blur-sm z-20 border border-white/30 hover:border-white/50 transition-all duration-300 ${
                showZoomControls ? 'opacity-100' : 'opacity-0'
              }`}
              title="Close (ESC)"
              style={{
                filter: 'none !important',
                backdropFilter: 'none !important',
                WebkitBackdropFilter: 'none !important',
              }}
            >
              <X size={20} strokeWidth={2.5} />
            </button>

            {/* Navigation Arrows - Auto-hide */}
            {skins.length > 1 && (
              <>
                {/* Previous Button */}
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    handlePreviousSkin()
                  }}
                  onMouseEnter={handleControlMouseEnter}
                  onMouseLeave={handleControlMouseLeave}
                  className={`absolute left-4 top-1/2 -translate-y-1/2 w-12 h-12 bg-black/80 hover:bg-black/95 text-white rounded-full flex items-center justify-center shadow-xl backdrop-blur-sm z-20 border border-white/30 hover:border-white/50 transition-all duration-300 ${
                    showZoomControls ? 'opacity-100' : 'opacity-0'
                  }`}
                  title="Previous Skin (←)"
                  style={{
                    filter: 'none !important',
                    backdropFilter: 'none !important',
                    WebkitBackdropFilter: 'none !important',
                  }}
                >
                  <ChevronLeft size={24} strokeWidth={2.5} />
                </button>

                {/* Next Button */}
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    handleNextSkin()
                  }}
                  onMouseEnter={handleControlMouseEnter}
                  onMouseLeave={handleControlMouseLeave}
                  className={`absolute right-4 top-1/2 -translate-y-1/2 w-12 h-12 bg-black/80 hover:bg-black/95 text-white rounded-full flex items-center justify-center shadow-xl backdrop-blur-sm z-20 border border-white/30 hover:border-white/50 transition-all duration-300 ${
                    showZoomControls ? 'opacity-100' : 'opacity-0'
                  }`}
                  title="Next Skin (→)"
                  style={{
                    filter: 'none !important',
                    backdropFilter: 'none !important',
                    WebkitBackdropFilter: 'none !important',
                  }}
                >
                  <ChevronRight size={24} strokeWidth={2.5} />
                </button>
              </>
            )}

            {/* Skin Info Indicator - Fixed width and auto-hide */}
            <div
              className={`absolute bottom-4 left-1/2 -translate-x-1/2 bg-black/80 backdrop-blur-sm rounded-full px-4 py-2 border border-white/30 transition-all duration-300 ${
                showZoomControls ? 'opacity-100' : 'opacity-0'
              }`}
              style={{
                filter: 'none !important',
                backdropFilter: 'none !important',
                WebkitBackdropFilter: 'none !important',
                minWidth: 'fit-content',
              }}
            >
              <div className="flex items-center space-x-3 text-white">
                {/* Fixed width container based on longest skin name */}
                <span
                  className="text-sm font-medium"
                  style={{
                    minWidth: `${longestSkinName.length * 0.6}em`,
                    textAlign: 'center'
                  }}
                >
                  {getZoomedSkinInfo().name}
                </span>
                <span className="text-xs text-gray-300 whitespace-nowrap">
                  {getZoomedSkinInfo().current} / {getZoomedSkinInfo().total}
                </span>
              </div>
            </div>
            {zoomMode === 'splash' ? (
              <Image
                src={getZoomedImageUrl() || "/placeholder.svg"}
                alt={getZoomedSkinInfo().name}
                width={1920}
                height={1080}
                className="rounded-lg cursor-pointer"
                onClick={handleZoomedImageClick}
                onMouseEnter={handleImageMouseEnter}
                onMouseLeave={handleImageMouseLeave}
                style={{
                  userSelect: 'none',
                  filter: 'none !important',
                  backdropFilter: 'none !important',
                  WebkitBackdropFilter: 'none !important',
                  maxWidth: '90vw',
                  maxHeight: '85vh',
                  width: 'auto',
                  height: 'auto',
                  objectFit: 'contain'
                }}
                draggable={false}
                priority
              />
            ) : (
              <div
                className="relative rounded-lg overflow-hidden cursor-pointer"
                onClick={handleZoomedImageClick}
                onMouseEnter={handleImageMouseEnter}
                onMouseLeave={handleImageMouseLeave}
                style={{
                  maxWidth: '90vw',
                  maxHeight: '85vh',
                  filter: 'none !important',
                  backdropFilter: 'none !important',
                  WebkitBackdropFilter: 'none !important'
                }}
              >
                <Image
                  src="https://i.ibb.co/z91Cdg3/image.png"
                  alt="In-game background"
                  width={1920}
                  height={1080}
                  style={{
                    userSelect: 'none',
                    pointerEvents: 'none',
                    filter: 'none !important',
                    backdropFilter: 'none !important',
                    WebkitBackdropFilter: 'none !important',
                    maxWidth: '90vw',
                    maxHeight: '85vh',
                    width: 'auto',
                    height: 'auto',
                    objectFit: 'contain'
                  }}
                  draggable={false}
                  priority
                />
                <div
                  className="absolute inset-0 flex items-center justify-center"
                  style={{
                    filter: 'none !important',
                    backdropFilter: 'none !important',
                    WebkitBackdropFilter: 'none !important'
                  }}
                >
                  <Image
                    src={getZoomedImageUrl() || "/placeholder.svg"}
                    alt={getZoomedSkinInfo().name}
                    width={800}
                    height={800}
                    style={{
                      userSelect: 'none',
                      pointerEvents: 'none',
                      filter: 'none !important',
                      backdropFilter: 'none !important',
                      WebkitBackdropFilter: 'none !important',
                      maxWidth: '60%',
                      maxHeight: '60%',
                      objectFit: 'contain'
                    }}
                    draggable={false}
                  />
                </div>
              </div>
            )}
          </div>
        </div>,
        document.body
      )}
    </div>
  )
}
