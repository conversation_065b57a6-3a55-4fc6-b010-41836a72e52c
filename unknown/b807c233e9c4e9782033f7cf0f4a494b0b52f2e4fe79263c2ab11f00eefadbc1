import {
  CommunityDragonChroma,
  CommunityDragonEmote,
  CommunityDragonNexusFinisher,
  CommunityDragonSkin,
  CommunityDragonSummonerIcon,
} from '@/lib/types/league-api'
import {
  CatalogEntry,
  MythicStoreSection,
  ProcessedMythicStorefront,
  ProcessedStoreItem,
  StorefrontDisplayMetadata
} from '@/lib/types/store'
import { transformToCDNUrl } from '@/lib/utils/cdn'
import { getColorDisplayName } from '@/lib/utils/chroma-utils'

interface CdnDataCache {
  [key: string]: any
  skins?: CommunityDragonSkin[]
  chromas?: (CommunityDragonChroma & { parentSkinName: string })[]
  allItems?: any[] // All skins and chromas for contentId lookup
  nexusFinishers?: CommunityDragonNexusFinisher[]
  summonerIcons?: CommunityDragonSummonerIcon[]
  emotes?: CommunityDragonEmote[]
  communityDragonSkins?: any[] // Raw Community Dragon skins data for chroma processing
}

// Item type IDs from the API response
const ItemTypeIDs = {
  SUMMONER_ICON: 'bb69c4a5-35a6-423c-a61a-a1eb4ca1aae7',
  CHAMPION_SKIN: 'd620ae7b-cf87-496c-96c5-893c99dc2791', // This is actually the chroma type ID
  CHROMA: 'd620ae7b-cf87-496c-96c5-893c99dc2791', // Alias for clarity
  CHAMPION: '7fdbb3e4-c36f-4b3c-9088-b794ff127ff3',
  WARD_SKIN: '93b91332-509a-4848-b534-8facbdc94315',
  EMOTE: '5cee03fb-af16-4bc9-89f7-064b49e1233f',
  NEXUS_FINISHER: '2a858302-542f-454c-8bfc-5dbddbc7ff8b',
  BUNDLE: 'bundle', // Not a real typeId, but we can treat it as one
  MYTHIC_ESSENCE: 'mythic_essence', // Not a real typeId
}


class StoreService {
  private cdnCache: CdnDataCache = {}
  private communityDragonSkinsCache: any[] | null = null
  private cdnCacheTimestamp: number = 0
  private readonly CACHE_TTL = 1000 * 60 * 60 // 1 hour

  private cdnUrls = {
    skins: 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/skins.json',
    nexusFinishers: 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/en_gb/v1/nexusfinishers.json',
    summonerIcons: 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/en_gb/v1/summoner-icons.json',
    emotes: 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/en_gb/v1/summoner-emotes.json',
  }

  private async loadCdnData() {
    const now = Date.now()
    if (now - this.cdnCacheTimestamp < this.CACHE_TTL && this.cdnCache.skins) {
      return
    }

    
    for (const [key, url] of Object.entries(this.cdnUrls)) {
      try {
        const response = await fetch(url)
        if (!response.ok) throw new Error(`Failed to fetch ${key}`)
        const data = await response.json()

        if (key === 'skins') {
          const allSkins: CommunityDragonSkin[] = Object.values(data)
          const skins: CommunityDragonSkin[] = []
          const chromas: (CommunityDragonChroma & { parentSkinName: string })[] = []

          // Also collect all items (skins and chromas) for contentId matching
          const allItems: any[] = []

          for (const skin of allSkins) {
            if (skin.isBase) continue
            skins.push(skin)
            allItems.push(skin) // Add skin to all items

            if (skin.chromas) {
              for (const chroma of skin.chromas) {
                if (chroma.skinClassification === 'kRecolor') {
                  const chromaWithParent = { ...chroma, parentSkinName: skin.name }
                  chromas.push(chromaWithParent)
                  allItems.push(chromaWithParent) // Add chroma to all items
                }
              }
            }
          }

          this.cdnCache.skins = skins
          this.cdnCache.chromas = chromas
          this.cdnCache.allItems = allItems // Store all items for contentId lookup


          // Debug: Look for Firecracker Teemo specifically
          const firecrackerItems = allItems.filter(item =>
            item.name?.toLowerCase().includes('firecracker') &&
            item.name?.toLowerCase().includes('teemo')
          );
        } else {
          this.cdnCache[key] = Array.isArray(data) ? data : Object.values(data)
        }
      } catch (e) {
        console.error(`Failed to fetch and process ${key}:`, e)
      }
    }
    this.cdnCacheTimestamp = now
  }

  private async getRawStorefrontApiData(): Promise<any> { // Using any to avoid type conflicts with old incorrect types

    const response = await fetch('https://api.loldb.info/api/store/storefronts')
    if (!response.ok) {
      throw new Error('Failed to fetch storefront data from external API')
    }
    return response.json()
  }

  private async getCommunityDragonSkins(): Promise<any[]> {
    if (this.communityDragonSkinsCache) {
      return this.communityDragonSkinsCache
    }

    try {
      const response = await fetch('https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/skins.json')
      if (!response.ok) {
        console.warn('Failed to fetch Community Dragon skins data:', response.status)
        return []
      }

      const skinsData = await response.json()

      // The Community Dragon API might return an object with a data property or directly an array
      let actualSkinsArray: any[]
      if (Array.isArray(skinsData)) {
        actualSkinsArray = skinsData
      } else if (skinsData && Array.isArray(skinsData.data)) {
        actualSkinsArray = skinsData.data
      } else if (skinsData && typeof skinsData === 'object') {
        // If it's an object, try to extract an array from common property names
        actualSkinsArray = skinsData.skins || skinsData.items || Object.values(skinsData)
        if (!Array.isArray(actualSkinsArray)) {
          console.warn('Community Dragon skins data format not recognized:', typeof skinsData)
          return []
        }
      } else {
        console.warn('Community Dragon skins data is not in expected format:', typeof skinsData)
        return []
      }

      this.communityDragonSkinsCache = actualSkinsArray
      return actualSkinsArray
    } catch (error) {
      console.error('Error fetching Community Dragon skins data:', error)
      return []
    }
  }

  public async getProcessedMythicStorefront(): Promise<ProcessedMythicStorefront> {
    await this.loadCdnData()
    const rawApiResponse = await this.getRawStorefrontApiData()
    
    const mythicShopRotations = rawApiResponse.data.data.filter((storefront: any) => 
        storefront.displayMetadata?.shoppefront?.id === "MYTHIC_SHOP"
    );

    const allItems: ProcessedStoreItem[] = [];
    let featured: MythicStoreSection | undefined;
    let biweekly: MythicStoreSection | undefined;
    let weekly: MythicStoreSection | undefined;
    let daily: MythicStoreSection | undefined;

    for (const rotation of mythicShopRotations) {
        const category = rotation.displayMetadata.shoppefront.categories[0];
        const endDate = rotation.catalogEntries?.[0]?.endTime ?? rotation.displayMetadata.endDate;
        const sectionInfo = {
            id: rotation.id,
            name: rotation.name,
            endDate: endDate,
            highlighted: false, // This info isn't present, so we default
            spotlight: false // This info isn't present, so we default
        };

        if(category === "FEATURED") featured = sectionInfo;
        if(category === "BIWEEKLY") biweekly = sectionInfo;
        if(category === "WEEKLY") weekly = sectionInfo;
        if(category === "DAILY") daily = sectionInfo;

        for (const entry of rotation.catalogEntries) {
            const processed = await this.processCatalogEntry(entry, rotation.displayMetadata, category as 'FEATURED' | 'BIWEEKLY' | 'WEEKLY' | 'DAILY');
            if(processed) {
                allItems.push(processed);
            }
        }
    }

    return {
      name: 'Mythic Shop',
      background: '', // Not available in this structure
      items: allItems,
      featured: featured!,
      biweekly: biweekly!,
      weekly: weekly!,
      daily: daily!,
    }
  }

  private findBestMatch(name: string, collection: any[] | undefined, key: string) {
    if (!name || !collection) return null
    const lowerName = name.toLowerCase().trim()
    if (!lowerName) return null

    // Exact match
    let match = collection.find(item => item && item[key] && item[key].toLowerCase() === lowerName)
    if (match) return match

    // Match without "Chroma" if it exists, for matching chromas with skin names
    if (lowerName.includes(' chroma')) {
      const skinName = lowerName.replace(' chroma', '').trim()
      match = collection.find(item => item && item[key] && item[key].toLowerCase() === skinName)
      if (match) return match
    }

    return null
  }
  
  private findCloseMatch(name: string, collection: any[] | undefined, key: string) {
    if (!name || !collection) return null
    const lowerName = name.toLowerCase().replace(/\s*\(.*\)\s*|\s*chroma\s*/g, '').trim()
    if (!lowerName) return null

    // Fallback to inclusive match
    return collection.find(item => item && item[key] && item[key].toLowerCase().includes(lowerName)) || null
  }

  /**
   * Extract champion ID from chroma data
   */
  private extractChampionIdFromChroma(chroma: any): string | null {
    // Try to extract champion ID from the chroma ID
    // Chroma IDs are typically in format: championId + skinId + chromaId
    // For example: 17038 = champion 17 (Teemo), skin 0, chroma 38
    if (chroma.id) {
      const chromaIdStr = chroma.id.toString();
      if (chromaIdStr.length >= 3) {
        // Extract first 1-3 digits as champion ID
        if (chromaIdStr.length === 4) {
          return chromaIdStr.substring(0, 1); // e.g., 1703 -> 1
        } else if (chromaIdStr.length === 5) {
          return chromaIdStr.substring(0, 2); // e.g., 17038 -> 17
        } else if (chromaIdStr.length >= 6) {
          return chromaIdStr.substring(0, 3); // e.g., 266001 -> 266
        }
      }
    }

    // Fallback: try to find champion ID from parent skin if available
    if (chroma.parentSkinName && this.cdnCache.skins) {
      const parentSkin = this.cdnCache.skins.find(s => s.name === chroma.parentSkinName);
      if (parentSkin && parentSkin.id) {
        const skinIdStr = parentSkin.id.toString();
        if (skinIdStr.length >= 3) {
          return skinIdStr.substring(0, Math.min(3, skinIdStr.length - 2));
        }
      }
    }

    return null;
  }

  private generateChromaURL(chromaName: string, colors: string[]): string {
    // Create skin slug from name
    const skinSlug = chromaName
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '') // Remove special characters except spaces and hyphens
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
      .replace(/^-|-$/g, '') // Remove leading/trailing hyphens
      .trim();

    // Convert hex colors to color names and remove duplicates
    const colorNames = colors.slice(0, 2).map(color =>
      getColorDisplayName(color).toLowerCase().trim()
    );
    const uniqueColorNames = [...new Set(colorNames)]; // Remove duplicates

    // Join colors with hyphen
    const chromaColorSlug = uniqueColorNames.join('-');

    return `${skinSlug}-${chromaColorSlug}`;
  }

  private findChromaInCommunityDragon(itemId: string, communityDragonSkins: any[]): any | null {
    // Check if communityDragonSkins is valid and iterable
    if (!communityDragonSkins || !Array.isArray(communityDragonSkins)) {
      return null;
    }

    // Search through all skins and their chromas to find matching contentId
    for (const skin of communityDragonSkins) {
      if (skin.chromas) {
        for (const chroma of skin.chromas) {
          if (chroma.contentId === itemId && chroma.skinClassification === 'kRecolor') {
            return {
              ...chroma,
              skinName: skin.name, // Add skin name for URL generation
              name: skin.name, // Use skin name as the chroma name for display
              colors: chroma.colors || [] // Extract colors array directly from Community Dragon
            };
          }
        }
      }
    }

    return null;
  }

  private async processChromas(entry: CatalogEntry, mainFulfillment: any, mainPayment: any, rotationCategory?: string): Promise<void> {
    // This method can be used for additional chroma processing if needed
    // For now, it's just a placeholder for the chroma processing logic
  }

  private async processCatalogEntry(entry: CatalogEntry, rotationMetadata: StorefrontDisplayMetadata, rotationCategory?: 'FEATURED' | 'BIWEEKLY' | 'WEEKLY' | 'DAILY'): Promise<ProcessedStoreItem | null> {
    if (!entry.purchaseUnits || entry.purchaseUnits.length === 0) {
        return null;
    }

    const mainFulfillment = entry.purchaseUnits[0].fulfillment;
    const mainPayment = entry.purchaseUnits[0].paymentOptions[0]?.payments[0];

    const { itemTypeId, name: fulfillmentName, itemId } = mainFulfillment;
    const { name: entryName, id, endTime } = entry;
    
    let imagePath: string | null = null;
    let videoPath: string | null = null;
    let displayName = fulfillmentName || entryName;
    let itemType = itemTypeId;
    let subType: ProcessedStoreItem['subType'];

    const searchName = displayName;

    try {
        if(entryName.includes("NexusFinisher")) {
            itemType = ItemTypeIDs.NEXUS_FINISHER;
            subType = 'nexus_finisher';
        }

        switch (itemType) {
            case ItemTypeIDs.NEXUS_FINISHER: {
                subType = 'nexus_finisher';
                // Prioritize matching by contentId from nexusfinishers.json which maps to itemId in the fulfillment response.
                const finisher = this.cdnCache.nexusFinishers?.find(f => f.contentId === mainFulfillment.itemId);

                if (finisher) {
                    imagePath = transformToCDNUrl(finisher.iconPath);
                    videoPath = transformToCDNUrl(finisher.videoPath);
                    displayName = finisher.translatedName; // Use the user-friendly name as requested
                } else {
                    // Fallback to name matching if contentId fails for any reason
                    console.warn(`Could not find Nexus Finisher by contentId: ${mainFulfillment.itemId}. Falling back to name matching for "${searchName}".`);
                    const fallbackFinisher = this.findBestMatch(searchName, this.cdnCache.nexusFinishers, 'translatedName') 
                        || this.findCloseMatch(searchName, this.cdnCache.nexusFinishers, 'name');
                    
                    if (fallbackFinisher) {
                        imagePath = transformToCDNUrl(fallbackFinisher.iconPath);
                        videoPath = transformToCDNUrl(fallbackFinisher.videoPath);
                        displayName = fallbackFinisher.translatedName || fallbackFinisher.name;
                    }
                }
                break;
            }
            case ItemTypeIDs.CHAMPION_SKIN: {
                // This itemTypeId is actually for chromas, so let's process them as chromas
                // First try to find chroma in Community Dragon skins data
                const communityDragonSkins = await this.getCommunityDragonSkins();
                const chromaData = this.findChromaInCommunityDragon(mainFulfillment.itemId, communityDragonSkins);

                if (chromaData) {
                    subType = 'chroma';
                    displayName = chromaData.name; // Use skin name for display

                    // Generate chroma image URL using chromaPath
                    if (chromaData.chromaPath) {
                        // Extract the path part after /lol-game-data/assets
                        const pathPart = chromaData.chromaPath.replace('/lol-game-data/assets', '').toLowerCase();
                        imagePath = `https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default${pathPart}`;
                    }

                    // Store the chroma data for URL generation (we'll add this to the item)
                    // The frontend will use this to generate the proper URL with colors
                } else {
                    // Fallback to skin processing if not a chroma
                    const skinByContentId = this.cdnCache.skins?.find(s => s.contentId === mainFulfillment.itemId);

                    if (skinByContentId) {
                        // Found a skin match by contentId
                        subType = 'skin';
                        // Handle tiered skins (like K/DA ALL OUT Seraphine) by checking for tiers
                        let splashPath: string
                        if (skinByContentId.questSkinInfo?.tiers && skinByContentId.questSkinInfo.tiers.length > 0) {
                            // For tiered skins, use the first tier's splash path
                            const firstTier = skinByContentId.questSkinInfo.tiers[0]
                            splashPath = firstTier.splashPath || firstTier.uncenteredSplashPath
                        } else {
                            // For regular skins, use the root level path
                            splashPath = skinByContentId.splashPath
                        }
                        imagePath = transformToCDNUrl(splashPath);
                        displayName = skinByContentId.name;
                    } else {
                        // Fallback to name matching for skins
                        subType = 'skin';
                        let skinMatch = this.findBestMatch(searchName, this.cdnCache.skins, 'name');
                        if (!skinMatch) {
                            skinMatch = this.findCloseMatch(searchName, this.cdnCache.skins, 'name');
                        }
                        if (skinMatch) {
                            // Handle tiered skins for name matching fallback
                            let splashPath: string
                            if (skinMatch.questSkinInfo?.tiers && skinMatch.questSkinInfo.tiers.length > 0) {
                                // For tiered skins, use the first tier's splash path
                                const firstTier = skinMatch.questSkinInfo.tiers[0]
                                splashPath = firstTier.splashPath || firstTier.uncenteredSplashPath
                            } else {
                                // For regular skins, use the root level path
                                splashPath = skinMatch.splashPath
                            }
                            imagePath = transformToCDNUrl(splashPath);
                            displayName = skinMatch.name;
                        }
                    }
                }
                break;
            }
            case ItemTypeIDs.EMOTE: {
            subType = 'emote';
            const emoteByContentId = this.cdnCache.emotes?.find(e => e.contentId === mainFulfillment.itemId);
            if(emoteByContentId) {
                imagePath = transformToCDNUrl(emoteByContentId.inventoryIcon);
            } else {
                const emote = this.findBestMatch(searchName, this.cdnCache.emotes, 'name') || this.findCloseMatch(searchName, this.cdnCache.emotes, 'name');
                if (emote) imagePath = transformToCDNUrl(emote.inventoryIcon);
            }
                break;
            }
            case ItemTypeIDs.SUMMONER_ICON: {
                subType = 'icon';
            const iconByContentId = this.cdnCache.summonerIcons?.find(i => i.contentId === mainFulfillment.itemId);
            if(iconByContentId) {
                imagePath = transformToCDNUrl(iconByContentId.imagePath);
            } else {
                const icon = this.findBestMatch(searchName, this.cdnCache.summonerIcons, 'title') || this.findCloseMatch(searchName, this.cdnCache.summonerIcons, 'title');
                    if (icon) imagePath = transformToCDNUrl(icon.imagePath);
                }
                break;
            }
            case ItemTypeIDs.WARD_SKIN: {
                subType = 'ward';
                // Fetch ward skin images using the itemId
                try {
                    const wardImageUrl = await this.getWardSkinImage(mainFulfillment.itemId);
                    const wardShadowImageUrl = await this.getWardSkinShadowImage(mainFulfillment.itemId);
                    if (wardImageUrl) {
                        imagePath = wardImageUrl;
                    }
                    if (wardShadowImageUrl) {
                        // We'll need to pass this through the return object
                        // For now, store it in a way we can access it later
                        (mainFulfillment as any).shadowImagePath = wardShadowImageUrl;
                    }
                } catch (error) {
                    console.error('Failed to fetch ward skin images:', error);
                }
                break;
            }
            default:
                // Fallback for bundles or other types by checking description
                if(entry.displayMetadata?.description?.toLowerCase().includes('bundle')) {
                    subType = 'bundle';
                    const skinMatch = this.findCloseMatch(searchName, this.cdnCache.skins, 'name');
                    if(skinMatch) {
                        // Handle tiered skins for bundle fallback
                        let splashPath: string
                        if (skinMatch.questSkinInfo?.tiers && skinMatch.questSkinInfo.tiers.length > 0) {
                            // For tiered skins, use the first tier's splash path
                            const firstTier = skinMatch.questSkinInfo.tiers[0]
                            splashPath = firstTier.splashPath || firstTier.uncenteredSplashPath
                        } else {
                            // For regular skins, use the root level path
                            splashPath = skinMatch.splashPath
                      }
                      console.log({
                        splashPath,
                        skinMatch
                      })
                        imagePath = transformToCDNUrl(splashPath);
                    }
                }
                break;
        }

      if (!imagePath) {
          imagePath = '/placeholder.svg'
      }

    } catch (e) {
      console.error(`Error processing store item "${displayName}":`, e);
    }

    // Prepare chroma data if this is a chroma
    let chromaData: { name: string; colors: string[] } | undefined;
    if (subType === 'chroma') {
      // Extract chroma data from the processing above
      const communityDragonSkins = await this.getCommunityDragonSkins();
      const foundChromaData = this.findChromaInCommunityDragon(mainFulfillment.itemId, communityDragonSkins);
      if (foundChromaData && foundChromaData.colors) {
        chromaData = {
          name: foundChromaData.name,
          colors: foundChromaData.colors
        };
      }
    }

    return {
      id,
      name: entryName,
      displayName,
      description: entry.displayMetadata?.description ?? null,
      cost: mainPayment?.finalDelta ?? 0,
      currency: mainPayment?.name ?? 'ME',
      imagePath,
      videoPath,
      type: itemType,
      subType,
      endTime,
      rotationCategory,
      shadowImagePath: (mainFulfillment as any).shadowImagePath || null,
      chromaData,
    };
  }

  /**
   * Fetch ward skin images using the itemId
   */
  private async getWardSkinImage(itemId: string): Promise<string | null> {
    try {
      // Fetch ward skins data from Community Dragon
      const response = await fetch('https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/en_gb/v1/ward-skins.json');

      if (!response.ok) {
        console.error('Failed to fetch ward skins data:', response.status);
        return null;
      }

      const wardSkinsData = await response.json();

      // Find the ward skin by contentId (which matches the itemId)
      const wardSkin = wardSkinsData.find((ward: any) => ward.contentId === itemId);

      if (!wardSkin || !wardSkin.wardImagePath) {
        console.warn('Ward skin not found or missing wardImagePath for itemId:', itemId);
        return null;
      }

      // Extract the path starting with "/content" and transform to CDN URL
      const wardImagePath = wardSkin.wardImagePath;
      const contentIndex = wardImagePath.indexOf('/content');

      if (contentIndex === -1) {
        console.warn('Invalid wardImagePath format:', wardImagePath);
        return null;
      }

      const relativePath = wardImagePath.substring(contentIndex + 1); // Remove leading slash
      const baseUrl = 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/';

      // Convert to lowercase for consistency with Community Dragon URLs
      const finalUrl = baseUrl + relativePath.toLowerCase();

      return finalUrl;
    } catch (error) {
      console.error('Error fetching ward skin image:', error);
      return null;
    }
  }

  /**
   * Fetch ward skin shadow image using the itemId
   */
  private async getWardSkinShadowImage(itemId: string): Promise<string | null> {
    try {
      // Fetch ward skins data from Community Dragon
      const response = await fetch('https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/en_gb/v1/ward-skins.json');

      if (!response.ok) {
        console.error('Failed to fetch ward skins data:', response.status);
        return null;
      }

      const wardSkinsData = await response.json();

      // Find the ward skin by contentId (which matches the itemId)
      const wardSkin = wardSkinsData.find((ward: any) => ward.contentId === itemId);

      if (!wardSkin || !wardSkin.wardShadowImagePath) {
        console.warn('Ward skin not found or missing wardShadowImagePath for itemId:', itemId);
        return null;
      }

      // Extract the path starting with "/content" and transform to CDN URL
      const wardShadowImagePath = wardSkin.wardShadowImagePath;
      const contentIndex = wardShadowImagePath.indexOf('/content');

      if (contentIndex === -1) {
        console.warn('Invalid wardShadowImagePath format:', wardShadowImagePath);
        return null;
      }

      const relativePath = wardShadowImagePath.substring(contentIndex + 1); // Remove leading slash
      const baseUrl = 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/';

      // Convert to lowercase for consistency with Community Dragon URLs
      const finalUrl = baseUrl + relativePath.toLowerCase();

      return finalUrl;
    } catch (error) {
      console.error('Error fetching ward skin shadow image:', error);
      return null;
    }
  }
}

export const storeService = new StoreService();