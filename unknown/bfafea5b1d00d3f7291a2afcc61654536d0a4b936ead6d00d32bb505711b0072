"use client"

import * as React from "react"
import { Check, ChevronsUpDown } from "lucide-react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

interface SkinLinesComboboxProps {
  selectedSkinLines: string[]
  onSelectionChange: (skinLines: string[]) => void
  availableSkinLines: string[]
  placeholder?: string
  className?: string
}

export function SkinLinesCombobox({
  selectedSkinLines,
  onSelectionChange,
  availableSkinLines,
  placeholder = "All Skin Lines",
  className
}: SkinLinesComboboxProps) {
  const [open, setOpen] = React.useState(false)

  const handleSelect = (skinLineName: string) => {
    const isSelected = selectedSkinLines.includes(skinLineName)
    
    if (isSelected) {
      // Remove skin line if already selected
      onSelectionChange(selectedSkinLines.filter(sl => sl !== skinLineName))
    } else {
      // Add skin line if not selected
      onSelectionChange([...selectedSkinLines, skinLineName])
    }
  }

  const getDisplayText = () => {
    if (selectedSkinLines.length === 0) {
      return placeholder
    } else if (selectedSkinLines.length === 1) {
      return selectedSkinLines[0]
    } else {
      return `${selectedSkinLines[0]} +${selectedSkinLines.length - 1}`
    }
  }

  return (
    <div className={className}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between bg-gray-900/40 border-gray-700/20 text-white hover:bg-gray-800/50 h-[40px]"
          >
            <span className="truncate text-left">
              {getDisplayText()}
            </span>
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0 bg-gray-900 border-gray-700">
          <Command>
            <CommandInput 
              placeholder="Search skin lines..." 
              className="h-9 bg-gray-900 border-gray-700 text-white placeholder-gray-400"
            />
            <CommandList className="max-h-[300px]">
              <CommandEmpty className="text-gray-400 py-6 text-center text-sm">
                No skin line found.
              </CommandEmpty>
              <CommandGroup>
                {availableSkinLines.map((skinLine) => (
                  <CommandItem
                    key={skinLine}
                    value={skinLine}
                    onSelect={() => handleSelect(skinLine)}
                    className="text-white hover:bg-purple-400/10 cursor-pointer"
                  >
                    <div className="flex items-center space-x-2 flex-1">
                      <span className="flex-1">{skinLine}</span>
                    </div>
                    <Check
                      className={cn(
                        "ml-auto h-4 w-4",
                        selectedSkinLines.includes(skinLine) ? "opacity-100" : "opacity-0"
                      )}
                    />
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  )
}
