"use client"

import { useState, useRef, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { ScrollArea } from "@/components/ui/scroll-area"
import { ChevronDown } from "lucide-react"
import { ChromaRarityIconsSimple } from "@/components/ui/chroma-rarity-icons"

interface MultiSelectChromaRaritiesProps {
  selectedRarities: string[]
  onSelectionChange: (rarities: string[]) => void
  availableRarities: string[]
  placeholder?: string
  className?: string
}

export function MultiSelectChromaRarities({
  selectedRarities,
  onSelectionChange,
  availableRarities,
  placeholder = "Select rarities...",
  className = ""
}: MultiSelectChromaRaritiesProps) {
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleRarityToggle = (rarity: string) => {
    if (selectedRarities.includes(rarity)) {
      onSelectionChange(selectedRarities.filter(r => r !== rarity))
    } else {
      onSelectionChange([...selectedRarities, rarity])
    }
  }

  const clearAll = () => {
    onSelectionChange([])
  }

  // Always show all predefined chroma rarities regardless of data availability, including Others and Legacy
  const displayRarities = ['Others', 'Legacy', 'Regular', 'Epic', 'Legendary', 'Mythic', 'Ultimate', 'Exalted', 'Transcendent']

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Trigger Button */}
      <Button
        variant="outline"
        onClick={() => setIsOpen(!isOpen)}
        className="w-full justify-between bg-gray-900/40 border-gray-700/20 text-white hover:bg-gray-800/50 h-[40px] items-center"
      >
        <span className="truncate text-left text-gray-400">
          {selectedRarities.length > 0
            ? `Rarities +${selectedRarities.length}`
            : placeholder
          }
        </span>
        <ChevronDown className={`h-4 w-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </Button>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute top-full left-0 right-0 z-50 mt-1 bg-gray-900 border border-gray-700 rounded-md shadow-lg">
          <div className="p-2">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-300">Select Rarities</span>
              {selectedRarities.length > 0 && (
                <button
                  onClick={clearAll}
                  className="text-xs text-gray-400 hover:text-white"
                >
                  Clear all
                </button>
              )}
            </div>

            <ScrollArea className="h-48">
              <div className="space-y-1">
                {displayRarities.map((rarity) => (
                  <div
                    key={rarity}
                    className="flex items-center space-x-2 p-2 hover:bg-gray-800/50 rounded cursor-pointer"
                    onClick={() => handleRarityToggle(rarity)}
                  >
                    <Checkbox
                      checked={selectedRarities.includes(rarity)}
                      onChange={() => handleRarityToggle(rarity)}
                      className="pointer-events-none border-gray-600 data-[state=checked]:bg-purple-600 data-[state=checked]:border-purple-600"
                    />
                    {rarity !== 'Others' && (
                      <div className="w-4 h-4 flex items-center justify-center flex-shrink-0">
                        <ChromaRarityIconsSimple
                          rarity={rarity}
                          size={14}
                        />
                      </div>
                    )}
                    <span className="text-sm text-gray-300 flex-1">{rarity}</span>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>
        </div>
      )}
    </div>
  )
}