"use client"

export function FiltersSkeleton() {
  return (
    <div className="mb-8 space-y-4">
      <div className="flex flex-col lg:flex-row gap-4">
        {/* Search Bar Skeleton */}
        <div className="relative flex-1">
          <div className="h-10 bg-gray-700/50 rounded-md animate-pulse"></div>
        </div>

        {/* Tags Filter Skeleton */}
        <div className="w-full lg:w-48">
          <div className="h-10 bg-gray-700/50 rounded-md animate-pulse"></div>
        </div>

        {/* Main Stats Filter Skeleton */}
        <div className="w-full lg:w-48">
          <div className="h-10 bg-gray-700/50 rounded-md animate-pulse"></div>
        </div>

        {/* Reset Filters Button Skeleton */}
        <div className="w-full lg:w-auto">
          <div className="h-10 bg-gray-700/50 rounded-md animate-pulse lg:w-32"></div>
        </div>
      </div>
    </div>
  )
}
