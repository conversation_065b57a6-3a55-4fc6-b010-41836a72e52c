/**
 * Example implementations of the reusable search hook for different data types
 * These can be used as templates for implementing search in other pages
 */

import { useSearchWithSuggestions, SearchConfig } from './use-search-with-suggestions'
import { ProcessedSkinData } from '@/lib/services/skin-service'
import { ProcessedChromaData } from '@/lib/services/chroma-service'

// Example: Search for skins
export function useSkinsSearch(skins: ProcessedSkinData[]) {
  const config: SearchConfig<ProcessedSkinData> = {
    items: skins,
    getSearchableText: (skin) => [
      skin.name,
      skin.champion,
      skin.tier,
      ...(skin.skinLines || [])
    ],
    getAvailableTerms: (skins) => {
      const terms = new Set<string>()
      skins.forEach(skin => {
        terms.add(skin.name)
        terms.add(skin.champion)
        terms.add(skin.tier)
        if (skin.skinLines) {
          skin.skinLines.forEach(line => terms.add(line))
        }
      })
      return Array.from(terms).filter(term => term && term.trim().length > 0)
    },
    debounceMs: 300,
    maxSuggestions: 3,
    minSimilarity: 0.4
  }

  return useSearchWithSuggestions(config)
}

// Example: Search for chromas
export function useChromasSearch(chromas: ProcessedChromaData[]) {
  const config: SearchConfig<ProcessedChromaData> = {
    items: chromas,
    getSearchableText: (chroma) => [
      chroma.name,
      chroma.championName,
      chroma.skinName,
      chroma.rarity
    ],
    getAvailableTerms: (chromas) => {
      const terms = new Set<string>()
      chromas.forEach(chroma => {
        terms.add(chroma.name)
        terms.add(chroma.championName)
        terms.add(chroma.skinName)
        terms.add(chroma.rarity)
      })
      return Array.from(terms).filter(term => term && term.trim().length > 0)
    },
    debounceMs: 300,
    maxSuggestions: 3,
    minSimilarity: 0.4
  }

  return useSearchWithSuggestions(config)
}

// Example: Search for champions (generic object)
interface Champion {
  name: string
  title: string
  roles: string[]
  tags: string[]
}

export function useChampionsSearch(champions: Champion[]) {
  const config: SearchConfig<Champion> = {
    items: champions,
    getSearchableText: (champion) => [
      champion.name,
      champion.title,
      ...champion.roles,
      ...champion.tags
    ],
    getAvailableTerms: (champions) => {
      const terms = new Set<string>()
      champions.forEach(champion => {
        terms.add(champion.name)
        terms.add(champion.title)
        champion.roles.forEach(role => terms.add(role))
        champion.tags.forEach(tag => terms.add(tag))
      })
      return Array.from(terms).filter(term => term && term.trim().length > 0)
    },
    debounceMs: 250, // Faster for champion search
    maxSuggestions: 5, // More suggestions for champions
    minSimilarity: 0.3 // More lenient for champion names
  }

  return useSearchWithSuggestions(config)
}

// Example: Search for simple string array
export function useSimpleStringSearch(items: string[]) {
  const config: SearchConfig<string> = {
    items,
    getSearchableText: (item) => item,
    getAvailableTerms: (items) => items.filter(item => item && item.trim().length > 0),
    debounceMs: 200,
    maxSuggestions: 4,
    minSimilarity: 0.5
  }

  return useSearchWithSuggestions(config)
}

// Example: Search with custom configuration
export function useCustomSearch<T>(
  items: T[],
  getSearchableText: (item: T) => string | string[],
  getAvailableTerms: (items: T[]) => string[],
  options?: {
    debounceMs?: number
    maxSuggestions?: number
    minSimilarity?: number
  }
) {
  const config: SearchConfig<T> = {
    items,
    getSearchableText,
    getAvailableTerms,
    debounceMs: options?.debounceMs ?? 300,
    maxSuggestions: options?.maxSuggestions ?? 3,
    minSimilarity: options?.minSimilarity ?? 0.4
  }

  return useSearchWithSuggestions(config)
}
