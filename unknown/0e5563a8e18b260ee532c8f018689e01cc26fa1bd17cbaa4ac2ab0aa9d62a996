// React hooks for champions data fetching

import { ChampionDetailed, ChampionListItem, ChampionWithStoreData } from '@/lib/types/league-api'
import { useCallback, useEffect, useMemo, useState } from 'react'

// Types for champion filters
export interface ChampionFilters {
  roles: string[]
  classes: string[]
  difficulty: [number, number]
  search: string
  sortField: 'name' | 'release' | 'price'
  sortDirection: 'asc' | 'desc'
}

interface ChampionFiltersInternal extends ChampionFilters {
  debouncedSearch: string
}

interface UseChampionsResult {
  champions: ChampionListItem[]
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
  refresh: () => Promise<void>
}

interface UseChampionDetailResult {
  champion: ChampionDetailed | null
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
}

// Hook for fetching all champions
export function useChampions(): UseChampionsResult {
  const [champions, setChampions] = useState<ChampionListItem[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchChampions = useCallback(async (forceRefresh = false) => {
    try {
      setLoading(true)
      setError(null)
      
      const url = forceRefresh ? '/api/champions?refresh=true' : '/api/champions'
      const response = await fetch(url)
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch champions')
      }
      
      setChampions(result.data)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
      setError(errorMessage)
      console.error('Error fetching champions:', err)
    } finally {
      setLoading(false)
    }
  }, [])

  const refetch = useCallback(() => fetchChampions(false), [fetchChampions])
  const refresh = useCallback(() => fetchChampions(true), [fetchChampions])

  useEffect(() => {
    fetchChampions()
  }, [fetchChampions])

  return {
    champions,
    loading,
    error,
    refetch,
    refresh
  }
}

// Hook for fetching individual champion details
export function useChampionDetail(championId: string | null): UseChampionDetailResult {
  const [champion, setChampion] = useState<ChampionDetailed | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchChampion = useCallback(async () => {
    if (!championId) {
      setChampion(null)
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)
      
      const response = await fetch(`/api/champions/${championId}`)
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch champion details')
      }
      
      setChampion(result.data)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
      setError(errorMessage)
      console.error(`Error fetching champion ${championId}:`, err)
    } finally {
      setLoading(false)
    }
  }, [championId])

  const refetch = useCallback(() => fetchChampion(), [fetchChampion])

  useEffect(() => {
    fetchChampion()
  }, [fetchChampion])

  return {
    champion,
    loading,
    error,
    refetch
  }
}

// Hook for filtering and searching champions
export function useChampionFilters(champions: ChampionWithStoreData[]) {
  const [filters, setFilters] = useState<ChampionFiltersInternal>({
    roles: [],
    classes: [],
    difficulty: [0, 10],
    search: '',
    sortField: 'name',
    sortDirection: 'asc',
    debouncedSearch: ''
  })

  // Debounce search term with 300ms delay
  useEffect(() => {
    const timer = setTimeout(() => {
      setFilters(prev => ({ ...prev, debouncedSearch: prev.search }))
    }, 300)

    return () => clearTimeout(timer)
  }, [filters.search])

  const filteredChampions = useMemo(() => {
    let result = [...champions]

    // Filter by roles (multiple selection)
    if (filters.roles.length > 0) {
      result = result.filter(champion => {
        // Always use recommended positions - no fallbacks
        if (champion.recommendedPositions && champion.recommendedPositions.length > 0) {
          return champion.recommendedPositions.some(position => filters.roles.includes(position))
        }
        // If no recommended positions, don't show the champion
        return false
      })
    }

    // Filter by classes (multiple selection)
    if (filters.classes.length > 0) {
      result = result.filter(champion => {
        if (!champion.tags) return false

        // Convert filter classes to lowercase for comparison with API tags
        const lowercaseFilterClasses = filters.classes.map(cls => cls.toLowerCase())

        return champion.tags.some(tag => lowercaseFilterClasses.includes(tag.toLowerCase()))
      })
    }

    // Filter by difficulty range
    result = result.filter(champion =>
      champion.difficulty >= filters.difficulty[0] &&
      champion.difficulty <= filters.difficulty[1]
    )

    // Filter by search term (only search champion names) - use debounced search
    if (filters.debouncedSearch.trim()) {
      const searchTerm = filters.debouncedSearch.toLowerCase().trim()
      result = result.filter(champion =>
        champion.name.toLowerCase().includes(searchTerm)
      )
    }

    // Apply sorting
    result.sort((a, b) => {
      let comparison = 0

      if (filters.sortField === 'name') {
        comparison = a.name.localeCompare(b.name)
      } else if (filters.sortField === 'release') {
        // Sort by actual release date from store data
        const dateA = a.storeData?.releaseDate || 0
        const dateB = b.storeData?.releaseDate || 0
        comparison = dateA - dateB
      } else if (filters.sortField === 'price') {
        // Sort by actual Blue Essence price from store data
        const priceA = a.storeData?.ip || 0
        const priceB = b.storeData?.ip || 0
        comparison = priceA - priceB
      }

      return filters.sortDirection === 'desc' ? -comparison : comparison
    })

    return result
  }, [champions, filters.debouncedSearch, filters.roles, filters.classes, filters.difficulty, filters.sortField, filters.sortDirection])

  const updateFilters = useCallback((newFilters: Partial<ChampionFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }))
  }, [])

  const resetFilters = useCallback(() => {
    setFilters({
      roles: [],
      classes: [],
      difficulty: [0, 10],
      search: '',
      sortField: 'name',
      sortDirection: 'asc',
      debouncedSearch: ''
    })
  }, [])

  // Create public filters interface (without internal debouncedSearch)
  const publicFilters: ChampionFilters = {
    roles: filters.roles,
    classes: filters.classes,
    difficulty: filters.difficulty,
    search: filters.search,
    sortField: filters.sortField,
    sortDirection: filters.sortDirection
  }

  return {
    filteredChampions,
    filters: publicFilters,
    updateFilters,
    resetFilters,
    storeDataLoading: false,
    isSearching: filters.search !== filters.debouncedSearch
  }
}

// Hook for free champion rotation
export function useFreeRotation() {
  const [freeChampions, setFreeChampions] = useState<ChampionListItem[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchFreeRotation = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/free-rotation')
      const result = await response.json()

      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch free rotation')
      }

      setFreeChampions(result.data.freeChampions)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
      setError(errorMessage)
      console.error('Error fetching free rotation:', err)
    } finally {
      setLoading(false)
    }
  }, [])

  useEffect(() => {
    fetchFreeRotation()
  }, [fetchFreeRotation])

  return {
    freeChampions,
    loading,
    error,
    refetch: fetchFreeRotation
  }
}
