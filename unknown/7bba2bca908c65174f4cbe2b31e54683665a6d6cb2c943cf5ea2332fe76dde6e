// API route for free champion rotation using loldb.info API

import { recommendedPositionsService } from '@/lib/services/recommended-positions-service'
import { normalizeChampionData } from '@/lib/utils/champion-data-utils'
import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    // Fetch champions data from loldb.info API
    const response = await fetch('https://api.loldb.info/api/champions/inventory/minimal')

    if (!response.ok) {
      throw new Error(`Failed to fetch champions data: ${response.status} ${response.statusText}`)
    }

    const responseData = await response.json()

    // Extract the champions array from the response
    const championsData = responseData.data

    // Fetch recommended positions data
    let recommendedPositionsData: { [key: string]: { recommendedPositions: string[] } } = {}
    try {
      recommendedPositionsData = await recommendedPositionsService.getAllRecommendedPositions()
    } catch (error) {
      console.warn('Failed to fetch recommended positions data:', error)
      // Continue without recommended positions data
    }

    // Helper function to transform loadScreenPath to CDN URL (same as /champions)
    const transformLoadScreenPathToCDN = (loadScreenPath: string): string => {
      // Extract the path after "/ASSETS/" and convert to lowercase
      const assetsIndex = loadScreenPath.indexOf('/ASSETS/')
      if (assetsIndex === -1) {
        throw new Error(`Invalid loadScreenPath format: ${loadScreenPath}`)
      }

      const pathAfterAssets = loadScreenPath.substring(assetsIndex + 1) // +1 to remove the leading slash
      const lowercasePath = pathAfterAssets.toLowerCase()

      // Construct the final Community Dragon URL
      const baseUrl = 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default'
      return `${baseUrl}/${lowercasePath}`
    }

    // Filter champions that are free to play and transform data
    const freeChampions = championsData
      .filter((champion: any) => champion.freeToPlay === true)
      .map((champion: any) => {
        // Transform the champion data to match our expected format
        const imageUrl = transformLoadScreenPathToCDN(champion.baseLoadScreenPath)

        // Get recommended positions for this champion
        const championKey = champion.id.toString()
        const recommendedPositions = recommendedPositionsData[championKey]?.recommendedPositions || []
        const mappedPositions = recommendedPositions.length > 0
          ? recommendedPositions
              .filter((pos: any) => pos && typeof pos === 'string')
              .map((pos: string) => recommendedPositionsService.mapPositionName(pos))
          : []

        // Handle roles - can be array or string
        let validTags: string[] = []
        if (Array.isArray(champion.roles)) {
          validTags = champion.roles.filter((tag: string) =>
            tag !== 'requirement_owned' &&
            !tag.includes('<<') &&
            !tag.includes('>>')
          )
        } else if (typeof champion.roles === 'string') {
          validTags = champion.roles.split(' ').filter((tag: string) =>
            tag !== 'requirement_owned' &&
            !tag.includes('<<') &&
            !tag.includes('>>')
          )
        }

        const rawChampionData = {
          id: champion.alias,
          name: champion.name, // Use the actual name instead of alias
          title: champion.title || '', // Use the title from the API
          difficulty: 5, // Default difficulty since not available
          image: imageUrl,
          slug: champion.alias.toLowerCase(),
          tags: validTags, // Use filtered tags
          championId: champion.id,
          recommendedPositions: mappedPositions // Add recommended positions
        }

        return normalizeChampionData(rawChampionData)
      })

    return NextResponse.json({
      success: true,
      data: {
        freeChampions,
        totalFree: freeChampions.length
      },
      isLiveData: true,
      timestamp: new Date().toISOString()
    }, {
      headers: {
        'Cache-Control': 'public, s-maxage=1800, stale-while-revalidate=3600',
        'CDN-Cache-Control': 'public, s-maxage=1800',
        'Vercel-CDN-Cache-Control': 'public, s-maxage=1800'
      }
    })

  } catch (error) {
    console.error('❌ Error fetching free rotation:', error)

    return NextResponse.json({
      success: false,
      error: 'Failed to fetch free rotation data',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, {
      status: 500,
      headers: {
        'Cache-Control': 'no-cache'
      }
    })
  }
}
