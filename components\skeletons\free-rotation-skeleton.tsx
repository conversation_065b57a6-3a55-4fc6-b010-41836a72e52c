import { Card, CardContent } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"

export function FreeRotationInfoSkeleton() {
  return (
    <div className="free-rotation-info-grid">
      {Array.from({ length: 3 }).map((_, index) => (
        <Card key={index} className="bg-gray-900/40 border-orange-700/20">
          <CardContent className="p-6 text-center">
            <Skeleton className="h-8 w-8 mx-auto mb-3" />
            <Skeleton className="h-6 w-24 mx-auto mb-2" />
            <Skeleton className="h-4 w-32 mx-auto" />
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

export function FreeRotationChampionsSkeleton({ count = 14, title = "Free Champion Rotation" }: { count?: number; title?: string }) {
  return (
    <div className="mb-8">
      <div className="flex items-center mb-6">
        <Skeleton className="h-6 w-6 mr-2" />
        <Skeleton className="h-8 w-64" />
      </div>
      <div className="free-rotation-champion-grid">
        {Array.from({ length: count }).map((_, index) => (
          <Card key={index} className="bg-gray-900/40 border-orange-700/20 w-full min-w-[140px] sm:min-w-[160px] max-w-[160px] sm:max-w-[280px] aspect-[2/3] flex flex-col">
            <CardContent className="p-0 flex flex-col h-full relative overflow-hidden rounded-lg">
              {/* Champion Image Skeleton - Full Card Background */}
              <div className="absolute inset-0 w-full h-full">
                <Skeleton className="w-full h-full" />
              </div>

              {/* Top Shadow Overlay */}
              <div className="absolute inset-x-0 top-0 h-20 bg-gradient-to-b from-black/40 via-black/20 to-transparent pointer-events-none"></div>

              {/* Role and Class Icons Overlay Skeleton */}
              <div className="absolute top-2 right-2 sm:top-3 sm:right-3 flex flex-col space-y-1 sm:space-y-2 z-10">
                <div className="bg-black/60 backdrop-blur-sm rounded-full p-1.5 sm:p-2">
                  <Skeleton className="w-4 h-4 sm:w-5 sm:h-5 rounded-full" />
                </div>
                <div className="bg-black/60 backdrop-blur-sm rounded-full p-1.5 sm:p-2">
                  <Skeleton className="w-4 h-4 sm:w-5 sm:h-5 rounded-full" />
                </div>
              </div>

              {/* Champion Name Skeleton - Bottom */}
              <div className="absolute bottom-0 left-0 right-0 p-3 sm:p-4 md:p-5 z-10 text-center">
                <Skeleton className="h-4 sm:h-5 md:h-6 w-20 sm:w-24 md:w-28 mx-auto" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}


