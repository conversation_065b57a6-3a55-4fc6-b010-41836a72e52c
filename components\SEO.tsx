import { Metadata } from 'next'

// Default SEO configuration for LoLDB
const DEFAULT_SEO = {
  siteName: 'LoLDB',
  siteUrl: 'https://loldb.info', // Update with your actual domain
  defaultTitle: 'LoLDB - Your Ultimate League of Legends Database',
  defaultDescription: 'LoLDB, the Complete League of Legends database with champions, skins, chromas, items, mythic shop content, and more! Find everything you need for League.',
  defaultKeywords: ['League of Legends', 'LoL', 'Champions', 'Items', 'Database', 'Riot Games', 'Skins', 'Chromas', 'Mythic Shop'],
  twitterHandle: '@loldb', // Update with your actual Twitter handle
  defaultImage: '/images/loldb-og-image.png', // Create this image (1200x630px recommended)
  favicon: '/favicon.ico',
  themeColor: '#c89b3c', // LoL gold color
}

export interface SEOProps {
  title?: string
  description?: string
  keywords?: string[]
  image?: string
  url?: string
  type?: 'website' | 'article' | 'profile'
  publishedTime?: string
  modifiedTime?: string
  author?: string
  section?: string
  tags?: string[]
  noIndex?: boolean
  noFollow?: boolean
  canonical?: string
}

/**
 * Generate metadata for Next.js pages
 * This function should be used in page.tsx files with generateMetadata
 */
export function generateSEOMetadata(props: SEOProps = {}): Metadata {
  const {
    title,
    description = DEFAULT_SEO.defaultDescription,
    keywords = DEFAULT_SEO.defaultKeywords,
    image = DEFAULT_SEO.defaultImage,
    url,
    type = 'website',
    publishedTime,
    modifiedTime,
    author,
    section,
    tags,
    noIndex = false,
    noFollow = false,
    canonical,
  } = props

  // Construct full title
  const fullTitle = title 
    ? `${title} | ${DEFAULT_SEO.siteName}`
    : DEFAULT_SEO.defaultTitle

  // Construct full URL
  const fullUrl = url 
    ? `${DEFAULT_SEO.siteUrl}${url.startsWith('/') ? url : `/${url}`}`
    : DEFAULT_SEO.siteUrl

  // Construct full image URL
  const fullImageUrl = image.startsWith('http') 
    ? image 
    : `${DEFAULT_SEO.siteUrl}${image.startsWith('/') ? image : `/${image}`}`

  // Combine keywords
  const allKeywords = [...new Set([...keywords, ...DEFAULT_SEO.defaultKeywords])]

  // Robots directive
  const robots = {
    index: !noIndex,
    follow: !noFollow,
    googleBot: {
      index: !noIndex,
      follow: !noFollow,
      'max-video-preview': -1,
      'max-image-preview': 'large' as 'large',
      'max-snippet': -1,
    },
  }

  const metadata: Metadata = {
    title: fullTitle,
    description,
    keywords: allKeywords,
    authors: author ? [{ name: author }] : [{ name: 'LoLDB Team' }],
    creator: 'LoLDB',
    publisher: 'LoLDB',
    robots: {

      ...robots,
    },
    alternates: canonical ? { canonical } : undefined,
    
    // Open Graph
    openGraph: {
      type,
      siteName: DEFAULT_SEO.siteName,
      title: fullTitle,
      description,
      url: fullUrl,
      images: [
        {
          url: fullImageUrl,
          width: 1200,
          height: 630,
          alt: title || DEFAULT_SEO.defaultTitle,
        },
      ],
      locale: 'en_US',
      ...(publishedTime && { publishedTime }),
      ...(modifiedTime && { modifiedTime }),
      ...(author && { authors: [author] }),
      ...(section && { section }),
      ...(tags && { tags }),
    },

    // Twitter
    twitter: {
      card: 'summary_large_image',
      site: DEFAULT_SEO.twitterHandle,
      creator: DEFAULT_SEO.twitterHandle,
      title: fullTitle,
      description,
      images: [fullImageUrl],
    },

    // Additional metadata
    applicationName: DEFAULT_SEO.siteName,
    referrer: 'origin-when-cross-origin',
    formatDetection: {
      email: false,
      address: false,
      telephone: false,
    },
    
    // Icons
    icons: {
      icon: DEFAULT_SEO.favicon,
      shortcut: DEFAULT_SEO.favicon,
      apple: '/LoLDB-PNG.png',
    },

    // Manifest
    manifest: '/manifest.json',

    // Other
    other: {
      'theme-color': DEFAULT_SEO.themeColor,
      'msapplication-TileColor': DEFAULT_SEO.themeColor,
      'apple-mobile-web-app-capable': 'yes',
      'apple-mobile-web-app-status-bar-style': 'default',
      'apple-mobile-web-app-title': DEFAULT_SEO.siteName,
    },
  }

  return metadata
}

/**
 * Generate champion-specific metadata
 */
export function generateChampionSEO(championData: {
  name: string
  title: string
  role?: string
  description?: string
  image?: string
}): Metadata {
  const { name, title, role, description, image } = championData
  
  const championTitle = `${name}, ${title}`
  const championDescription = description || 
    `Learn about ${name}, ${title} in League of Legends. View ${name}'s abilities, skins, stats, and more on LoLDB.`
  
  const championKeywords = [
    name,
    title,
    `${name} champion`,
    `${name} abilities`,
    `${name} skins`,
    `${name} build`,
    role && `${role} champion`,
    'League of Legends champion',
  ].filter(Boolean) as string[]

  return generateSEOMetadata({
    title: championTitle,
    description: championDescription,
    keywords: championKeywords,
    image,
    url: `/champions/${name.toLowerCase()}`,
    type: 'article',
    section: 'Champions',
    tags: [name, role].filter(Boolean) as string[],
  })
}

/**
 * Generate skin-specific metadata
 */
export function generateSkinSEO(skinData: {
  name: string
  championName: string
  rarity?: string
  price?: string
  description?: string
  image?: string
}): Metadata {
  const { name, championName, rarity, price, description, image } = skinData
  
  const skinTitle = `${name} - ${championName} Skin`
  const skinDescription = description || 
    `${name} skin for ${championName} in League of Legends. ${rarity ? `${rarity} rarity skin` : 'Skin'} ${price ? `priced at ${price}` : ''} available on LoLDB.`
  
  const skinKeywords = [
    name,
    championName,
    `${name} skin`,
    `${championName} skin`,
    `${championName} ${name}`,
    rarity && `${rarity} skin`,
    'League of Legends skin',
  ].filter(Boolean) as string[]

  return generateSEOMetadata({
    title: skinTitle,
    description: skinDescription,
    keywords: skinKeywords,
    image,
    url: `/skins/${name.toLowerCase().replace(/\s+/g, '-')}`,
    type: 'article',
    section: 'Skins',
    tags: [championName, rarity].filter(Boolean) as string[],
  })
}

/**
 * Generate item-specific metadata
 */
export function generateItemSEO(itemData: {
  name: string
  description?: string
  tags?: string[]
  price?: string
  image?: string
}): Metadata {
  const { name, description, tags, price, image } = itemData

  const itemTitle = `${name} - League of Legends Item`
  const itemDescription = description ||
    `${name} item in League of Legends. ${price ? `Costs ${price} gold.` : ''} View stats, build path, and more on LoLDB.`

  const itemKeywords = [
    name,
    `${name} item`,
    'League of Legends item',
    ...(tags || []),
  ]

  return generateSEOMetadata({
    title: itemTitle,
    description: itemDescription,
    keywords: itemKeywords,
    image,
    url: `/items/${name.toLowerCase().replace(/\s+/g, '-')}`,
    type: 'article',
    section: 'Items',
    tags: tags || [],
  })
}

/**
 * Generate rarity-specific metadata for skin rarity pages
 */
export function generateSkinRaritySEO(rarityData: {
  rarity: string
  slug: string
  description?: string
}): Metadata {
  const { rarity, slug, description } = rarityData

  // Get rarity-specific descriptions
  const rarityDescriptions: Record<string, string> = {
    'Regular': 'Standard skin quality with basic visual changes',
    'Epic': 'High-quality skins with new models, textures, and effects',
    'Legendary': 'Premium skins with new voice lines and animations',
    'Ultimate': 'Evolving skins that change throughout the game',
    'Mythic': 'Exclusive skins with unique prestige elements',
    'Transcendent': 'Highest tier skins with transformative features',
    'Exalted': 'Ultra-rare skins with exceptional quality',
    'Legacy': 'Limited-time skins no longer available for purchase'
  }

  const rarityTitle = `League of Legends ${rarity} Skins`
  const rarityDescription = description ||
    `Browse all ${rarity} skins in League of Legends. ${rarityDescriptions[rarity] || `${rarity} quality skins`}. Find your perfect ${rarity.toLowerCase()} skin on LoLDB.`

  const rarityKeywords = [
    `${rarity} skins`,
    `${rarity} League of Legends skins`,
    `${rarity} LoL skins`,
    `${rarity} rarity`,
    `${rarity} champion skins`,
    'League of Legends skins',
    'LoL skins',
    'champion skins',
    'skin database',
    'skin collection'
  ]

  return generateSEOMetadata({
    title: rarityTitle,
    description: rarityDescription,
    keywords: rarityKeywords,
    url: `/skins/rarity/${slug}`,
    canonical: `https://loldb.info/skins/rarity/${slug}`,
    type: 'website',
    section: 'Skins',
    tags: [rarity, 'Skins', 'Rarity'],
  })
}

export default generateSEOMetadata
