/**
 * Storefronts Service - Handles fetching and processing mythic shop data
 */

import { INVENTORY_TYPE_IDS, getInventoryTypeName } from '@/lib/constants/inventory-types'

// Raw API response interfaces
export interface StorefrontsApiResponse {
  success: boolean
  data: StorefrontData[]
}

export interface StorefrontData {
  id: string
  name: string
  catalogEntries: CatalogEntry[]
  displayMetadata: {
    description: string
    endDate: string
    startDate: string
    shoppefront: {
      categories: string[]
      id: string
    }
  }
}

export interface CatalogEntry {
  id: string
  name: string
  endTime: string
  purchaseUnits: PurchaseUnit[]
}

export interface PurchaseUnit {
  fulfillment: {
    itemId: string
    inventoryTypeId: string
    name: string
  }
  paymentOptions: PaymentOption[]
}

export interface PaymentOption {
  payments: Payment[]
}

export interface Payment {
  finalDelta: number
  currencyType: string
}

// Processed data interfaces
export interface ProcessedStorefrontItem {
  id: string
  itemId: string
  inventoryTypeId: string
  inventoryType: string
  name: string
  price: number
  currency: string
  endTime: string
  category: 'featured' | 'bi-weekly' | 'weekly' | 'daily'
  imageUrl?: string
  videoUrl?: string
}

interface StorefrontsCache {
  data: ProcessedStorefrontItem[]
  timestamp: number
}

class StorefrontsService {
  private cache: StorefrontsCache | null = null
  private readonly CACHE_TTL = 1000 * 60 * 15 // 15 minutes
  private readonly API_URL = 'https://api.loldb.info/api/store/storefronts'

  /**
   * Get all storefront items
   */
  async getAllStorefrontItems(): Promise<ProcessedStorefrontItem[]> {
    // Check cache first
    if (this.cache && this.isCacheValid()) {
      
      return this.cache.data
    }

    try {
      
      const response = await fetch(this.API_URL)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: Failed to fetch storefront data`)
      }

      const apiResponse = await response.json()

      if (!apiResponse.success) {
        throw new Error('API returned unsuccessful response')
      }

      const storefronts = apiResponse.data?.data || []
      
      const processedItems = await this.processStorefrontData(storefronts)

      // Update cache
      this.cache = {
        data: processedItems,
        timestamp: Date.now()
      }

      
      return processedItems

    } catch (error) {
      console.error('❌ STOREFRONTS SERVICE: Error fetching data:', error)
      throw error
    }
  }

  /**
   * Get items by category
   */
  async getItemsByCategory(category: 'featured' | 'bi-weekly' | 'weekly' | 'daily'): Promise<ProcessedStorefrontItem[]> {
    const allItems = await this.getAllStorefrontItems()
    return allItems.filter(item => item.category === category)
  }

  // Removed getFeaturedItems method - no longer needed

  /**
   * Process raw storefront data into usable format
   */
  private async processStorefrontData(storefronts: StorefrontData[]): Promise<ProcessedStorefrontItem[]> {
    const processedItems: ProcessedStorefrontItem[] = []

    for (const storefront of storefronts) {
      for (const catalogEntry of storefront.catalogEntries) {
        for (const purchaseUnit of catalogEntry.purchaseUnits) {
          const { fulfillment, paymentOptions } = purchaseUnit

          // Get the first payment option (usually the primary one)
          const primaryPayment = paymentOptions[0]?.payments[0]
          if (!primaryPayment) continue

          const inventoryType = getInventoryTypeName(fulfillment.itemTypeId)

          const processedItem: ProcessedStorefrontItem = {
            id: `${storefront.id}-${catalogEntry.id}-${fulfillment.itemId}`,
            itemId: fulfillment.itemId,
            inventoryTypeId: fulfillment.itemTypeId,
            inventoryType,
            name: fulfillment.name,
            price: primaryPayment.finalDelta,
            currency: primaryPayment.name || 'lol_mythic_essence',
            endTime: catalogEntry.endTime,
            category: this.categorizeStorefront(storefront),
          }

          // Add media URLs based on inventory type
          await this.addMediaUrls(processedItem)

          processedItems.push(processedItem)
        }
      }
    }

    return processedItems
  }

  /**
   * Categorize storefront based on displayMetadata categories
   */
  private categorizeStorefront(storefront: StorefrontData): 'featured' | 'bi-weekly' | 'weekly' | 'daily' {
    const categories = storefront.displayMetadata?.shoppefront?.categories || []

    // Check categories array first
    for (const category of categories) {
      const cat = category.toLowerCase()
      if (cat === 'featured') return 'featured'
      if (cat === 'biweekly' || cat === 'bi-weekly') return 'bi-weekly'
      if (cat === 'weekly') return 'weekly'
      if (cat === 'daily') return 'daily'
    }

    // Fallback to name-based categorization
    const name = storefront.name.toLowerCase()
    if (name.includes('featured') || name.includes('highlight')) {
      return 'featured'
    } else if (name.includes('bi-weekly') || name.includes('biweekly')) {
      return 'bi-weekly'
    } else if (name.includes('weekly')) {
      return 'weekly'
    } else if (name.includes('daily')) {
      return 'daily'
    }

    // Default to featured
    return 'featured'
  }

  /**
   * Add appropriate media URLs based on inventory type
   */
  private async addMediaUrls(item: ProcessedStorefrontItem): Promise<void> {
    try {
      if (item.inventoryTypeId === INVENTORY_TYPE_IDS.NEXUS_FINISHER) {
        // For nexus finishers, try to get video and image from nexusfinishers API
        const nexusData = await this.getNexusFinisherData(item.itemId)
        if (nexusData) {
          item.videoUrl = nexusData.videoUrl
          item.imageUrl = nexusData.imageUrl
        }
      } else if (item.inventoryTypeId === INVENTORY_TYPE_IDS.EMOTE) {
        // For emotes, get image from summoner-emotes API
        const emoteData = await this.getEmoteData(item.itemId)
        if (emoteData) {
          item.imageUrl = emoteData.imageUrl
        }
      }
    } catch (error) {
      console.warn(`⚠️ Failed to get media URLs for item ${item.itemId}:`, error)
    }
  }

  /**
   * Get nexus finisher data (placeholder - implement based on actual API)
   */
  private async getNexusFinisherData(itemId: string): Promise<{ videoUrl: string; imageUrl: string } | null> {
    // TODO: Implement actual nexus finisher API call
    // For now, return placeholder data
    return {
      videoUrl: 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/assets/loadouts/nexusfinishers/noxusfinisher/noxusfinishervideo.webm',
      imageUrl: 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/assets/loadouts/nexusfinishers/noxusfinisher/noxusfinishersplash.png'
    }
  }

  /**
   * Get emote data (placeholder - implement based on actual API)
   */
  private async getEmoteData(itemId: string): Promise<{ imageUrl: string } | null> {
    // TODO: Implement actual emote API call
    // For now, return placeholder data
    return {
      imageUrl: 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/assets/loadouts/summoneremotes/events/exalted/4838_exalted_mordekaiser_animated_emote_inventory.accessories_15_5_delayed.png'
    }
  }

  /**
   * Check if cache is still valid
   */
  private isCacheValid(): boolean {
    if (!this.cache) return false
    return (Date.now() - this.cache.timestamp) < this.CACHE_TTL
  }

  /**
   * Clear the cache
   */
  clearCache(): void {
    this.cache = null
    
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    if (!this.cache) {
      return {
        cached: false,
        timestamp: null,
        itemCount: 0,
        age: 0,
        ttl: this.CACHE_TTL
      }
    }

    const age = Date.now() - this.cache.timestamp
    return {
      cached: true,
      timestamp: this.cache.timestamp,
      itemCount: this.cache.data.length,
      age,
      ttl: this.CACHE_TTL,
      valid: age < this.CACHE_TTL
    }
  }
}

// Export singleton instance
export const storefrontsService = new StorefrontsService()
