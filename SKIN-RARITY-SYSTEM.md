# 🎨 Skin Rarity System Implementation

## Overview

This document describes the comprehensive skin rarity system implemented for LoLDB, which integrates with the Community Dragon API to provide accurate skin rarity information, legacy status, and proper icon display across all skin-related pages.

## 🏗️ Architecture

### Core Components

1. **SkinRarityService** (`lib/api/skin-rarity-service.ts`)
   - Fetches and caches skin data from Community Dragon API
   - Provides rarity normalization and icon URL generation
   - Implements singleton pattern with 24-hour caching

2. **SkinRarityIcons Component** (`components/ui/skin-rarity-icons.tsx`)
   - Reusable component for displaying rarity and legacy icons
   - Handles base skin logic (no icons for base skins)
   - Supports tooltips and responsive sizing

3. **Enhanced Type Definitions** (`lib/types/league-api.ts`)
   - Extended interfaces for Community Dragon skin data
   - Enhanced SkinData interface with rarity information

4. **API Integration** (`app/api/skins/rarity/route.ts`)
   - RESTful API for accessing skin rarity data
   - Supports single skin, multiple skins, and bulk operations
   - Includes cache management endpoints

## 🎯 Key Features

### Rarity Detection Logic

```typescript
// Base skins (isBase: true) → No icons displayed
// Regular skins (kNoRarity) → Regular rarity icon
// Epic skins (kEpic) → Epic rarity icon
// Legacy skins (isLegacy: true) → Legacy icon + rarity icon
```

### Supported Rarity Tiers

- **Regular** (`kNoRarity`, `kRare`) → Standard skin icon
- **Epic** (`kEpic`) → Epic gem icon
- **Legendary** (`kLegendary`) → Legendary gem icon
- **Ultimate** (`kUltimate`) → Ultimate gem icon
- **Mythic** (`kMythic`) → Mythic gem icon
- **Transcendent** (`kTranscendent`) → Transcendent gem icon
- **Exalted** (`kExalted`) → Exalted gem icon

### Legacy Status

- Legacy skins display both rarity icon AND legacy icon
- Icons are positioned side by side
- Tooltips provide detailed descriptions

## 🔧 Usage Examples

### Using SkinRarityIcons Component

```tsx
import { SkinRarityIcons } from '@/components/ui/skin-rarity-icons'

// Basic usage
<SkinRarityIcons
  rarity="Epic"
  isLegacy={true}
  isBase={false}
  size={16}
  showTooltip={true}
/>

// Base skin (no icons will be displayed)
<SkinRarityIcons
  rarity="Regular"
  isLegacy={false}
  isBase={true}
/>
```

### Using SkinRarityService

```typescript
import { skinRarityService, SkinRarityService } from '@/lib/api/skin-rarity-service'

// Get single skin data
const skinData = await skinRarityService.getSkinRarityData(1013)
if (skinData) {
  const normalizedRarity = SkinRarityService.normalizeRarity(skinData.rarity)
  const isLegacy = skinData.isLegacy
  const isBase = skinData.isBase
}

// Get multiple skins
const skinIds = [1000, 1001, 1013]
const skinsData = await skinRarityService.getMultipleSkinRarityData(skinIds)

// Calculate skin ID from champion key and skin number
const skinId = SkinRarityService.calculateSkinId("1", 13) // Annie skin 13 = 1013
```

### API Endpoints

```bash
# Get all skins rarity data
GET /api/skins/rarity

# Get specific skin
GET /api/skins/rarity?skinId=1013

# Get multiple skins
GET /api/skins/rarity?skinIds=1000,1001,1013

# Clear cache
POST /api/skins/rarity
Content-Type: application/json
{"action": "clear-cache"}

# Get cache stats
POST /api/skins/rarity
Content-Type: application/json
{"action": "get-stats"}
```

## 🔄 Integration Points

### Champion Pages (`/champions/slug`)

- Champion data transformer automatically fetches rarity data
- Skin carousel displays rarity and legacy icons
- Skin viewer shows enhanced rarity information

### Skins Pages (`/skins/*`)

- All skin listing pages can use the rarity system
- Consistent icon display across all skin cards
- Filter by rarity tier support

### Data Flow

```
Community Dragon API → SkinRarityService → Champion Data Transformer → UI Components
```

## 🧪 Testing

### Test Endpoint

```bash
GET /api/test-skin-rarity
```

This endpoint tests the system with known skin IDs and provides detailed results.

### Example Test Results

```json
{
  "success": true,
  "data": {
    "individualResults": [
      {
        "skinId": 1000,
        "name": "Annie",
        "isBase": true,
        "originalRarity": "kNoRarity",
        "normalizedRarity": "Regular",
        "isLegacy": false
      },
      {
        "skinId": 1013,
        "name": "Lunar Beast Annie",
        "isBase": false,
        "originalRarity": "kEpic",
        "normalizedRarity": "Epic",
        "isLegacy": true
      }
    ]
  }
}
```

## 🎨 Icon URLs

The system uses Community Dragon's official icon assets:

- **Regular**: `summoner-icon-rare.png`
- **Epic**: `rarity-gem-icons/cn-gem-3.png`
- **Legendary**: `rarity-gem-icons/legendary.png`
- **Ultimate**: `rarity-gem-icons/ultimate.png`
- **Legacy**: `summoner-icon/icon-legacy.png`

## 🚀 Performance

- **Caching**: 24-hour cache with stale-while-revalidate
- **Batch Operations**: Efficient multiple skin fetching
- **Memory Management**: Singleton service with controlled cache size
- **Error Handling**: Graceful fallbacks to name-based rarity detection

## 🔮 Future Enhancements

- Filter skins by rarity tier
- Rarity-based sorting options
- Advanced skin statistics
- Rarity trend analysis
- Integration with skin pricing data
