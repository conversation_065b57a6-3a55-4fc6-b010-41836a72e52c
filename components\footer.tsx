"use client";

import Link from "next/link";
import Image from "next/image";
import {
  ChampionsIcon,
  SkinsIcon,
  ItemsIcon,
  ShopIcon,
  MythicIcon,
  SeasonIcon,
  DiscountsIcon,
  CurrencyConversionIcon,
} from "./navigation/navigation-data";

export default function Footer() {
  const footerLinks = [
    {
      title: "Champions",
      links: [
        {
          label: "All Champions",
          href: "/champions",
          icon: ChampionsIcon,
        },
        {
          label: "Free Rotation",
          href: "/champions/free-rotation",
          icon: ChampionsIcon,
        },
      ],
    },
    {
      title: "Cosmetics",
      links: [
        {
          label: "All Skins",
          href: "/skins",
          icon: SkinsIcon,
        },
        {
          label: "Chromas",
          href: "/skins/chromas",
          icon: SkinsIcon,
        },
      ],
    },
    {
      title: "Items & Shop",
      links: [
        {
          label: "Items",
          href: "/items",
          icon: ItemsIcon,
        },
        {
          label: "Shop",
          href: "/shop",
          icon: ShopIcon,
        },
        {
          label: "Discounts",
          href: "/shop/discounts",
          icon: DiscountsIcon,
        },
        {
          label: "Mythic Shop",
          href: "/shop/mythic",
          icon: MythicIcon,
        },
      ],
    },
    {
      title: "Season & More",
      links: [
        {
          label: "Season Countdown",
          href: "/season/countdown",
          icon: SeasonIcon,
        },
        {
          label: "Conversion Tool",
          href: "/conversion-tool",
          icon: CurrencyConversionIcon,
        },
      ],
    },
  ];

  return (
    <footer className="border-t border-gray-700/50 bg-gray-950/80 backdrop-blur-sm py-12 ml-0 md:ml-72 mt-auto">
      <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8 mb-8">
          {/* Logo and Description - First Column */}
          <div className="space-y-4">
            <div className="flex items-center space-x-3 mb-4">
              <Image
                src="/images/LoLDB-Logo-Secondary.svg"
                alt="LoLDB Logo"
                width={40}
                height={40}
                className="w-10 h-10"
              />
              <Image
                src="/images/LoLDB-Logo-Text.svg"
                alt="LoLDB Text"
                width={80}
                height={24}
                className="h-6 w-auto"
              />
            </div>
            <p className="text-gray-400 text-sm max-w-md">
              Your ultimate League of Legends database with champions, items, skins, and more.
            </p>
          </div>

          {/* Service Links */}
          {footerLinks.map((section, index) => (
            <div key={index} className="space-y-4">
              <h3 className="text-lg font-semibold text-white mb-4 border-b border-gray-700/30 pb-2">
                {section.title}
              </h3>
              <ul className="space-y-3">
                {section.links.map((link, linkIndex) => (
                  <li key={linkIndex}>
                    <Link
                      href={link.href}
                      className="text-gray-400 hover:text-orange-400 transition-colors duration-200 text-sm"
                    >
                      {link.label}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}

          {/* About Section */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white mb-4 border-b border-gray-700/30 pb-2">
              About
            </h3>
            <ul className="space-y-3">
              <li>
                <Link
                  href="/blog"
                  className="text-gray-400 hover:text-orange-400 transition-colors duration-200 text-sm"
                >
                  Blog
                </Link>
              </li>
              <li>
                <Link
                  href="/privacy"
                  className="text-gray-400 hover:text-orange-400 transition-colors duration-200 text-sm"
                >
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link
                  href="/terms"
                  className="text-gray-400 hover:text-orange-400 transition-colors duration-200 text-sm"
                >
                  Terms of Service
                </Link>
              </li>
              <li>
                <Link
                  href="/contact"
                  className="text-gray-400 hover:text-orange-400 transition-colors duration-200 text-sm"
                >
                  Contact
                </Link>
              </li>
            </ul>
          </div>
        </div>

        {/* Divider */}
        <div className="border-t border-gray-700/30 pt-8">
          {/* Copyright */}
          <div className="text-center">
            <p className="text-gray-400 text-sm">
              © 2025 loldb.info - Your ultimate League of Legends database
            </p>
            <p className="text-xs text-gray-500 mt-2">
              Not affiliated with Riot Games. League of Legends is a trademark
              of Riot Games, Inc.
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
}
