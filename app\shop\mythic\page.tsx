"use client";

import { MythicIcon } from "@/components/navigation/navigation-data";
import SharedLayout from "@/components/shared-layout";
import { Skeleton } from "@/components/ui/skeleton";
import {
  ProcessedAPIResponse,
  ProcessedMythicStorefront,
  ProcessedStoreItem,
} from "@/lib/types/store";
import { transformToCDNUrl } from "@/lib/utils/cdn";
import { getColorDisplayName } from "@/lib/utils/chroma-utils";
import { Clock } from "lucide-react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

// TypeScript interfaces for the raw storefront API response
interface StorefrontApiResponse {
  success: boolean;
  data: {
    data: StorefrontData[];
  };
}

interface StorefrontData {
  id: string;
  name: string;
  catalogEntries: CatalogEntry[];
  displayMetadata: {
    description: string;
    endDate: string;
    startDate: string;
    shoppefront: {
      categories: string[];
      id: string;
      bundleType?: string;
    };
  };
}

interface CatalogEntry {
  id: string;
  name: string;
  endTime: string;
  displayMetadata?: {
    description?: string;
    shoppefront?: {
      bundleType?: string;
    };
  };
  purchaseUnits: PurchaseUnit[];
}

interface PurchaseUnit {
  fulfillment: {
    itemId: string;
    itemTypeId: string;
    name: string;
    finalDelta: number;
  };
  paymentOptions: PaymentOption[];
}

interface PaymentOption {
  payments: Payment[];
}

interface Payment {
  finalDelta: number;
  currencyId: string;
  name: string;
}

interface EnhancedFeaturedItem extends ProcessedStoreItem {
  skinData?: SkinData | null;
}

// TypeScript interfaces for Community Dragon skins.json
interface SkinsApiResponse {
  [key: string]: SkinData;
}

interface SkinData {
  id: number;
  contentId: string;
  isBase: boolean;
  name: string;
  skinClassification: string;
  splashPath: string;
  uncenteredSplashPath: string;
  tilePath: string;
  loadScreenPath: string;
  loadScreenVintagePath?: string;
  skinType: string;
  rarity: string;
  isLegacy: boolean;
  splashVideoPath: string | null;
  previewVideoUrl: string | null;
  collectionSplashVideoPath: string | null;
  collectionCardHoverVideoPath: string | null;
  featuresText: string | null;
  chromaPath: string | null;
  emblems: any;
  regionRarityId: number;
  rarityGemPath: string | null;
  skinLines: Array<{ id: number }> | null;
  description: string | null;
  chromas?: ChromaData[];
  questSkinInfo?: {
    tiers: {
      splashPath: string;
      uncenteredSplashPath: string;
    }[];
  };
}

interface ChromaData {
  id: number;
  name: string;
  contentId: string;
  skinClassification: string;
  chromaPath: string;
  tilePath: string;
  colors: string[];
  descriptions: Array<{ region: string; description: string }>;
  description: string;
  rarities: Array<{ region: string; rarity: number }>;
}

const timeUntil = (endDate: string) => {
  if (!endDate) {
    return "Permanent";
  }
  const end = new Date(endDate);
  if (isNaN(end.getTime())) {
    return "Permanent";
  }

  // If the year is way in the future, consider it permanent
  if (end.getFullYear() > new Date().getFullYear() + 10) {
    return "Permanent";
  }

  const now = new Date().getTime();
  const distance = end.getTime() - now;

  if (distance < 0) {
    return "Expired";
  }

  const days = Math.floor(distance / (1000 * 60 * 60 * 24));
  const hours = Math.floor(
    (distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
  );
  const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));

  // If days left, show days + hours
  if (days > 1) return `Expires in ${days} days ${hours}h`;
  if (days === 1) return `Expires in 1 day ${hours}h`;

  // If hours left, show hours + minutes
  if (hours > 1) return `Expires in ${hours} hours ${minutes}m`;
  if (hours === 1) return `Expires in 1 hour ${minutes}m`;

  // If only minutes left
  if (minutes > 1) return `Expires in ${minutes} minutes`;

  return `Expires in 1 minute`;
};

const formatFullDate = (dateString: string) => {
  if (!dateString) return "";
  const date = new Date(dateString);
  if (isNaN(date.getTime())) return "";
  return date.toLocaleString(undefined, {
    year: "numeric",
    month: "long",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  });
};

export default function MythicShopPage() {
  const router = useRouter();
  const [mythicShopData, setMythicShopData] =
    useState<ProcessedMythicStorefront | null>(null);
  const [loading, setLoading] = useState(true);
  const [bundleItem, setBundleItem] = useState<EnhancedFeaturedItem | null>(null);

  // Helper function to create skin slug
  const createSkinSlug = (skinName: string): string => {
    return skinName
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '') // Remove special characters except spaces and hyphens
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
      .replace(/^-|-$/g, '') // Remove leading/trailing hyphens
  };





  // Function to generate chroma URL with colors
  const generateChromaURL = (chromaName: string, colors: string[]): string => {
    // Create skin slug from name
    const skinSlug = chromaName
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '') // Remove special characters except spaces and hyphens
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
      .replace(/^-|-$/g, '') // Remove leading/trailing hyphens
      .trim();

    // Convert hex colors to color names and remove duplicates
    const colorNames = colors.slice(0, 2).map(color =>
      getColorDisplayName(color).toLowerCase().trim()
    );
    const uniqueColorNames = [...new Set(colorNames)]; // Remove duplicates

    // Join colors with hyphen - if both colors are the same, use only one
    const chromaColorSlug = uniqueColorNames.length === 1 ? uniqueColorNames[0] : uniqueColorNames.join('-');

    return `${skinSlug}-${chromaColorSlug}`;
  };

  // Navigation handler for mythic shop items
  const handleItemClick = (item: ProcessedStoreItem) => {
    if (item.subType === 'skin') {
      // Navigate to skin page
      const skinSlug = createSkinSlug(item.displayName);
      router.push(`/skins/${skinSlug}`);
    } else if (item.subType === 'chroma') {
      // Handle chroma navigation with colors
      if (item.chromaData && item.chromaData.colors && item.chromaData.colors.length > 0) {
        const chromaSlug = generateChromaURL(item.chromaData.name, item.chromaData.colors);
        router.push(`/skins/chromas/${chromaSlug}`);
      } else {
        // Fallback to simple slug if no chroma data
        const fallbackSlug = item.displayName
          .toLowerCase()
          .replace(/[^a-z0-9\s-]/g, '')
          .replace(/\s+/g, '-')
          .replace(/-+/g, '-')
          .replace(/^-|-$/g, '')
          .trim();
        router.push(`/skins/chromas/${fallbackSlug}`);
      }
    }
    // For other types (emote, icon, etc.), we don't navigate
  };

  // Function to find and enhance bundle item with SKIN_WITH_BORDER_ICON
  const findBundleItem = async () => {
    try {

      // Fetch raw storefront data
      const response = await fetch('https://api.loldb.info/api/store/storefronts');
      if (!response.ok) {
        return null;
      }

      const rawData: StorefrontApiResponse = await response.json();

      // Find storefront with SKIN_WITH_BORDER_ICON bundle
      const bundleStorefront = rawData.data.data.find(storefront =>
        storefront.displayMetadata?.shoppefront?.bundleType === "SKIN_WITH_BORDER_ICON" ||
        storefront.catalogEntries?.some(entry =>
          entry.displayMetadata?.shoppefront?.bundleType === "SKIN_WITH_BORDER_ICON"
        )
      );

      if (!bundleStorefront) {
        return null;
      }


      // Find the catalog entry with the bundle
      const bundleEntry = bundleStorefront.catalogEntries.find(entry =>
        entry.displayMetadata?.shoppefront?.bundleType === "SKIN_WITH_BORDER_ICON"
      );

      if (!bundleEntry || !bundleEntry.purchaseUnits?.[0]) {
        return null;
      }

      const skinItemId = bundleEntry.purchaseUnits[0].fulfillment.itemId;
      const skinName = bundleEntry.purchaseUnits[0].fulfillment.name;
      const price = bundleEntry.purchaseUnits[0].paymentOptions[0]?.payments[0]?.finalDelta || 0;


      // Fetch skin data using Community Dragon skins.json API
      const skinsResponse = await fetch('https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/skins.json');
      if (!skinsResponse.ok) {
        return null;
      }

      const skinsData: SkinsApiResponse = await skinsResponse.json();

      // Find skin by contentId (which matches the itemId from storefront)
      let skin: SkinData | undefined = Object.values(skinsData).find(
        (s: SkinData) => s.contentId === skinItemId
      );

      if (!skin) {
          Object.values(skinsData).slice(0, 5).map((s: SkinData) => ({
            contentId: s.contentId,
            name: s.name,
            id: s.id
          }))
        

        // Create fallback item without skin image
        ;
        const fallbackItem: EnhancedFeaturedItem = {
          id: bundleEntry.id,
          name: bundleEntry.name,
          displayName: skinName,
          description: bundleStorefront.displayMetadata.description,
          cost: price,
          currency: 'ME',
          imagePath: null, // No image available
          videoPath: null,
          type: 'bundle',
          subType: 'bundle',
          endTime: bundleEntry.endTime,
          skinData: null
        };

        ;
        return fallbackItem;
      }

      ;

      // Use CDN utility to construct proper Community Dragon image URL from splashPath
      // Handle tiered skins (like K/DA ALL OUT Seraphine) by checking for tiers
      let splashPath: string
      if (skin && skin.questSkinInfo?.tiers && skin.questSkinInfo.tiers.length > 0) {
        // For tiered skins, use the first tier's splash path
        const firstTier = skin.questSkinInfo.tiers[0]
        splashPath = firstTier.splashPath || firstTier.uncenteredSplashPath
      } else {
        // For regular skins, use the root level path
        splashPath = skin.splashPath
      }
      const imageUrl = transformToCDNUrl(splashPath);
      const videoUrl = skin.splashVideoPath ? transformToCDNUrl(skin.splashVideoPath) : null;
      ;
      ;

      const enhancedItem: EnhancedFeaturedItem = {
        id: bundleEntry.id,
        name: bundleEntry.name,
        displayName: skin.name || skinName,
        description: bundleStorefront.displayMetadata.description,
        cost: price,
        currency: 'ME',
        imagePath: imageUrl,
        videoPath: videoUrl,
        type: 'bundle',
        subType: 'bundle',
        endTime: bundleEntry.endTime,
        skinData: skin
      };

      return enhancedItem;

    } catch (error) {
      console.error('💥 DEBUG: Error finding bundle item:', error);
      return null;
    }
  };

  useEffect(() => {
    const fetchStorefronts = async () => {
      try {
        setLoading(true);

        // Fetch processed storefront data
        const response = await fetch("/api/store/storefronts");
        if (!response.ok) {
          throw new Error("Failed to fetch storefronts");
        }
        const data: ProcessedAPIResponse = await response.json();
        if (data.success) {
          setMythicShopData(data.data.storefront.mythicshop);


        }

        // Fetch bundle item separately
        const bundle = await findBundleItem();
        if (bundle) {
          setBundleItem(bundle);
        }



      } catch (error) {
        console.error("Failed to fetch storefront data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchStorefronts();
  }, []);

  const mythicItems = mythicShopData?.items || [];

  // Dynamically determine featured items based on rotation category and expiration dates
  let featuredItem1: ProcessedStoreItem | EnhancedFeaturedItem | undefined;
  let featuredItem2: ProcessedStoreItem | EnhancedFeaturedItem | undefined;
  let featuredItem3: ProcessedStoreItem | EnhancedFeaturedItem | undefined;

  const featuredPool = mythicItems.filter(
    (item) => item.rotationCategory === "FEATURED"
  );
  if (bundleItem) {
    const isBundleInPool = featuredPool.some((item) => item.id === bundleItem.id);
    if (!isBundleInPool) {
      featuredPool.push(bundleItem);
    }
  }

  const itemsByEndTime = featuredPool.reduce((acc, item) => {
    const endTime = item.endTime || 'permanent';
    if (!acc[endTime]) {
      acc[endTime] = [];
    }
    acc[endTime].push(item);
    return acc;
  }, {} as Record<string, (ProcessedStoreItem | EnhancedFeaturedItem)[]>);

  const groups = Object.values(itemsByEndTime);

  // Sort items within each group by price, descending
  for (const group of groups) {
    group.sort((a, b) => (b.cost || 0) - (a.cost || 0));
  }

  if (groups.length === 1) {
    // All items have same expiration, so just order by price
    [featuredItem1, featuredItem2, featuredItem3] = groups[0];
  } else if (groups.length > 1) {
    // There are different expiration dates. Find the item that is alone.
    const groupWithOne = groups.find(g => g.length === 1);
    const groupWithMultiple = groups.find(g => g.length > 1);

    if (groupWithOne && groupWithMultiple) {
        featuredItem3 = groupWithOne[0];
        [featuredItem1, featuredItem2] = groupWithMultiple;
    } else {
        // Fallback: could be multiple groups.
        // Flatten, sort by price, and take the top 3.
        const allItems = groups.flat().sort((a, b) => (b.cost || 0) - (a.cost || 0));
        [featuredItem1, featuredItem2, featuredItem3] = allItems;
    }
  }

  // Fallback for featuredItem3 to prioritize the bundle if logic fails
  if (!featuredItem3 && bundleItem) {
    featuredItem3 = bundleItem;
  } else if (!featuredItem3) {
    // This fallback might not be needed anymore, but let's keep it just in case
    featuredItem3 = mythicItems.find(
      (item) => item.name === "Mythic Shop Featured Bundle"
    );
  }


  // Get the earliest endTime from featuredItem1 and featuredItem2 for the Featured section timer
  const getFeaturedSectionEndTime = () => {
    const endTimes = [
      featuredItem1?.endTime,
      featuredItem2?.endTime
    ].filter(Boolean); // Remove null/undefined values

    if (endTimes.length === 0) return null;

    // Return the earliest endTime
    return endTimes.reduce((earliest, current) => {
      if (!earliest) return current;
      if (!current) return earliest;
      return new Date(current) < new Date(earliest) ? current : earliest;
    });
  };

  // Debug logs

  // Filter items by rotation category instead of price for more accurate results
  const biWeeklyItems = mythicItems.filter(
    (item) => item.rotationCategory === "BIWEEKLY"
  );
  const weeklyItems = mythicItems.filter(
    (item) => item.rotationCategory === "WEEKLY"
  );
  const dailyItems = mythicItems.filter(
    (item) => item.rotationCategory === "DAILY"
  );

  console.log({
    dailyItems
  })



  const renderItemCard = (item: ProcessedStoreItem) => {
    const isEmote = item.subType === "emote";
    const isIcon = item.subType === "icon";
    const isChroma = item.subType === "chroma";
    const isSkin = item.subType === "skin";
    const isWard = item.subType === "ward";
    const isClickable = isSkin || isChroma;

    return (
      <div
        key={item.id}
        className={`bg-gradient-to-br from-gray-900/60 to-gray-800/40 border border-purple-700/30 rounded-xl overflow-hidden relative group transition-all duration-500 transform hover:scale-105 ${
          isClickable
            ? 'cursor-pointer hover:border-purple-400/70 hover:shadow-lg hover:shadow-purple-500/20'
            : 'cursor-default hover:border-purple-700/30'
        }`}
        onClick={() => isClickable && handleItemClick(item)}
      >
        {isEmote || isIcon || isChroma || isWard ? (
          // Emotes, Icons, Chromas, and Ward Skins maintain their centered layout with smaller size
          <>
            <div className="h-48 flex items-center justify-center p-6">
              {isWard ? (
                // Ward skins display ward image overlaid on shadow image
                <div className="relative flex items-center justify-center">
                  {item.shadowImagePath && (
                    <Image
                      src={item.shadowImagePath}
                      alt={`${item.displayName} Shadow`}
                      width={140}
                      height={80}
                      className="object-contain opacity-70"
                    />
                  )}
                  {item.imagePath && (
                    <Image
                      src={item.imagePath}
                      alt={item.displayName}
                      width={120}
                      height={120}
                      className="object-contain absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-10"
                    />
                  )}
                </div>
              ) : (
                // Other items (emotes, icons, chromas) use single image
                item.imagePath && (
                  <Image
                    src={item.imagePath}
                    alt={item.displayName}
                    width={isEmote ? 170 : 170}
                    height={isEmote ? 170 : 170}
                    className="object-contain"
                  />
                )
              )}
            </div>
            <div className="p-4 bg-gradient-to-t from-black/90 via-black/70 to-transparent">
              <p
                className="text-white font-semibold truncate text-sm mb-2"
                title={item.displayName}
              >
                {item.displayName}
              </p>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-1 text-purple-400">
                  <MythicIcon className="h-4 w-4" />
                  <span className="font-medium">{item.cost}</span>
                </div>
                {item.subType && (
                  <span className="px-2 py-1 text-xs font-medium bg-purple-600/30 text-purple-200 rounded-full border border-purple-400/30">
                    {item.subType.charAt(0).toUpperCase() + item.subType.slice(1)}
                  </span>
                )}
              </div>
            </div>
          </>
        ) : (
          // Skins and other items use full card space
          <>
            {item.imagePath && (
              <div className="relative h-64 w-full">
                <Image
                  src={item.imagePath}
                  alt={item.displayName}
                  fill
                  className="object-cover group-hover:scale-110 transition-transform duration-700 ease-out"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent opacity-60 group-hover:opacity-40 transition-opacity duration-300" />
              </div>
            )}
            <div className="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/90 via-black/70 to-transparent">
              <p
                className="text-white font-bold text-sm mb-2 drop-shadow-lg"
                title={item.displayName}
              >
                {item.displayName}
              </p>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-1 text-purple-300">
                  <MythicIcon className="h-4 w-4" />
                  <span className="font-semibold">{item.cost}</span>
                </div>
                {item.subType && (
                  <span className="px-2 py-1 text-xs font-medium bg-purple-600/30 text-purple-200 rounded-full border border-purple-400/30">
                    {item.subType.charAt(0).toUpperCase() + item.subType.slice(1)}
                  </span>
                )}
              </div>
            </div>
          </>
        )}
      </div>
    );
  };

  if (loading) {
    return (
      <SharedLayout>
        <div className="container mx-auto px-4 sm:px-6 md:px-8 pb-8 no-top-padding max-w-full overflow-hidden">
          <div className="mb-8">
            <div className="flex items-center space-x-3 mb-4">
              <MythicIcon className="h-8 w-8 text-purple-400" />
              <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-400 to-purple-400 bg-clip-text text-transparent">
                Mythic Shop
              </h1>
            </div>
            <p className="text-gray-300 text-lg">
              Exclusive items available for Mythic Essence
            </p>
          </div>

          <div className="space-y-16">
            <section>
              <div className="flex items-center space-x-3 mb-8">
                <h2 className="text-3xl font-bold text-white">Featured</h2>
                <Clock className="h-6 w-6 text-purple-400" />
                <Skeleton className="h-6 w-40 bg-gray-700/50 rounded-full" />
              </div>
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 lg:min-h-[450px]">
                <Skeleton className="lg:col-span-1 min-h-[300px] lg:min-h-[450px] bg-gradient-to-br from-gray-900/60 to-gray-800/40 border border-purple-700/30 rounded-xl" />
                <div className="lg:col-span-2 grid grid-cols-1 lg:grid-rows-2 gap-8">
                  <Skeleton className="min-h-[300px] lg:min-h-auto bg-gradient-to-br from-gray-900/60 to-gray-800/40 border border-purple-700/30 rounded-xl" />
                  <Skeleton className="min-h-[300px] lg:min-h-auto bg-gradient-to-br from-gray-900/60 to-gray-800/40 border border-purple-700/30 rounded-xl" />
                </div>
              </div>
            </section>
            <section>
              <div className="flex items-center space-x-3 mb-8">
                <h2 className="text-3xl font-bold text-white">Bi-Weekly</h2>
                <Skeleton className="h-6 w-40 bg-gray-700/50 rounded-full" />
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
                <Skeleton className="h-64 bg-gradient-to-br from-gray-900/60 to-gray-800/40 rounded-xl" />
                <Skeleton className="h-64 bg-gradient-to-br from-gray-900/60 to-gray-800/40 rounded-xl" />
              </div>
            </section>
            <section>
              <div className="flex items-center space-x-3 mb-8">
                <h2 className="text-3xl font-bold text-white">Weekly</h2>
                <Skeleton className="h-6 w-40 bg-gray-700/50 rounded-full" />
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
                <Skeleton className="h-64 bg-gradient-to-br from-gray-900/60 to-gray-800/40 rounded-xl" />
              </div>
            </section>
            <section>
              <div className="flex items-center space-x-3 mb-8">
                <h2 className="text-3xl font-bold text-white">Daily</h2>
                <Skeleton className="h-6 w-40 bg-gray-700/50 rounded-full" />
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
                <Skeleton className="h-64 bg-gradient-to-br from-gray-900/60 to-gray-800/40 rounded-xl" />
              </div>
            </section>
          </div>
        </div>
      </SharedLayout>
    );
  }

  return (
    <SharedLayout>
      <div className="container mx-auto px-4 sm:px-6 md:px-8 pb-8 no-top-padding max-w-full overflow-hidden">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-4">
            <MythicIcon className="h-8 w-8 text-purple-400" />
            <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-400 to-purple-400 bg-clip-text text-transparent">
              Mythic Shop
            </h1>
          </div>
          <p className="text-gray-300 text-lg">
            Exclusive items available for Mythic Essence
          </p>
        </div>

        {/* Mythic Shop Categories */}
        <div className="space-y-16">
          {/* Featured Section */}
          <section>
            <div className="flex items-center space-x-3 mb-8">
              <h2 className="text-3xl font-bold text-white">Featured</h2>
              {getFeaturedSectionEndTime() && (
                <>
                  <Clock className="h-6 w-6 text-purple-400" />
                  <span className="text-purple-400 font-semibold text-lg">
                    {timeUntil(getFeaturedSectionEndTime()!)}
                  </span>
                </>
              )}
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 4xl:grid-cols-4 gap-8 lg:min-h-[450px]">
              {/* Left - Tall Vertical Grid (Featured Item 1) */}
              <div className="lg:col-span-1 4xl:col-span-1 min-h-[300px] lg:min-h-[450px] bg-gradient-to-br from-gray-900/60 to-gray-800/40 border border-purple-700/30 rounded-xl overflow-hidden relative group cursor-default hover:border-purple-700/30 transition-all duration-500 transform hover:scale-105">
                {featuredItem1 ? (
                  <>
                    {featuredItem1.imagePath && (
                      <Image
                        src={featuredItem1.imagePath}
                        alt={featuredItem1.displayName}
                        fill
                        className={`object-cover transition-opacity duration-500 ease-in-out ${
                          featuredItem1.videoPath ? "group-hover:opacity-0" : ""
                        }`}
                      />
                    )}
                    {featuredItem1.videoPath && (
                      <video
                        src={featuredItem1.videoPath}
                        autoPlay
                        loop
                        muted
                        playsInline
                        className="absolute inset-0 w-full h-full object-cover opacity-0 transition-opacity duration-500 ease-in-out group-hover:opacity-100"
                      />
                    )}
                    <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/90 via-black/70 to-transparent p-6">
                      <p
                        className="text-white font-bold text-lg mb-3 drop-shadow-lg"
                        title={featuredItem1.displayName}
                      >
                        {featuredItem1.displayName}
                      </p>
                      <div className="flex items-center space-x-2 text-purple-300">
                        <MythicIcon className="h-5 w-5" />
                        <span className="font-semibold text-lg">
                          {featuredItem1.cost}
                        </span>
                      </div>
                    </div>
                  </>
                ) : (
                  <div className="flex items-center justify-center h-full">
                    <p className="text-gray-400 text-lg">
                      Featured item not available
                    </p>
                  </div>
                )}
              </div>

              {/* Middle - Wide Vertical Grid (Featured Item 3) - Only on 4xl+ screens */}
              <div
                className={`hidden 4xl:block 4xl:col-span-2 min-h-[300px] lg:min-h-[450px] bg-gradient-to-br from-gray-900/60 to-gray-800/40 border border-purple-700/30 rounded-xl overflow-hidden relative group transition-all duration-500 transform hover:scale-105 ${
                  featuredItem3?.subType === 'skin' || featuredItem3?.subType === 'bundle'
                    ? 'cursor-pointer hover:border-purple-400/70 hover:shadow-lg hover:shadow-purple-500/20'
                    : 'cursor-default hover:border-purple-700/30'
                }`}
                onClick={() => {
                  if (featuredItem3?.subType === 'skin') {
                    const skinSlug = createSkinSlug(featuredItem3.displayName);
                    router.push(`/skins/${skinSlug}`);
                  } else if (featuredItem3?.subType === 'bundle' && bundleItem?.skinData) {
                    // For bundle items, use the skin name from the skin data
                    const skinSlug = createSkinSlug(bundleItem.skinData.name);
                    router.push(`/skins/${skinSlug}`);
                  }
                }}
              >
                {featuredItem3 ? (
                  <>
                    {featuredItem3.imagePath && (
                      <Image
                        src={featuredItem3.imagePath}
                        alt={featuredItem3.displayName}
                        fill
                        className={`object-top object-cover transition-opacity duration-500 ease-in-out ${
                          featuredItem3.videoPath ? "group-hover:opacity-0" : ""
                        }`}
                      />
                    )}
                    {featuredItem3.videoPath && (
                      <video
                        src={featuredItem3.videoPath}
                        autoPlay
                        loop
                        muted
                        playsInline
                        className="absolute inset-0 w-full h-full object-cover opacity-0 transition-opacity duration-500 ease-in-out group-hover:opacity-100"
                      />
                    )}
                    {featuredItem3.endTime && (
                      <div className="absolute top-4 right-4">
                        <span className="px-2 py-1 text-xs font-medium bg-purple-600/30 text-purple-200 rounded-full border border-purple-400/30">
                          {timeUntil(featuredItem3.endTime)}
                        </span>
                      </div>
                    )}
                    <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/90 via-black/70 to-transparent p-6">
                      <p
                        className="text-white font-bold text-lg mb-3 drop-shadow-lg"
                        title={featuredItem3.displayName}
                      >
                        {featuredItem3.displayName}
                      </p>
                      <div className="flex items-center space-x-2 text-purple-300">
                        <MythicIcon className="h-5 w-5" />
                        <span className="font-semibold text-lg">
                          {featuredItem3.cost}
                        </span>
                      </div>
                    </div>
                  </>
                ) : (
                  <div className="flex items-center justify-center h-full">
                    <p className="text-gray-400 text-lg">
                      Featured item not available
                    </p>
                  </div>
                )}
              </div>

              {/* Right - Tall Vertical Grid (Featured Item 2) - Only on 4xl+ screens */}
              <div className="hidden 4xl:block 4xl:col-span-1 min-h-[300px] lg:min-h-[450px] bg-gradient-to-br from-gray-900/60 to-gray-800/40 border border-purple-700/30 rounded-xl overflow-hidden relative group cursor-default hover:border-purple-700/30 transition-all duration-500 transform hover:scale-105">
                {featuredItem2 ? (
                  <div className="w-full h-full flex flex-col items-center justify-center p-6">
                    {featuredItem2.imagePath && (
                      <Image
                        src={featuredItem2.imagePath}
                        alt={featuredItem2.displayName}
                        width={300}
                        height={300}
                        className={`object-contain flex-grow transition-opacity duration-500 ease-in-out ${
                          featuredItem2.videoPath ? "group-hover:opacity-0" : ""
                        }`}
                      />
                    )}
                    {featuredItem2.videoPath && (
                      <video
                        src={featuredItem2.videoPath}
                        autoPlay
                        loop
                        muted
                        playsInline
                        className="absolute inset-0 w-full h-full object-cover opacity-0 transition-opacity duration-500 ease-in-out group-hover:opacity-100"
                      />
                    )}
                    <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/90 via-black/70 to-transparent p-4">
                      <p
                        className="text-white text-sm font-bold truncate mb-2 drop-shadow-lg"
                        title={featuredItem2.displayName}
                      >
                        {featuredItem2.displayName}
                      </p>
                      <div className="flex items-center space-x-2 text-purple-300">
                        <MythicIcon className="h-4 w-4" />
                        <span className="font-semibold">
                          {featuredItem2.cost}
                        </span>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-full">
                    <p className="text-gray-400 text-lg">
                      Featured item not available
                    </p>
                  </div>
                )}
              </div>

              {/* Original Right - Two Horizontal Grids (Hidden on 4xl+ screens) */}
              <div className="lg:col-span-2 4xl:hidden grid grid-cols-1 lg:grid-rows-2 gap-8">
                {/* Top Horizontal Grid (Featured Item 2) */}
                <div className="min-h-[300px] lg:min-h-auto bg-gradient-to-br from-gray-900/60 to-gray-800/40 border border-purple-700/30 rounded-xl overflow-hidden relative group cursor-default hover:border-purple-700/30 transition-all duration-500 transform hover:scale-105">
                  {featuredItem2 ? (
                    <div className="w-full h-full flex flex-col items-center justify-center p-6">
                      {featuredItem2.imagePath && (
                        <Image
                          src={featuredItem2.imagePath}
                          alt={featuredItem2.displayName}
                          width={225}
                          height={225}
                          className={`object-contain flex-grow transition-opacity duration-500 ease-in-out ${
                            featuredItem2.videoPath ? "group-hover:opacity-0" : ""
                          }`}
                        />
                      )}
                      {featuredItem2.videoPath && (
                        <video
                          src={featuredItem2.videoPath}
                          autoPlay
                          loop
                          muted
                          playsInline
                          className="absolute inset-0 w-full h-full object-cover opacity-0 transition-opacity duration-500 ease-in-out group-hover:opacity-100"
                        />
                      )}
                      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/90 via-black/70 to-transparent p-4">
                        <p
                          className="text-white text-sm font-bold truncate mb-2 drop-shadow-lg"
                          title={featuredItem2.displayName}
                        >
                          {featuredItem2.displayName}
                        </p>
                        <div className="flex items-center space-x-2 text-purple-300">
                          <MythicIcon className="h-4 w-4" />
                          <span className="font-semibold">
                            {featuredItem2.cost}
                          </span>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center h-full">
                      <p className="text-gray-400">
                        Featured item not available
                      </p>
                    </div>
                  )}
                </div>

                {/* Bottom Horizontal Grid (Featured Item 3) */}
                <div
                  className={`min-h-[300px] lg:min-h-auto bg-gradient-to-br from-gray-900/60 to-gray-800/40 border border-purple-700/30 rounded-xl overflow-hidden relative group transition-all duration-500 transform hover:scale-105 ${
                    featuredItem3?.subType === 'skin' || featuredItem3?.subType === 'bundle'
                      ? 'cursor-pointer hover:border-purple-400/70 hover:shadow-lg hover:shadow-purple-500/20'
                      : 'cursor-default hover:border-purple-700/30'
                  }`}
                  onClick={() => {
                    if (featuredItem3?.subType === 'skin') {
                      const skinSlug = createSkinSlug(featuredItem3.displayName);
                      router.push(`/skins/${skinSlug}`);
                    } else if (featuredItem3?.subType === 'bundle' && bundleItem?.skinData) {
                      // For bundle items, use the skin name from the skin data
                      const skinSlug = createSkinSlug(bundleItem.skinData.name);
                      router.push(`/skins/${skinSlug}`);
                    }
                  }}
                >
                  {featuredItem3 ? (
                    <>
                      {featuredItem3.imagePath && (
                        <Image
                          src={featuredItem3.imagePath}
                          alt={featuredItem3.displayName}
                          fill
                          className={`object-top object-cover transition-opacity duration-500 ease-in-out ${
                            featuredItem3.videoPath ? "group-hover:opacity-0" : ""
                          }`}
                        />
                      )}
                      {featuredItem3.videoPath && (
                        <video
                          src={featuredItem3.videoPath}
                          autoPlay
                          loop
                          muted
                          playsInline
                          className="absolute inset-0 w-full h-full object-cover opacity-0 transition-opacity duration-500 ease-in-out group-hover:opacity-100"
                        />
                      )}
                      {featuredItem3.endTime && (
                        <div className="absolute top-4 right-4">
                          <span className="px-2 py-1 text-xs font-medium bg-purple-600/30 text-purple-200 rounded-full border border-purple-400/30">
                            {timeUntil(featuredItem3.endTime)}
                          </span>
                        </div>
                      )}
                      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/90 via-black/70 to-transparent p-4">
                        <p
                          className="text-white text-sm font-bold truncate mb-2 drop-shadow-lg"
                          title={featuredItem3.displayName}
                        >
                          {featuredItem3.displayName}
                        </p>
                        <div className="flex items-center space-x-2 text-purple-300">
                          <MythicIcon className="h-4 w-4" />
                          <span className="font-semibold">
                            {featuredItem3.cost}
                          </span>
                        </div>
                      </div>
                    </>
                  ) : (
                    <div className="flex items-center justify-center h-full">
                      <p className="text-gray-400">
                        Featured item not available
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </section>

          {/* Bi-Weekly Section */}
          <section>
            <div className="flex items-center space-x-4 mb-8">
              <h2 className="text-3xl font-bold text-white">Bi-Weekly</h2>
              {mythicShopData?.biweekly?.endDate && (
                <>
                  <Clock className="h-6 w-6 text-purple-400" />
                  <span className="text-purple-400 font-semibold text-lg">
                    {timeUntil(mythicShopData.biweekly.endDate)}
                  </span>
                </>
              )}
            </div>
            {biWeeklyItems.length > 0 ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
                {biWeeklyItems.map(renderItemCard)}
              </div>
            ) : (
              <div className="bg-gradient-to-br from-gray-900/60 to-gray-800/40 border border-purple-700/30 rounded-xl p-16 text-center">
                <p className="text-gray-400 text-lg">
                  No bi-weekly items available
                </p>
              </div>
            )}
          </section>

          {/* Weekly Section */}
          <section>
            <div className="flex items-center space-x-4 mb-8">
              <h2 className="text-3xl font-bold text-white">Weekly</h2>
              {mythicShopData?.weekly?.endDate && (
                <>
                  <Clock className="h-6 w-6 text-purple-400" />
                  <span className="text-purple-400 font-semibold text-lg">
                    {timeUntil(mythicShopData.weekly.endDate)}
                  </span>
                </>
              )}
            </div>
            {weeklyItems.length > 0 ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
                {weeklyItems.map(renderItemCard)}
              </div>
            ) : (
              <div className="bg-gradient-to-br from-gray-900/60 to-gray-800/40 border border-purple-700/30 rounded-xl p-16 text-center">
                <p className="text-gray-400 text-lg">
                  No weekly items available
                </p>
              </div>
            )}
          </section>

          {/* Daily Section */}
          <section>
            <div className="flex items-center space-x-4 mb-8">
              <h2 className="text-3xl font-bold text-white">Daily</h2>
              {mythicShopData?.daily?.endDate && (
                <>
                  <Clock className="h-6 w-6 text-purple-400" />
                  <span className="text-purple-400 font-semibold text-lg">
                    {timeUntil(mythicShopData.daily.endDate)}
                  </span>
                </>
              )}
            </div>
            {dailyItems.length > 0 ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
                {dailyItems.map(renderItemCard)}
              </div>
            ) : (
              <div className="bg-gradient-to-br from-gray-900/60 to-gray-800/40 border border-purple-700/30 rounded-xl p-16 text-center">
                <p className="text-gray-400 text-lg">
                  No daily items available
                </p>
              </div>
            )}
          </section>
        </div>
      </div>
    </SharedLayout>
  );
}
