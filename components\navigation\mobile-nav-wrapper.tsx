"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Menu } from "lucide-react"
import dynamic from "next/dynamic"

// Dynamically import MobileNav with no SSR to prevent hydration issues
const MobileNavDynamic = dynamic(() => import("./mobile-nav"), {
  ssr: false,
  loading: () => (
    <Button
      variant="ghost"
      size="icon"
      className="md:hidden text-white hover:bg-orange-600/20"
    >
      <Menu className="h-6 w-6" />
      <span className="sr-only">Toggle navigation menu</span>
    </Button>
  ),
})

interface MobileNavWrapperProps {
  currentPath?: string
}

export default function MobileNavWrapper({ currentPath }: MobileNavWrapperProps) {
  const [isMounted, setIsMounted] = useState(false)

  useEffect(() => {
    setIsMounted(true)
  }, [])

  if (!isMounted) {
    return (
      <Button
        variant="ghost"
        size="icon"
        className="md:hidden text-white hover:bg-orange-600/20"
      >
        <Menu className="h-6 w-6" />
        <span className="sr-only">Toggle navigation menu</span>
      </Button>
    )
  }

  return <MobileNavDynamic currentPath={currentPath} />
}
