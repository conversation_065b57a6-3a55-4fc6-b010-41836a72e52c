import SkinsPageClient from "./client"
import { generateSEOMetadata } from "@/components/SEO"
import type { Metadata } from 'next'

export async function generateMetadata(): Promise<Metadata> {
  return generateSEOMetadata({
    title: 'All League of Legends Skins',
    description: 'Explore 1800+ League of Legends champion skins and cosmetics. Browse by champion, rarity, skin line, and price. Find your perfect skin on LoLDB.',
    keywords: [
      'League of Legends skins',
      'LoL skins',
      'champion skins',
      'skin database',
      'skin prices',
      'skin rarities',
      'Ultimate skins',
      'Legendary skins',
      'Epic skins',
      'skin lines',
      'cosmetics',
      'champion cosmetics'
    ],
    url: '/skins',
    canonical: 'https://loldb.info/skins',
    type: 'website',
    section: 'Skins'
  });
}

export default function SkinsPage() {
  return <SkinsPageClient />;
}
