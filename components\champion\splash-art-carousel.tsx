"use client"

import * as React from "react"
import { useState } from "react"
import Image from "next/image"
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel"
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip"

// Sample skin data structure for demo
interface SkinData {
  name: string
  price: string
  rarity: string
  image: string
  splashArt: string
  inGameImage: string
  isBase: boolean
  hasInGameImage: boolean
  chromas: any[]
  skinNum: number
}

// Sample data for demonstration
const sampleSkins: SkinData[] = [
  {
    name: "Classic",
    price: "Free",
    rarity: "Regular",
    image: "https://ddragon.leagueoflegends.com/cdn/img/champion/loading/Jinx_0.jpg",
    splashArt: "https://ddragon.leagueoflegends.com/cdn/img/champion/splash/Jinx_0.jpg",
    inGameImage: "",
    isBase: true,
    hasInGameImage: false,
    chromas: [],
    skinNum: 0
  },
  {
    name: "Mafia Jinx",
    price: "975",
    rarity: "Epic",
    image: "https://ddragon.leagueoflegends.com/cdn/img/champion/loading/Jinx_1.jpg",
    splashArt: "https://ddragon.leagueoflegends.com/cdn/img/champion/splash/Jinx_1.jpg",
    inGameImage: "",
    isBase: false,
    hasInGameImage: false,
    chromas: [],
    skinNum: 1
  },
  {
    name: "Firecracker Jinx",
    price: "1350",
    rarity: "Epic",
    image: "https://ddragon.leagueoflegends.com/cdn/img/champion/loading/Jinx_2.jpg",
    splashArt: "https://ddragon.leagueoflegends.com/cdn/img/champion/splash/Jinx_2.jpg",
    inGameImage: "",
    isBase: false,
    hasInGameImage: false,
    chromas: [],
    skinNum: 2
  },
  {
    name: "Slayer Jinx",
    price: "1350",
    rarity: "Epic",
    image: "https://ddragon.leagueoflegends.com/cdn/img/champion/loading/Jinx_3.jpg",
    splashArt: "https://ddragon.leagueoflegends.com/cdn/img/champion/splash/Jinx_3.jpg",
    inGameImage: "",
    isBase: false,
    hasInGameImage: false,
    chromas: [],
    skinNum: 3
  },
  {
    name: "Star Guardian Jinx",
    price: "1350",
    rarity: "Epic",
    image: "https://ddragon.leagueoflegends.com/cdn/img/champion/loading/Jinx_4.jpg",
    splashArt: "https://ddragon.leagueoflegends.com/cdn/img/champion/splash/Jinx_4.jpg",
    inGameImage: "",
    isBase: false,
    hasInGameImage: false,
    chromas: [],
    skinNum: 4
  }
]

// Rarity icon mapping
function getRarityIconUrl(rarity: string): string | null {
  const iconMap: { [key: string]: string } = {
    'Regular': 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/summoner-icon-rare.png',
    'Legacy': 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/icon-legacy.png',
    'Epic': 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/cn-gem-3.png',
    'Legendary': 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/cn-gem-4.png',
    'Ultimate': 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/cn-gem-5.png',
    'Mythic': 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/cn-gem-8.png',
    'Exalted': 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/cn-gem-10.png',
    'Transcendent': 'https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/rarity-gem-icons/cn-gem-11.png'
  }
  return iconMap[rarity] || null
}

// Get rarity description for tooltip
function getRarityDescription(rarity: string) {
  const descriptions: Record<string, string> = {
    'Regular': 'Standard skin quality with basic visual changes',
    'Legacy': 'Limited-time skin no longer available for purchase',
    'Epic': 'High-quality skin with new model, textures, and effects',
    'Legendary': 'Premium skin with new voice lines and animations',
    'Ultimate': 'Evolving skin that changes throughout the game',
    'Mythic': 'Exclusive skin with unique prestige elements',
    'Exalted': 'Ultra-rare skin with exceptional quality',
    'Transcendent': 'Highest tier skin with transformative features'
  }
  return descriptions[rarity] || `${rarity} rarity skin`
}

export function SplashArtCarousel() {
  const [currentSkinIndex, setCurrentSkinIndex] = useState(0)
  const skins = sampleSkins

  return (
    <div className="w-full max-w-4xl mx-auto">
      <Carousel
        opts={{
          align: "center",
          loop: true,
        }}
        className="w-full"
      >
        <CarouselContent className="-ml-2 md:-ml-4">
          {skins.map((skin, index) => (
            <CarouselItem key={index} className="pl-2 md:pl-4 md:basis-1/3 lg:basis-1/4">
              <div className="relative group">
                <button
                  onClick={() => setCurrentSkinIndex(index)}
                  className={`relative w-full aspect-[3/4] rounded-lg overflow-hidden border-2 transition-all duration-300 ${
                    index === currentSkinIndex 
                      ? 'border-cyan-400 shadow-lg shadow-cyan-400/50 scale-105' 
                      : 'border-gray-600 hover:border-cyan-300 hover:scale-102'
                  }`}
                >
                  <Image
                    src={skin.image}
                    alt={skin.name}
                    fill
                    className="object-cover object-top"
                    style={{ userSelect: 'none', pointerEvents: 'none' }}
                    draggable={false}
                  />
                  
                  {/* Skin name overlay */}
                  <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-3">
                    <h3 className="text-white text-sm font-semibold truncate">{skin.name}</h3>
                    <div className="flex items-center justify-between mt-1">
                      {skin.price === "Free" ? (
                        <span className="text-green-400 text-xs">Free</span>
                      ) : skin.price === "N/A" ? (
                        <span className="text-gray-400 text-xs">N/A</span>
                      ) : (
                        <div className="flex items-center gap-1">
                          {skin.price.includes('ME') ? (
                            <Image
                              src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/mythic.svg"
                              alt="ME"
                              width={12}
                              height={12}
                              className="w-3 h-3"
                              style={{ userSelect: 'none', pointerEvents: 'none' }}
                              draggable={false}
                            />
                          ) : (
                            <Image
                              src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg"
                              alt="RP"
                              width={12}
                              height={12}
                              className="w-3 h-3"
                              style={{ userSelect: 'none', pointerEvents: 'none' }}
                              draggable={false}
                            />
                          )}
                          <span className={`text-xs ${
                            skin.price.includes('ME') ? "text-purple-400" : "text-cyan-400"
                          }`}>
                            {skin.price.replace(' RP', '').replace(' ME', '')}
                          </span>
                        </div>
                      )}
                      <span className="text-gray-300 text-xs">{skin.rarity}</span>
                    </div>
                  </div>

                  {/* Rarity icon */}
                  {getRarityIconUrl(skin.rarity) && (
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className="absolute top-2 right-2 w-6 h-6 rounded-full bg-black/80 backdrop-blur-sm flex items-center justify-center border border-gray-600/50 cursor-help">
                          <Image
                            src={getRarityIconUrl(skin.rarity)!}
                            alt={skin.rarity}
                            width={16}
                            height={16}
                            className="w-4 h-4"
                            style={{ userSelect: 'none', pointerEvents: 'none' }}
                            draggable={false}
                          />
                        </div>
                      </TooltipTrigger>
                      <TooltipContent>
                        <div className="text-center">
                          <div className="font-semibold text-cyan-300">{skin.rarity}</div>
                          <div className="text-sm text-gray-300 mt-1">
                            {getRarityDescription(skin.rarity)}
                          </div>
                        </div>
                      </TooltipContent>
                    </Tooltip>
                  )}
                </button>
              </div>
            </CarouselItem>
          ))}
        </CarouselContent>
        
        <CarouselPrevious className="bg-slate-800/90 hover:bg-slate-700/90 border-slate-600 text-white" />
        <CarouselNext className="bg-slate-800/90 hover:bg-slate-700/90 border-slate-600 text-white" />
      </Carousel>

      {/* Current skin info display */}
      <div className="mt-6 text-center">
        <h2 className="text-2xl font-bold text-white">{skins[currentSkinIndex].name}</h2>
        <div className="flex items-center justify-center space-x-4 mt-2">
          {skins[currentSkinIndex].price === "Free" ? (
            <span className="text-green-400 font-semibold">Free</span>
          ) : skins[currentSkinIndex].price === "N/A" ? (
            <span className="text-gray-400 font-semibold">N/A</span>
          ) : (
            <div className="flex items-center gap-2">
              {skins[currentSkinIndex].price.includes('ME') ? (
                <Image
                  src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/mythic.svg"
                  alt="ME"
                  width={16}
                  height={16}
                  className="w-4 h-4"
                  style={{ userSelect: 'none', pointerEvents: 'none' }}
                  draggable={false}
                />
              ) : (
                <Image
                  src="https://raw.communitydragon.org/latest/plugins/rcp-fe-lol-static-assets/global/default/images/currency/icons/rp.svg"
                  alt="RP"
                  width={16}
                  height={16}
                  className="w-4 h-4"
                  style={{ userSelect: 'none', pointerEvents: 'none' }}
                  draggable={false}
                />
              )}
              <span className={`font-semibold ${
                skins[currentSkinIndex].price.includes('ME') ? "text-purple-400" : "text-cyan-400"
              }`}>
                {skins[currentSkinIndex].price.replace(' RP', '').replace(' ME', '')}
              </span>
            </div>
          )}
          <span className="text-gray-300">•</span>
          <span className="text-gray-300">{skins[currentSkinIndex].rarity}</span>
        </div>
      </div>
    </div>
  )
}
